<?php

namespace DreApp\Libraries;

class SettingsCrud
{
    /**
     * Get value list or array
     * @param  $config string config variable
     * @param  $list   bool   if true return list formatted as table, if not return as array
     * @return array   return list of values
     */
    public static function get(string $config, bool $list = false): array
    {
        $currentValues = service('settings')->get($config);
        if (!$list) {

            return $currentValues;
        }
        $thead = ['key', 'value'];
        $tbody = [];
        foreach ($currentValues as $key => $value) {
            $tbody[$key] = [$key, $value];
        }

        return [
            'thead' => $thead,
            'tbody' => $tbody
        ];
    }

    /**
     * Add value to config
     * @param  $config string config variable
     * @param  $value  value  to add
     * @return bool
     */
    public static function add(string $config, string $value): bool
    {
        $currentValues = service('settings')->get($config);
        if (empty($currentValues)) {
            $currentValues = [];
        }
        $newSetOfValues = array_unique(array_merge($currentValues, [strtolower(trim($value))]));
        if (!service('settings')->set($config, $newSetOfValues)) {

            return false;
        }

        return true;
    }

    /**
     * Update value
     * @param  $config string config variable
     * @param  $key    int    value key
     * @param  $value  value  to update
     * @return bool
     */
    public static function update(string $config, int $key, string $value): bool
    {
        $currentValues = service('settings')->get($config);
        if (empty($currentValues) || !isset($currentValues[$key])) {

            return false;
        }
        $value = strtolower(trim($value));
        $currentValues[$key] = $value;
        if (!service('settings')->set($config, $currentValues)) {

            return false;
        }

        return true;
    }

    /**
     * Delete value by key
     * @param  $config string config variable
     * @param  $key    int    value key
     * @return bool
     */
    public static function delete(string $config, int $key): bool
    {
        $currentValues = service('settings')->get($config);
        unset($currentValues[$key]);
        if (!service('settings')->set($config, $currentValues)) {

            return false;
        }

        return true;
    }
}
