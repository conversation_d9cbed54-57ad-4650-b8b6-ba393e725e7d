<?php

namespace DreApp\Cells;

class ContestCell
{
    /**
     * show status badge
     * @param  array $params the current status
     * @return the   badge
     */
    public function status(array $params): string
    {
        if ($params['status'] === 'open') {
            return '<span class="badge badge-success">Aberto</span>';
        }
        if ($params['status'] === 'closed') {
            return '<span class="badge badge-danger">Fechado</span>';
        }
        if ($params['status'] === 'revoked') {
            return '<span class="badge badge-warning">Revogado</span>';
        }

        return '<span class="badge badge-secondary">Sem estado</span>';
    }

    /**
     * show decision yes or no
     * @param  array    $params the decision
     * @return string
     */
    public function decision(array $params): string
    {
        if ($params['decision'] === '1') {
            return '<span class="badge badge-success">Sim</span>';
        }
        if ($params['decision'] === '0') {
            return '<span class="badge badge-danger">Não</span>';
        }

        return '';
    }
}
