<?php

if (!function_exists('excluded')) {
    /**
     * Get excluded words from a sentence string
     * @param  array   $wordList to exclude list
     * @param  string  $words    sentence string
     * @return array
     */
    function excluded(array | null $wordList = [], string $words = ''): array
    {

        if (empty($wordList) || empty($words)) {

            return [];
        }

        return array_intersect(
            array_map('mb_strtolower', $wordList),
            array_map('mb_strtolower', preg_split('/[^\p{L}\p{N}]+/u', $words, -1, PREG_SPLIT_NO_EMPTY)));
    }
}
