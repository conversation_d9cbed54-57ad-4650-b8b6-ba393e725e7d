<?php

// DASHBOARD
$routes->get('/', 'DreApp::index', ['namespace' => 'Dre<PERSON><PERSON>\Controllers', 'subdomain' => 'dre', 'filter' => 'DreApp\Filters\LoginRedirect::LoginRedirect']);

$routes->get('/release-log', 'Dashboard::releaseLog', ['namespace' => 'Admin\Controllers', 'subdomain' => 'dre', 'filter' => 'group:admin']);
$routes->get('logs', 'Dashboard::logs', ['namespace' => 'Admin\Controllers', 'subdomain' => 'dre', 'filter' => 'group:admin']);

$routes->group('dre', ['namespace' => 'DreApp\Controllers', 'subdomain' => 'dre', 'filter' => 'group:admin'], static function ($routes) {
    $routes->get('/', 'DreApp::index', ['filter' => 'DreApp\Filters\DreRedirect::DreRedirect']);
    $routes->get('index', 'DreApp::index', ['filter' => 'DreApp\Filters\DreRedirect::DreRedirect']);
    $routes->get('insert', 'DreApp::insert');
    $routes->get('update/(:num)', 'DreApp::update/$1');
    $routes->post('save', 'DreApp::save');
    $routes->get('detail/(:num)', 'DreApp::detail/$1', ['filter' => 'DreApp\Filters\LoginRedirect::LoginRedirect']);
    $routes->get('detail-public/(:num)', 'DreApp::detailPublic/$1', ['filter' => null]);
    $routes->get('interest/(:num)', 'DreApp::interest/$1', ['filter' => null]);
    $routes->get('interest-email/(:num)', 'DreApp::interestEmail/$1', ['filter' => null]);
    //send <NAME_EMAIL> about contest details
    $routes->get('send-email/(:num)', static function ($id) {
        $contest = model('\App\Models\DreItemModel')->where('id', $id)->first();
        if (empty($contest) || $contest->notified) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $attachments = model('\App\Models\DreItemDocumentModel')->where('dre_item_id', $id)->findAll();
        $emails = array_merge(['<EMAIL>'], [$contest->interest_emails]);
        $emails = explode(',', implode(',', array_filter($emails)));
        $emails = array_unique(array_map('trim', $emails));
        $emails = array_filter($emails);
        $sent = service('notification')->initialize([
            'to' => implode(',', $emails)
        ])
            ->subject('Concurso Público - ' . $contest->promoter)
            ->message(view('emails/alert-dre-detail', ['contest' => $contest, 'attachments' => $attachments]))
            ->send();

        if (!$sent) {

            return redirect()->to('dre')->with('error', 'Falha no envio do email.');
        }
        if (!model('\App\Models\DreItemModel')->where('id', $id)->set('notified', 1)->update()) {

            return redirect()->to('dre')->with('error', 'E-mail enviado mas falha em registar concurso como notificado.');
        }

        return redirect()->to('dre')->with('message', 'Email enviado com sucesso.');
    });
    $routes->group('attachments', static function ($routes) {
        $routes->get('(:num)', 'DreApp::attachments/$1', ['filter' => 'DreApp\Filters\LoginRedirect::LoginRedirect']);
        $routes->get('insert/(:num)', 'DreApp::insertAttachment/$1');
        $routes->get('update/(:num)', 'DreApp::updateAttachment/$1');
        $routes->post('save', 'DreApp::saveAttachment/$1');
        $routes->get('delete/(:num)', 'DreApp::deleteAttachment/$1');
        $routes->get('download/(:num)', 'DreApp::downloadAttachment/$1', ['filter' => null]);
    });
});

// SETTINGS
$routes->group('settings', ['namespace' => 'DreApp\Controllers', 'subdomain' => 'dre', 'filter' => 'group:admin'], static function ($routes) {
    // CRUD WORDS LIST
    $routes->group('words-list', static function ($routes) {
        $routes->get('', 'Words::index');
        $routes->get('insert', 'Words::insert');
        $routes->get('update/(:num)', 'Words::update/$1');
        $routes->post('save', 'Words::save');
        $routes->get('delete/(:num)', 'Words::delete/$1');
    });
    // CRUD EMAILS LIST
    $routes->group('emails', static function ($routes) {
        $routes->get('', 'Emails::index');
        $routes->get('insert', 'Emails::insert');
        $routes->get('update/(:num)', 'Emails::update/$1');
        $routes->post('save', 'Emails::save');
        $routes->get('delete/(:num)', 'Emails::delete/$1');
    });
});

// AUTHENTICATION
$routes->group('auth', ['namespace' => 'DreApp\Controllers', 'subdomain' => 'dre'], static function ($routes) {
    $routes->get('login', 'Auth::loginView', ['filter' => 'DreApp\Filters\HomeRedirect::HomeRedirect']);
    $routes->post('login', 'Auth::loginAction');
    $routes->get('forgot-password', 'Auth::forgotPasswordView');
    $routes->post('forgot-password', 'Auth::forgotPasswordAction');
    $routes->get('reset-password', 'Auth::resetPasswordView', ['filter' => \App\Filters\CheckPasswordToken::class]);
    $routes->post('reset-password', 'Auth::resetPasswordAction');
    $routes->get('logout', static function () {
        auth()->logout();
        return redirect()->to('auth/login')->with('confirm', 'Terminou a sessão com sucesso');
    });
});
