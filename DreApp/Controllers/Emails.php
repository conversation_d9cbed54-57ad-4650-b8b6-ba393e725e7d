<?php

namespace <PERSON><PERSON><PERSON><PERSON>\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Controllers\BaseController;
use <PERSON><PERSON><PERSON><PERSON>\Libraries\SettingsCrud;

class Emails extends BaseController
{
    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'emails';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/emails'))
            ->addCrumb('Destinatários', site_url('settings/emails'));
    }

    /**
     * Show the list of recipient emails
     * @return string
     */
    public function index(): string
    {
        $this->data['emails'] = SettingsCrud::get('Dre.emails');

        return view('dre/emails/index', $this->data);
    }

    /**
     * Insert recipient email
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/emails/insert'));

        return view('dre/emails/manage', $this->data);
    }

    /**
     * Update recipient email
     * @param  int      $key config array key
     * @return string
     */
    public function update(int $key): string
    {
        $this->data['key'] = $key;
        $this->data['email'] = SettingsCrud::get('Dre.emails')[$key];
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/emails/update/' . $key))
            ->addCrumb($this->data['email'], site_url('settings/emails/update/' . $key));

        return view('dre/emails/manage', $this->data);
    }

    /**
     * Save recipient email
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (isset($_POST['key']) && !is_null($_POST['key']) && $_POST['key'] !== '') {
            if (SettingsCrud::update('Dre.emails', $this->request->getPost()['key'], $this->request->getPost()['email'])) {

                return redirect()->back()->with('error', 'Falha ao gravar o destinatário.');
            }

            return redirect()->to('settings/emails')->with('message', 'O destinatário foi gravado com sucesso!');
        }
        if (SettingsCrud::add('Dre.emails', $this->request->getPost()['email'])) {

            return redirect()->back()->with('error', 'Falha ao gravar o destinatário.');
        }

        return redirect()->to('settings/emails')->with('message', 'O destinatário foi gravado com sucesso!');
    }

    /**
     * Delete recipient email
     * @param  int                                  $key config array key
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($key): \CodeIgniter\HTTP\RedirectResponse
    {
        if (SettingsCrud::delete('Dre.emails', $key)) {

            return redirect()->back()->with('error', 'Falha ao apagar o destinatário.');
        }

        return redirect()->to('settings/emails')->with('message', 'O destinatário foi apagado com sucesso!');
    }
}
