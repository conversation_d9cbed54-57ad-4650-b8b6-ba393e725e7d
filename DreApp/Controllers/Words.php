<?php

namespace Dr<PERSON><PERSON><PERSON>\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Controllers\BaseController;
use <PERSON><PERSON><PERSON><PERSON>\Libraries\SettingsCrud;

class Words extends BaseController
{
    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'words-list';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/words-list'))
            ->addCrumb('Dicionário de exclusão', site_url('settings/words-list'));
    }

    /**
     * Show the list of excluded words
     * @return string
     */
    public function index(): string
    {
        $this->data['words'] = SettingsCrud::get('Dre.words');

        return view('dre/words-list/index', $this->data);
    }

    /**
     * Insert excluded words view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/words-list/insert'));

        return view('dre/words-list/manage', $this->data);
    }

    /**
     * Update excluded word
     * @param  int      $key config array key
     * @return string
     */
    public function update(int $key): string
    {
        $this->data['key'] = $key;
        $this->data['word'] = SettingsCrud::get('Dre.words')[$key];
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/words-list/update' . $key))
            ->addCrumb($this->data['word'], site_url('settings/words-list/update/' . $key));

        return view('dre/words-list/manage', $this->data);
    }

    /**
     * Save excluded word
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (isset($_POST['key']) && !is_null($_POST['key']) && $_POST['key'] !== '') {
            if (SettingsCrud::update('Dre.words', $this->request->getPost()['key'], $this->request->getPost()['word'])) {

                return redirect()->back()->with('error', 'Falha ao gravar palavra.');
            }

            return redirect()->to('settings/words-list')->with('message', 'A palavra foi gravada com sucesso!');
        }
        if (SettingsCrud::add('Dre.words', $this->request->getPost()['word'])) {

            return redirect()->back()->with('error', 'Falha ao gravar palavra.');
        }

        return redirect()->to('settings/words-list')->with('message', 'A palavra foi gravada com sucesso!');
    }

    /**
     * Delete excluded word
     * @param  int                                  $key config array key
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function delete($key): \CodeIgniter\HTTP\RedirectResponse
    {
        if (SettingsCrud::delete('Dre.words', $key)) {

            return redirect()->back()->with('error', 'Falha ao apagar a palavra.');
        }

        return redirect()->to('settings/words-list')->with('message', 'A palavra foi apagada com sucesso!');
    }
}
