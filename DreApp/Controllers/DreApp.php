<?php

namespace Dre<PERSON><PERSON>\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Controllers\BaseController;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Dre<PERSON><PERSON> extends BaseController
{
    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'dre';
        $this->data['activeSubMenu'] = 'contests';
        $this->data['breadcrumbs']->addCrumb('Dre.pt', site_url('dre'));
        $this->data['currentWords'] = service('settings')->get('Dre.words');
    }

    /**
     * Show the list of Dre Contests, excluded or not
     * @return string
     */
    public function index(): string
    {
        $this->data['breadcrumbs']->addCrumb('Concursos Públicos', site_url('dre'));
        $this->data['excluded'] = $this->data['others'] = 0;
        $dreModel = model('\App\Models\DreItemModel')
            ->select('dre_items.*')
            ->orderBy('created_at', 'DESC')
            ->filter(service('request')->getGet());
        if (isset($_GET['excluded']) && $_GET['excluded'] === '1') {
            $this->data['excluded'] = 1;
            $this->data['contests'] = array_filter($dreModel->findAll(), function ($object) {

                return !empty($object->excluded);
            });
        } elseif (isset($_GET['others']) && $_GET['others'] === '1') {
            $this->data['others'] = 1;
            $this->data['contests'] = array_filter($dreModel->where('interest', false)->findAll(), function ($object) {

                return empty($object->excluded);
            });
        } else {
            $this->data['contests'] = $dreModel->where('interest', true)->findAll();
        }

        return view('dre/dre/index', $this->data);
    }

    /**
     * Manually insert a dre item
     * @return string
     */
    public function insert(): string
    {
        $this->data['companies'] = model('\App\Models\DreCompanyModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir Concurso', site_url('dre/insert'));

        return view('dre/dre/manage', $this->data);
    }

    /**
     * Show the detail of a Dre Contest
     * @return string
     */
    public function update($id): string
    {
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('dre_items.id', $id)->first();
        if (empty($this->data['contest'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['companies'] = model('\App\Models\DreCompanyModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb($this->data['contest']->title, site_url('dre/update/' . $id));

        return view('dre/dre/manage', $this->data);
    }

    /**
     * Save Contest
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\DreItemModel')->save($this->request->getPost())) {

            return redirect()->back()->with('errors', model('\App\Models\DreItemModel')->errors())->withInput();
        }
        $itemId = (!empty(model('\App\Models\DreItemModel')->getInsertID()) ? model('\App\Models\DreItemModel')->getInsertID() :
            $this->request->getPost()['id']);
        if (!model('\App\Models\DreItemCompanyModel')->where('item_id', $itemId)->delete()) {

            return redirect()->back()->with('errors', model('\App\Models\DreItemCompanyModel')->errors())->withInput();
        }
        if (!isset($_POST['companies']) || empty($_POST['companies'])) {

            return redirect()->to('dre')->with('message', 'O concurso foi gravado com sucesso!');
        }
        foreach ($_POST['companies'] as $company) {
            if (!model('\App\Models\DreItemCompanyModel')->insert(['item_id' => $itemId, 'company_id' => $company])) {
                return redirect()->back()->with('errors', model('\App\Models\DreItemCompanyModel')->errors())->withInput();
            }
        }

        return redirect()->to('dre')->with('message', 'O concurso foi gravado com sucesso!');
    }

    /**
     * Show the detail of a Dre Contest
     * @return string
     */
    public function detail($id): string
    {
        helper('word');
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('id', $id)->first();
        if (empty($this->data['contest'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb($this->data['contest']->title, site_url('dre/detail/' . $id));
        $this->data['excludedWords'] = excluded($this->data['currentWords'], $this->data['contest']->description);

        return view('dre/dre/detail', $this->data);
    }

    /**
     * Show the public detail of a Dre Contest
     * @return string
     */
    public function detailPublic($id): string
    {
        helper('word');
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('id', $id)->first();
        if (empty($this->data['contest'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['excludedWords'] = excluded($this->data['currentWords'], $this->data['contest']->description);
        $this->data['attachments'] = model('\App\Models\DreItemDocumentModel')
            ->where('dre_item_id', $id)
            ->orderBy('id', 'DESC')
            ->findAll();

        return view('dre/dre/detail-public', $this->data);
    }

    /**
     * Set interest directly for a dre contest
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function interest($id): \CodeIgniter\HTTP\RedirectResponse
    {
        $contest = model('\App\Models\DreItemModel')->where('id', $id)->first();
        if (empty($contest)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $interest = (!$contest->interest) ? 1 : 0;
        $email = null;
        if ($interest) {
            if (!auth()->user()) {
                return redirect()->to('auth/login')->with('error', 'Fazer login primeiro!');
            }
            $email = auth()->user()->email;
        }
        if (!model('\App\Models\DreItemModel')->where('id', $id)->set('interest', $interest)->update()) {

            return redirect()->back()->with('error', 'Erro ao atualizar interesse no concurso!');
        }
        $email = ($email === '<EMAIL>') ? '<EMAIL>' : $email;
        $emailArray = (!empty($contest->interest_emails)) ? explode(',', $contest->interest_emails) : [];
        if (!in_array($email, $emailArray) && $interest === 1) {
            $emailArray[] = $email;
            if (!model('\App\Models\DreItemModel')->where('id', $id)->set('interest_emails', implode(',', $emailArray))->update()) {

                return redirect()->back()->with('error', 'Erro ao registar email de interesse no concurso!');
            }
        }

        return redirect()->to('/')->with('message', 'O interesse foi atualizado no concurso com sucesso!');
    }

    /**
     * Set interest from email for a dre contest
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function interestEmail($id): \CodeIgniter\HTTP\RedirectResponse
    {
        $message = 'Interesse marcado com sucesso!';
        if (isset($_GET['email']) && empty($_GET['email'])) {
            return redirect()->to('/')->with('error', 'Erro ao marcar interesse no concurso!');
        }
        $contest = model('\App\Models\DreItemModel')->where('id', $id)->first();
        if (empty($contest)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        if ($contest->status === 'revoked') {
            return redirect()->to('/')->with('message', 'Não é possível marcar interesse num concurso revogado.');
        }
        if (!$contest->interest) {
            if (!model('\App\Models\DreItemModel')->where('id', $id)->set('interest', 1)->update()) {

                return redirect()->to('/')->with('error', 'Erro ao marcar interesse no concurso!');
            }
        }
        $emailArray = (!empty($contest->interest_emails)) ? explode(',', $contest->interest_emails) : [];
        $email = ($_GET['email'] === '<EMAIL>') ? '<EMAIL>' : $_GET['email'];
        if (!in_array($email, $emailArray)) {
            $emailArray[] = $email;
            if (!model('\App\Models\DreItemModel')->where('id', $id)->set('interest_emails', implode(',', $emailArray))->update()) {

                return redirect()->to('/')->with('error', 'Erro ao registar email de interesse no concurso!');
            }
            if ($contest->notified) {
                $attachments = model('\App\Models\DreItemDocumentModel')->where('dre_item_id', $id)->findAll();
                $sent = service('notification')->initialize(['to' => $_GET['email']])
                    ->subject('Concurso Público - ' . $contest->promoter)
                    ->message(view('emails/alert-dre-detail', ['contest' => $contest, 'attachments' => $attachments]))
                    ->send();
                if (!$sent) {

                    return redirect()->to('/')->with('error', 'Falha no envio do email.');
                }
                $message = 'Interesse marcado com sucesso! O detalhe do concurso foi enviado para o seu e-mail.';
            }
        }

        return redirect()->to('/')->with('message', $message);
    }

    /**
     * List all attachments in a Dre contest
     * @param  int    $contestId The selected contest
     * @return string The view with all the contest's attachments
     */
    public function attachments($contestId): string
    {
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('id', $contestId)->first();
        if (empty($this->data['contest'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['attachments'] = model('\App\Models\DreItemDocumentModel')
            ->where('dre_item_id', $contestId)
            ->orderBy('id', 'DESC')
            ->findAll();
        $this->data['breadcrumbs']->addCrumb($this->data['contest']->title, site_url('dre/update/' . $contestId))
            ->addCrumb('Anexos', site_url('dre/attachments/' . $contestId));

        return view('dre/dre/attachments', $this->data);
    }

    /**
     * Insert Attachment View
     * @param  int    $id    The selected contest
     * @return string Insert attachment form
     */
    public function insertAttachment(int $contestId): string
    {
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('id', $contestId)->first();
        if (empty($this->data['contest'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb($this->data['contest']->title, site_url('dre/update/' . $contestId))
            ->addCrumb('Anexos', site_url('dre/attachments/' . $contestId))
            ->addCrumb('Inserir', site_url('dre/attachments/insert' . $contestId));

        return view('dre/dre/attachments-manage', $this->data);
    }

    /**
     * Save Contest attachment
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function saveAttachment(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!isset($_POST['dre_item_id'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        if (!model('\App\Models\DreItemDocumentModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\DreItemDocumentModel')->errors())->withInput();
        }

        return redirect()->to('dre/attachments/' . $_POST['dre_item_id'])->with('message', 'O anexo foi gravado com sucesso!');
    }

    /**
     * Update Attachment View
     * @param  int    $id    The selected attachment
     * @return string Update attachment form
     */
    public function updateAttachment(int $id): string
    {
        $this->data['attachment'] = model('\App\Models\DreItemDocumentModel')->where('id', $id)->first();
        if (empty($this->data['attachment'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['contest'] = model('\App\Models\DreItemModel')->where('id', $this->data['attachment']->dre_item_id)->first();
        $this->data['breadcrumbs']->addCrumb($this->data['contest']->title, site_url('dre/update/' . $this->data['contest']->id))
            ->addCrumb('Anexos', site_url('dre/attachments/' . $this->data['contest']->id))
            ->addCrumb('Editar', site_url('dre/attachments/update/' . $this->data['attachment']->id))
            ->addCrumb($this->data['attachment']->name, site_url('dre/attachments/update/' . $this->data['attachment']->id));

        return view('dre/dre/attachments-manage', $this->data);
    }

    /**
     * Delete Attachment
     * @param  int                                  $id The selected attachment
     * @return \CodeIgniter\HTTP\RedirectResponse
     */

    public function deleteAttachment(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\DreItemDocumentModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o anexo.');
        }

        return redirect()->back()->with('confirm', 'O anexo foi apagado com sucesso!');
    }

    /**
     * Download a attachment
     * @param  int                                 $id The selected attachment
     * @return CodeIgniter\HTTP\DownloadResponse
     */
    public function downloadAttachment(int $id): \CodeIgniter\HTTP\DownloadResponse
    {
        $this->data['attachment'] = model('\App\Models\DreItemDocumentModel')->where('id', $id)->first();
        if (empty($this->data['attachment'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $filepath = FCPATH . '../public/uploads/dre/attachments/' . $this->data['attachment']->local_document;
        if (!file_exists($filepath)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->response->download($filepath, null);
    }

}
