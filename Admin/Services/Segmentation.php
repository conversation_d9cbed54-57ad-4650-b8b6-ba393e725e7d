<?php

namespace Admin\Services;

class Segmentation
{
    /**
     * City Model (geo_cities)
     * @var object
     */
    protected object $city;
    /**
     * Program Extra Model (nt_notices_national_extra)
     * @var object
     */
    protected object $programExtra;
    /**
     * Program City Model (nt_notices_national_extra_cities)
     * @var object
     */
    protected object $programCity;
    /**
     * Program Cim Model (nt_notices_national_extra_cims)
     * @var object
     */
    protected object $programCim;
    /**
     * assigns filter name to the name of the respective column in the geo_cities table
     * @var array
     */
    protected array $filterToColumn = [
        'districts' => 'district_id',
        'cities' => 'id',
        'nut_1' => 'nut_1',
        'nut_2' => 'nut_2',
        'nut_3' => 'nut_3',
        'cim' => 'cim',
    ];

    /**
     * Construct the segmentation service
     *  \App\Models\CityModel $cityModel,
     *  \App\Models\NoticiesNationalExtraModel $programExtraModel,
     *  \App\Models\ProgramCityModel $programCityModel
     */
    public function __construct(
        \App\Models\CityModel $cityModel,
        \App\Models\NoticiesNationalExtraModel $programExtraModel,
        \App\Models\NoticiesNationalExtraCityModel $programCityModel,
        \App\Models\NoticiesNationalExtraCimModel $programCimModel,
    ) {
        $this->city = $cityModel;
        $this->programExtra = $programExtraModel;
        $this->programCity = $programCityModel;
        $this->programCim = $programCimModel;
    }

    /**
     * Segment by filter type and program
     * @param  int       $programId
     * @param  string    $filter
     * @param  array     $results
     * @return boolean
     */
    public function segmentByProgram(int $programId, string $segmentationType, array $results): bool
    {
        if (!$results || !$segmentationType) {
            return false;
        }

        // get cities if filter is district, nut_1, nut_2 or nut_3
        if ($segmentationType !== 'cities' && $segmentationType !== 'cims') {
            $results = array_column($this->city->select('id')->whereIn($this->filterToColumn[$segmentationType], $results)->findAll(), 'id');
        }

        $data = [
            'id' => $programId,
            'table' => ($segmentationType === 'cims') ? 'programCim' : 'programCity',
            'column' => ($segmentationType === 'cims') ? 'cim_id' : 'city_id',
            'results' => $results,
        ];
        if (!$this->saveSegmentation($data)) {
            return false;
        }

        // update segmentation in the national_extra table
        if (!$this->programExtra->update($programId, [
            'segmentation_type' => $segmentationType,
            'cim' => ($segmentationType === 'cims') ? true : false,
        ])) {
            return false;
        }

        return true;
    }

    /**
     * Get segmentation by program
     * @param  object $program
     * @return array  result data
     */
    public function getByProgram(object $program): array
    {
        $segmentationType = $program->segmentation_type;

        if (!$segmentationType && !$program->cim) {
            return [];
        }

        if ($program->cim) {
            return array_column($this->programCim->select('cim_id')
                    ->where('extra_id', $program->id)
                    ->findAll(), 'cim_id');
        } else if ($segmentationType === 'districts') {
            return array_column($this->programCity->distinct('geo_districts.id')->select('geo_districts.id')
                    ->join('geo_cities', 'geo_cities.id = nt_notices_national_extra_cities.city_id')
                    ->join('geo_districts', 'geo_districts.id = geo_cities.district_id')
                    ->where('nt_notices_national_extra_cities.extra_id', $program->id)
                    ->findAll(), 'id');
        } else if (str_contains($segmentationType, 'nut')) {
            return array_column($this->programCity->distinct('geo_cities.' . $segmentationType)
                    ->select($segmentationType)
                    ->join('geo_cities', 'geo_cities.id = nt_notices_national_extra_cities.city_id')
                    ->where('nt_notices_national_extra_cities.extra_id', $program->id)
                    ->findAll(), $segmentationType);
        }

        // cities by default
        return array_column($this->programCity->select('city_id')
                ->where('extra_id', $program->id)
                ->findAll(), 'city_id');
    }

    /**
     * Save segmentation by type
     * @param  array $data
     * @return array result data
     */
    private function saveSegmentation(array $data)
    {
        // delete all records before save changes
        $this->{$data['table']}->where(['extra_id' => $data['id']])->delete();

        foreach ($data['results'] as $result) {
            if (!$this->{$data['table']}->save(['extra_id' => $data['id'], $data['column'] => $result])) {
                return false;
            }
        }

        return true;
    }
}
