<?php

namespace Admin\Services;

class PrrSegmentation
{
    /**
     * City Model (geo_cities)
     * @var object
     */
    protected object $city;
    /**
     * Program Extra Model (nt_notices_prr)
     * @var object
     */
    protected object $noticeModel;
    /**
     * Program City Model (nt_notices_prr_cities)
     * @var object
     */
    protected object $noticeCityModel;
    /**
     * Program Cim Model (nt_notices_prr_cims)
     * @var object
     */
    protected object $noticeCimModel;
    /**
     * assigns filter name to the name of the respective column in the geo_cities table
     * @var array
     */
    protected array $filterToColumn = [
        'districts' => 'district_id',
        'cities' => 'id',
        'nut_1' => 'nut_1',
        'nut_2' => 'nut_2',
        'nut_3' => 'nut_3',
        'cim' => 'cim',
    ];

    /**
     * Construct the PRR Segmentation service
     *  \App\Models\CityModel $cityModel,
     *  \App\Models\NoticePrrModel $noticeModel,
     *  \App\Models\NoticePrrCityModel $noticeCityModel
     *  \App\Models\NoticePrrCimModel $noticeCimModel
     */
    public function __construct(
        \App\Models\CityModel $cityModel,
        \App\Models\NoticePrrModel $noticeModel,
        \App\Models\NoticePrrCityModel $noticeCityModel,
        \App\Models\NoticePrrCimModel $noticeCimModel,
    ) {
        $this->city = $cityModel;
        $this->noticeModel = $noticeModel;
        $this->noticeCityModel = $noticeCityModel;
        $this->noticeCimModel = $noticeCimModel;
    }

    /**
     * Segment by filter type and Notice
     * @param  int       $noticeId
     * @param  string    $filter
     * @param  array     $results
     * @return boolean
     */
    public function segmentByNotice(int $noticeId, string $segmentationType, array $results): bool
    {
        if (!$results || !$segmentationType) {
            return false;
        }

        // get cities if filter is district, nut_1, nut_2 or nut_3
        if ($segmentationType !== 'cities' && $segmentationType !== 'cims') {
            $results = array_column($this->city->select('id')->whereIn($this->filterToColumn[$segmentationType], $results)->findAll(), 'id');
        }

        $data = [
            'id' => $noticeId,
            'table' => ($segmentationType === 'cims') ? 'noticeCimModel' : 'noticeCityModel',
            'column' => ($segmentationType === 'cims') ? 'cim_id' : 'city_id',
            'results' => $results,
        ];
        if (!$this->saveSegmentation($data)) {
            return false;
        }

        // update segmentation in the national_extra table
        if (!$this->noticeModel->update($noticeId, [
            'segmentation_type' => $segmentationType,
            'cim' => ($segmentationType === 'cims') ? true : false,
        ])) {
            return false;
        }

        return true;
    }

    /**
     * Get segmentation by notice
     * @param  object $notice
     * @return array  result data
     */
    public function getByNotice(object $notice): array
    {
        $segmentationType = $notice->segmentation_type;

        if (!$segmentationType && !$notice->cim) {
            return [];
        }

        if ($notice->cim) {
            return array_column($this->noticeCimModel->select('cim_id')
                    ->where('notice_id', $notice->id)
                    ->findAll(), 'cim_id');
        } else if ($segmentationType === 'districts') {
            return array_column($this->noticeCityModel->distinct('geo_districts.id')->select('geo_districts.id')
                    ->join('geo_cities', 'geo_cities.id = nt_notices_prr_cities.city_id')
                    ->join('geo_districts', 'geo_districts.id = geo_cities.district_id')
                    ->where('nt_notices_prr_cities.notice_id', $notice->id)
                    ->findAll(), 'id');
        } else if (str_contains($segmentationType, 'nut')) {
            return array_column($this->noticeCityModel->distinct('geo_cities.' . $segmentationType)
                    ->select($segmentationType)
                    ->join('geo_cities', 'geo_cities.id = nt_notices_prr_cities.city_id')
                    ->where('nt_notices_prr_cities.notice_id', $notice->id)
                    ->findAll(), $segmentationType);
        }

        // cities by default
        return array_column($this->noticeCityModel->select('city_id')
                ->where('notice_id', $notice->id)
                ->findAll(), 'city_id');
    }

    /**
     * Save segmentation by type
     * @param  array $data
     * @return array result data
     */
    private function saveSegmentation(array $data): bool
    {
        // delete all records before save changes
        $this->{$data['table']}->where(['notice_id' => $data['id']])->delete();

        foreach ($data['results'] as $result) {
            if (!$this->{$data['table']}->save(['notice_id' => $data['id'], $data['column'] => $result])) {
                return false;
            }
        }

        return true;
    }
}
