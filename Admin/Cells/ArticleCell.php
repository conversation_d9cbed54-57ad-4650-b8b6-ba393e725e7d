<?php

namespace Admin\Cells;

class ArticleCell
{
    /**
     * show status
     * @param  array $params the current status
     * @return the   badge
     */
    public function status(array $params): string
    {
        if (strtotime(date('Y-m-d H:i:s')) > strtotime($params['schedule'])) {
            return '<i class="fa-duotone fa-check"></i> Publicado';
        }

        return '<i class="fa-duotone fa-clock"></i> Agendado';
    }

    /**
     * show tags
     * @param  array $params the current tags
     * @return the   badge
     */
    public function tags(array $params): string
    {
        $arrayTags = explode(',', $params['tags']);
        $html = '<ul class="tags-ul border-0 pt-0">';
        foreach ($arrayTags as $tag) {
            $html .= '<li>' . $tag . '</li>';
        }
        $html .= '</ul>';

        return $html;
    }
}
