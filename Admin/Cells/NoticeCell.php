<?php

namespace Admin\Cells;

class NoticeCell
{
    /**
     * show status badge
     * @param  array $params the current status
     * @return the   badge
     */
    public function status(array $params): string
    {
        if ($params['status'] === 'done') {
            return '<span class="badge badge-success">Indexado</span>';
        } else if ($params['status'] === 'active') {
            return '<span class="badge badge-success">Publico</span>';
        } else if ($params['status'] === 'inactive') {
            return '<span class="badge badge-danger">Inativo</span>';
        }

        return '<span class="badge badge-danger">Não indexado</span>';
    }

    /**
     * show local badge
     * @param  array $params the current local status
     * @return the   badge
     */
    public function local(array $params): string
    {
        if ($params['local'] === '1') {
            return '<span class="badge badge-success">Disponível</span>';
        }

        return '<span class="badge badge-danger">Indisponivel</span>';
    }

    /**
     * show parsed badge
     * @param  array $params the current parsed status
     * @return the   badge
     */
    public function parsed(array $params): string
    {
        if ($params['parsed'] === '1') {
            return '<span class="badge badge-success">Indexado</span>';
        }

        return '<span class="badge badge-danger">Não indexado</span>';
    }

    /**
     * Force download a local document or just show the name
     * @param  array    $params The document object
     * @return string
     */
    public function download(array $params): string
    {
        if ($params['document']->local_document) {
            return '<a href="' . site_url('notices/national/documents/download/' . $params['document']->id) . '">' . $params['document']->documentoDesignacao . '</a>';
        }

        return $params['document']->local_document;
    }
}
