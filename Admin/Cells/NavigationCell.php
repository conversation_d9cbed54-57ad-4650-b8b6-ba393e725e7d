<?php

namespace Admin\Cells;

class NavigationCell
{
    /**
     * show Pending users status badge
     * @param  array $params
     * @return the   badge
     */
    public function pendingUsers(array $params): string
    {
        $pendingUsers = count(auth()->getProvider()->getUserDetails('company', null, ['user_details.start_date' => null]));

        if ($pendingUsers > 0) {
            return '<span class="badge badge-info right">' . $pendingUsers . '</span>';
        }

        return '';
    }

}
