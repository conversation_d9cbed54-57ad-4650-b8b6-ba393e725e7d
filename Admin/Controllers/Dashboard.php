<?php

namespace Admin\Controllers;

use CILogViewer\CILogViewer;
use DateTime;

class Dashboard extends BaseController
{
    /**
     * Show cards with relevant info for admin
     * @todo handle europe notices
     * @return string
     */
    public function index(): string
    {
        // pending companies
        $this->data['companies'] = count(auth()->getProvider()->getUserDetails('company', null));
        $this->data['pendingCompanies'] = count(auth()->getProvider()->getUserDetails(
            'company', null, ['user_details.start_date' => null]
        ));
        // scheduled articles
        $this->data['articles'] = model('\App\Models\ArticleModel')->countAllResults();
        $this->data['scheduledArticles'] = model('\App\Models\ArticleModel')->where('schedule >=', date('Y-m-d H:i:s'))->countAllResults();
        // inactive links
        $this->data['links'] = model('\App\Models\CrawlerLinkModel')->countAllResults();
        $this->data['inactiveLinks'] = model('\App\Models\CrawlerLinkModel')->where('status', 'todo')->countAllResults();
        // inactive prenotices
        $this->data['preNotices'] = model('\App\Models\PreNoticeModel')->countAllResults();
        $this->data['inactivePreNotices'] = model('\App\Models\PreNoticeModel')->where('status', 'inactive')->countAllResults();
        // inactive national notices
        $this->data['nationalNotices'] = model('\App\Models\NoticeNationalModel')->countAllResults();
        $this->data['inactiveNationalNotices'] = model('\App\Models\NoticeNationalModel')->where('status', 'inactive')->countAllResults();
        // Inactive Eropean Notices
        $this->data['europeanNotices'] = model('\App\Models\NoticeEuropeModel')->countAllResults();
        $this->data['activeEuropeanNotices'] = model('\App\Models\NoticeEuropeModel')->where('end_date >=', date('Y-m-d'))->countAllResults();
        // PRR
        $this->data['prrNotices'] = model('\App\Models\NoticePrrModel')->countAllResults();
        $this->data['activePrrNotices'] = model('\App\Models\NoticePrrModel')->where('status', 'active')->countAllResults();

        return view('admin/dashboard/index', $this->data);
    }

    /**
     * Show release log
     * @return string
     */
    public function releaseLog(): string
    {
        return view('admin/dashboard/releaselog', $this->data);
    }

    /**
     * Browse the logs diretly in the app
     */
    public function logs(): string
    {
        $logViewer = new CILogViewer();

        return $logViewer->showLogs();
    }
}
