<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Cims extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'cims';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('CIMS', site_url('settings/cims'));
    }

    /**
     * Show the list of CIMS
     * @return string
     */
    public function index(): string
    {
        // Get all CIMS
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();

        return view('admin/cims/index', $this->data);
    }

    /**
     * Insert sector view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/cims/insert'));

        return view('admin/cims/manage', $this->data);
    }

    /**
     * Save CIM
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CimModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\CimModel')->errors())->withInput();
        }

        return redirect()->to('settings/cims')->with('message', 'A CIM foi gravada com sucesso!');
    }

    /**
     * Update CIMS view
     * @param  int      $id The selected CIMS
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['cim'] = model('\App\Models\CimModel')->where('id', $id)->first();
        if (empty($this->data['cim'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/cims/update' . $id))
            ->addCrumb($this->data['cim']->name, site_url('settings/cims/update/' . $id));

        return view('admin/cims/manage', $this->data);
    }

    /**
     * Delete CIM
     * @param  int    $id        The selected CIM
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CimModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a CIM');
        }

        return redirect()->back()->with('confirm', 'A CIM foi apagada com sucesso.');
    }
}
