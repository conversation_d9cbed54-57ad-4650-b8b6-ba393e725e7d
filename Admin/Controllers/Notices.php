<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;
use \NumberFormatter;

class Notices extends BaseController
{

    /**
     * Load helpers
     * @var array
     */
    public $helpers = ['text'];

    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'notices';
        $this->data['breadcrumbs']->addCrumb('Avisos', site_url('notices/index'));
        $this->data['formatter'] = new NumberFormatter('pt_PT', NumberFormatter::CURRENCY);
    }

    /**
     * Show the list of links that will be indexed
     * @return string
     */
    public function index(): string
    {
        $this->data['activeSubMenu'] = 'links';
        $this->data['breadcrumbs']->addCrumb('Indexamento', site_url('notices/index'));
        $this->data['links'] = model('\App\Models\CrawlerLinkModel')
            ->orderBy('created_at', 'DESC')->findAll();

        return view('admin/notices/index', $this->data);
    }

    /**
     * Page with all the info about a notice for a global overview
     * @param  int    $id The selected notice
     * @return string The view with all the data
     */
    public function info(int $id): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb('Resumo', '#')
            ->addCrumb($this->data['notice']->designacaoPT, site_url('notices/national/info/' . $id));
        $this->data['documents'] = model('\App\Models\NoticiesNationalDocumentModel')
            ->where('notice_id', $id)
            ->orderBy('id', 'DESC')
            ->findAll();
        $this->data['texts'] = model('\App\Models\NoticeNationalTextModel')
            ->where('notice_id', $id)
            ->orderBy('document_id', 'ASC')
            ->findAll();
        $this->data['programs'] = model('\App\Models\NoticiesNationalExtraModel')
            ->where('notice_id', $id)
            ->orderBy('programaOperacionalDesignacao', 'ASC')
            ->findAll();
        foreach ($this->data['programs'] as $p => $program) {
            $this->data['programs'][$p]->thematics = array_column(model('\App\Models\NoticeThematicsModel')->where('extra_id', $program->id)->asArray()->findAll(), 'thematic_id');
            $this->data['programs'][$p]->sectors = array_column(model('\App\Models\NoticeSectorsModel')->where('extra_id', $program->id)->asArray()->findAll(), 'sector_id');
            $this->data['programs'][$p]->types = array_column(model('\App\Models\NoticeTypesModel')->where('extra_id', $program->id)->asArray()->findAll(), 'type_id');
        }

        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['nuts1'] = model('\App\Models\CityModel')->distinct('nut_1')->select('nut_1 AS nut')
            ->orderBy('nut_1')
            ->findAll();
        $this->data['nuts2'] = model('\App\Models\CityModel')->distinct('nut_2')->select('nut_2 AS nut')
            ->orderBy('nut_2')
            ->findAll();
        $this->data['nuts3'] = model('\App\Models\CityModel')->distinct('nut_3')->select('nut_3 AS nut')
            ->orderBy('nut_3')
            ->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();

        foreach ($this->data['programs'] as $key => $program) {
            $this->data['programs'][$key]->segmentation = service('segmentation')->getByProgram($program);
        }

        return view('admin/notices/info', $this->data);
    }

    /**
     * Show Europe links
     * @return string The table with all the notices for Pre-notices
     */
    public function preNotice(): string
    {
        $this->data['activeSubMenu'] = 'pre-notice';
        $this->data['breadcrumbs']->addCrumb('Pré-avisos', site_url('notices/pre-notice'));
        $this->data['notices'] = model('\App\Models\PreNoticeModel')
            ->orderBy('created_at', 'DESC')->findAll();

        return view('admin/notices/pre-notices', $this->data);
    }

    /**
     * Show the update pre-notice form
     * @param  int    $id The selected pre-notice notice
     * @return string The update form
     */
    public function updatePreNotice(int $id): string
    {
        $this->data['activeSubMenu'] = 'pre-notice';
        $this->data['notice'] = model('\App\Models\PreNoticeModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['breadcrumbs']->addCrumb('Pré-avisos', site_url('notices/pre-notice'))
            ->addCrumb($this->data['notice']->title, site_url('notices/pre-notice/update/' . $id));

        return view('admin/notices/update-pre-notice', $this->data);
    }

    /**
     * Save pre-notice data (we'll probably add more fields once other pick up the project)
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function savePreNotice(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\PreNoticeModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\PreNoticeModel')->errors());
        }

        return redirect()->to('notices/pre-notice')->with('confirm', 'O pre-aviso gravado com sucesso');
    }

    /**
     * Delete indexed content from pre-notice
     * @param  int        $id id
     * @return redirect
     */
    public function deletePreNotice(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\PreNoticeModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o pre-aviso.');
        }

        return redirect()->back()->with('confirm', 'O aviso indexado de portaldosfundoseuropeus.pt foi apagado com sucesso.');
    }

    /**
     * Show all the indexed data from the portugal2030.pt
     * @return string The view
     */
    public function national(): string
    {
        $noticeModel = model('\App\Models\NoticeNationalModel');
        $this->data['archive'] = false;
        if (isset($_GET['archive'])) {
            $this->data['archive'] = true;
            $noticeModel->where('dataFim <', date('Y-m-d'));
        } else {
            $noticeModel->where('dataFim >=', date('Y-m-d'));
        }
        $this->data['archive'] = (isset($_GET['archive'])) ? true : false;
        $this->data['activeSubMenu'] = 'national';
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'));
        $this->data['notices'] = $noticeModel->orderBy('dataInicio', 'DESC')->findAll();

        return view('admin/notices/national', $this->data);
    }

    /**
     * Update a national notice
     * @param  int    $id  The selected notice
     * @return string Show the view
     */
    public function updateNational(int $id): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb($this->data['notice']->designacaoPT, site_url('notices/national/update/' . $id));

        return view('admin/notices/update-national', $this->data);
    }

    /**
     * Save a given notice
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function saveNational(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeNationalModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticeNationalModel')->errors());
        }
        return redirect()->to('notices/national')->with('message', 'o seu aviso foi gravado com sucesso.');
    }

    /**
     * Save global status on all the items
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function globalSaveNational(): \CodeIgniter\HTTP\RedirectResponse
    {
        $noticeId = $this->request->getPost('id');
        $postData = $this->request->getPost();

        // Unpublish everything
        model('\App\Models\NoticiesNationalDocumentModel')
            ->whereIn('notice_id', [$noticeId])
            ->set(['status' => 'inactive'])
            ->skipValidation(true)
            ->update();
        model('\App\Models\NoticeNationalTextModel')
            ->whereIn('notice_id', [$noticeId])
            ->set(['status' => 'inactive'])
            ->skipValidation(true)
            ->update();
        model('\App\Models\NoticiesNationalExtraModel')
            ->whereIn('notice_id', [$noticeId])
            ->set(['status' => 'inactive'])
            ->skipValidation(true)
            ->update();
        model('\App\Models\NoticeNationalModel')->update($noticeId, ['status' => 'inactive']);

        // Set new status on everything
        if (isset($_POST['documents']) && !empty($_POST['documents'])) {
            model('\App\Models\NoticiesNationalDocumentModel')
                ->whereIn('id', array_keys($this->request->getPost('documents')))
                ->set(['status' => 'active'])
                ->skipValidation(true)
                ->update();
        }
        if (isset($_POST['texts']) && !empty($_POST['texts'])) {
            model('\App\Models\NoticeNationalTextModel')
                ->whereIn('id', array_keys($this->request->getPost('texts')))
                ->set(['status' => 'active'])
                ->skipValidation(true)
                ->update();
        }
        if (isset($_POST['programs']) && !empty($_POST['programs'])) {
            foreach ($this->request->getPost('programs') as $programId => $values) {
                if (isset($values['status'])) {
                    model('\App\Models\NoticiesNationalExtraModel')
                        ->where('id', $programId)->set(['status' => 'active'])
                        ->skipValidation(true)
                        ->update();
                }
                if (isset($values['thematics'])) {
                    model('\App\Models\NoticeThematicsModel')->where('extra_id', $programId)->delete();
                    foreach ($values['thematics'] as $tId) {
                        model('\App\Models\NoticeThematicsModel')->insert(['extra_id' => $programId, 'thematic_id' => $tId]);
                    }
                }
                if (isset($values['types'])) {
                    model('\App\Models\NoticeTypesModel')->where('extra_id', $programId)->delete();
                    foreach ($values['types'] as $tyId) {
                        model('\App\Models\NoticeTypesModel')->insert(['extra_id' => $programId, 'type_id' => $tyId]);
                    }
                }
                if (isset($values['sectors'])) {
                    model('\App\Models\NoticeSectorsModel')->where('extra_id', $programId)->delete();
                    foreach ($values['sectors'] as $sId) {
                        model('\App\Models\NoticeSectorsModel')->insert(['extra_id' => $programId, 'sector_id' => $sId]);
                    }
                }
            }
            foreach ($postData['programs'] as $programId => $values) {
                service('segmentation')->segmentByProgram(
                    $programId,
                    $values['filterType'] ?? null,
                    $postData[$values['filterType']][$programId] ?? []
                );
            }
        }
        if (isset($_POST['notice'])) {
            model('\App\Models\NoticeNationalModel')->update($noticeId, ['status' => 'active']);
        }

        return redirect()->back()->with('message', 'O seu aviso foi atualizado com sucesso!');
    }

    /**
     * Delete a national notice
     * @param  int                                  $id The selected notice
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function deleteNational(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeNationalModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o avisos selecionado.');
        }

        return redirect()->back()->with('confirm', 'O aviso portugal2030 foi apagado com sucesso.');
    }

    /**
     * Show all documents associated to a selected notice
     * @param  int  $noticeId The selected notice
     * @return Show the list of documents
     */
    public function documentsNational(int $noticeId): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $noticeId)->first();
        if (empty($this->data['notice'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $noticeId))
            ->addCrumb('Documentos', site_url('notices/national/documents/' . $noticeId));
        $this->data['documents'] = model('\App\Models\NoticiesNationalDocumentModel')
            ->where('notice_id', $noticeId)
            ->orderBy('documentoData', 'DESC')
            ->findAll();

        return view('admin/notices/documents-national', $this->data);
    }

    /**
     * Show form to update the document name
     * @param  int      $documentId The selected document
     * @return string
     */
    public function documentsUpdate(int $documentId): string
    {
        $this->data['document'] = model('\App\Models\NoticiesNationalDocumentModel')->where('id', $documentId)->first();
        if (empty($this->data['document'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')
            ->where('id', $this->data['document']->notice_id)
            ->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $this->data['document']->notice_id))
            ->addCrumb('Documentos', site_url('notices/national/documents/' . $this->data['document']->notice_id))
            ->addCrumb('Editar', site_url('notices/national/documents/update/' . $this->data['document']->id))
            ->addCrumb($this->data['document']->tipoDocumentoDesignacao, site_url('notices/national/documents/update' . $this->data['document']->id));

        return view('admin/notices/documents-update', $this->data);
    }

    /**
     * Set new name for a document
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function documentsSave()
    {
        if (!model('\App\Models\NoticiesNationalDocumentModel')->skipValidation()->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticiesNationalDocumentModel')->errors());
        }

        return redirect()->back()->with('confirm', 'O documento foi gravado com sucesso');
    }

    /**
     * Download a local document
     * @param  int                                 $id The selected document
     * @return CodeIgniter\HTTP\DownloadResponse
     */
    public function documentsDownload(int $id): \CodeIgniter\HTTP\DownloadResponse
    {
        $this->data['document'] = model('\App\Models\NoticiesNationalDocumentModel')->where('id', $id)->first();
        if (empty($this->data['document'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->response->download(WRITEPATH . '/downloads/' . $this->data['document']->documentoDesignacao, null);
    }

    /**
     * List all texts associated with a document
     * @param  int    $documentId The selected document
     * @return string The view showing the texts
     */
    public function documentsTexts(int $documentId): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['document'] = model('\App\Models\NoticiesNationalDocumentModel')->where('id', $documentId)->first();
        if (empty($this->data['document'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $this->data['document']->notice_id)->first();
        $this->data['texts'] = model('\App\Models\NoticeNationalTextModel')->where('document_id', $documentId)->findAll();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $this->data['document']->notice_id))
            ->addCrumb('Documentos', site_url('notices/national/documents/' . $this->data['document']->notice_id))
            ->addCrumb('Textos indexados', site_url('notices/national/documents/texts/' . $this->data['document']->id));

        return view('admin/notices/documents-texts', $this->data);
    }

    /**
     * Shoe the document text form
     * @param  int      $id The selected text
     * @return string
     */
    public function documentsTextsUpdate(int $id): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['text'] = model('\App\Models\NoticeNationalTextModel')->where('id', $id)->first();
        if (empty($this->data['text'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['document'] = model('\App\Models\NoticiesNationalDocumentModel')->where('id', $this->data['text']->document_id)->first();
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $this->data['document']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $this->data['document']->notice_id))
            ->addCrumb('Documentos', site_url('notices/national/documents/' . $this->data['document']->notice_id))
            ->addCrumb('Textos indexados', site_url('notices/national/documents/texts/' . $this->data['document']->id))
            ->addCrumb($this->data['text']->title, site_url('notices/national/documents/texts/update' . $this->data['text']->id));

        return view('admin/notices/documents-texts-update', $this->data);
    }

    /**
     * Save documents texts
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function documentsTextsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeNationalTextModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticeNationalTextModel')->errors());
        }
        $text = model('\App\Models\NoticeNationalTextModel')->where('id', $this->request->getPost('id'))->first();

        return redirect()->to('notices/national/documents/texts/' . $text->document_id)
            ->with('confirm', 'O texto foi gravado com sucesso');
    }

    /**
     * Delete a text associated with a file
     * @param  int                                  $id The selected text
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function documentsTextsDelete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeNationalTextModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o texto selecionado.');
        }

        return redirect()->back()->with('confirm', 'O texto foi apagado com sucesso.');
    }

    /**
     * Show programas associated with a given notice id
     * @param  int      $noticeId The selected notice
     * @return string
     */
    public function programsNational(int $noticeId): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['programs'] = model('\App\Models\NoticiesNationalExtraModel')
            ->where('notice_id', $noticeId)
            ->orderBy('programaOperacionalDesignacao', 'ASC')
            ->findAll();
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $noticeId)->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $this->data['notice']->id))
            ->addCrumb('Programas', site_url('notices/national/programs/' . $this->data['notice']->id));

        return view('admin/notices/programs-national', $this->data);
    }

    /**
     * Show program form
     * @param  int      $id selected program
     * @return string
     */
    public function programsUpdate(int $id): string
    {
        $this->data['activeSubMenu'] = 'national';
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')->where('id', $id)->first();
        if (empty($this->data['program'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['program']->thematics = array_column(
            model('\App\Models\NoticeThematicsModel')->where('extra_id', $this->data['program']->id)->asArray()->findAll(),
            'thematic_id'
        );
        $this->data['program']->sectors = array_column(
            model('\App\Models\NoticeSectorsModel')->where('extra_id', $this->data['program']->id)->asArray()->findAll(),
            'sector_id'
        );
        $this->data['program']->types = array_column(
            model('\App\Models\NoticeTypesModel')->where('extra_id', $this->data['program']->id)->asArray()->findAll(),
            'type_id'
        );
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')->where('id', $this->data['program']->notice_id)->first();
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['nuts1'] = model('\App\Models\CityModel')->distinct('nut_1')->select('nut_1 AS nut')
            ->orderBy('nut_1')
            ->findAll();
        $this->data['nuts2'] = model('\App\Models\CityModel')->distinct('nut_2')->select('nut_2 AS nut')
            ->orderBy('nut_2')
            ->findAll();
        $this->data['nuts3'] = model('\App\Models\CityModel')->distinct('nut_3')->select('nut_3 AS nut')
            ->orderBy('nut_3')
            ->findAll();
        $this->data['program']->segmentation = service('segmentation')->getByProgram($this->data['program']);
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($this->data['notice']->designacaoPT, 5), site_url('notices/national/update/' . $this->data['notice']->id))
            ->addCrumb('Programas', site_url('notices/national/programs/' . $this->data['notice']->id))
            ->addCrumb($this->data['program']->programaOperacionalDesignacao, site_url('notices/national/programs/update' . $id));

        return view('admin/notices/programs-update', $this->data);
    }

    /**
     * Save program
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function programsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        $postData = $this->request->getPost();
        $segmentation = $postData['segmentation'];

        unset($postData['segmentation']);
        if (!model('\App\Models\NoticiesNationalExtraModel')->save($postData)) {
            model('\App\Models\NoticeThematicsModel')->where('extra_id', $postData['id'])->delete();
            foreach ($postData['thematics'] as $tId) {
                model('\App\Models\NoticeThematicsModel')->insert(['extra_id' => $postData['id'], 'thematic_id' => $tId]);
            }
            model('\App\Models\NoticeTypesModel')->where('extra_id', $postData['id'])->delete();
            foreach ($postData['types'] as $tyId) {
                model('\App\Models\NoticeTypesModel')->insert(['extra_id' => $postData['id'], 'type_id' => $tyId]);
            }
            model('\App\Models\NoticeSectorsModel')->where('extra_id', $postData['id'])->delete();
            foreach ($postData['sectors'] as $sId) {
                model('\App\Models\NoticeSectorsModel')->insert(['extra_id' => $postData['id'], 'sector_id' => $sId]);
            }

            return redirect()->back()->with('errors', model('\App\Models\NoticiesNationalExtraModel')->errors());
        }
        if (!empty($segmentation)) {
            service('segmentation')->segmentByProgram(
                $postData['id'],
                $segmentation['filterType'] ?? null,
                $segmentation[$segmentation['filterType']] ?? []
            );
        }

        return redirect()->back()->with('confirm', 'O texto foi gravado com sucesso');
    }

    /**
     * Manage thematics associated with a given program id
     * @param  int    $programId The selected program
     * @return string The view
     */
    public function thematics(int $programId): string
    {
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')->where('id', $programId)->first();
        if (empty($this->data['program'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->thematics = array_column(
            model('\App\Models\NoticeThematicsModel')
                ->where('extra_id', $this->data['program']->id)
                ->asArray()
                ->findAll(),
            'thematic_id');

        $ntc = model('\App\Models\NoticeNationalModel')->where('id', $this->data['program']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($ntc->designacaoPT, 5), site_url('notices/national/update/' . $ntc->id))
            ->addCrumb($this->data['program']->programaOperacionalDesignacao, site_url('notices/national/programs/' . $ntc->id))
            ->addCrumb('Temáticas', site_url('notices/national/programs/' . $ntc->id));

        return view('admin/notices/thematics', $this->data);
    }

    /**
     * Save thematics into a program
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function thematicsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        $return = true;
        $program = model('\App\Models\NoticiesNationalExtraModel')
            ->where('id', $this->request->getPost('extra_id'))
            ->first();
        model('\App\Models\NoticeThematicsModel')
            ->where('extra_id', $this->request->getPost('extra_id'))
            ->delete();
        if (empty($_POST['thematics'])) {
            return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'A Temáticas foi atualizada com sucesso!');
        }

        foreach ($this->request->getPost('thematics') as $thematicId) {
            $insert = model('\App\Models\NoticeThematicsModel')->insert([
                'extra_id' => $this->request->getPost('extra_id'),
                'thematic_id' => $thematicId,
            ]);
            if (!$insert) {
                $return = false;
            }
        }
        if (!$return) {
            return redirect()->back()->with('error', 'Houve um problema ao gravar a Temáticas!');
        }

        return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'A Temáticas foi atualizada com sucesso!');
    }

    /**
     * Manage Types associated with a given program id
     * @param  int    $programId The selected program
     * @return string The view
     */
    public function types(int $programId): string
    {
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')->where('id', $programId)->first();
        if (empty($this->data['program'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->types = array_column(
            model('\App\Models\NoticeTypesModel')
                ->where('extra_id', $this->data['program']->id)
                ->asArray()
                ->findAll(),
            'type_id');

        $ntc = model('\App\Models\NoticeNationalModel')->where('id', $this->data['program']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($ntc->designacaoPT, 5), site_url('notices/national/update/' . $ntc->id))
            ->addCrumb($this->data['program']->programaOperacionalDesignacao, site_url('notices/national/programs/' . $ntc->id))
            ->addCrumb('Tipos', site_url('notices/national/programs/' . $ntc->id));

        return view('admin/notices/types', $this->data);
    }

    /**
     * Save Types into a program
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function typesSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        $return = true;
        $program = model('\App\Models\NoticiesNationalExtraModel')
            ->where('id', $this->request->getPost('extra_id'))
            ->first();
        model('\App\Models\NoticeTypesModel')
            ->where('extra_id', $this->request->getPost('extra_id'))
            ->delete();
        if (empty($_POST['types'])) {
            return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'Os Tipo de Entidade foram atualizadas com sucesso!');
        }
        foreach ($this->request->getPost('types') as $typeId) {
            $insert = model('\App\Models\NoticeTypesModel')->insert([
                'extra_id' => $this->request->getPost('extra_id'),
                'type_id' => $typeId,
            ]);
            if (!$insert) {
                $return = false;
            }
        }
        if (!$return) {
            return redirect()->back()->with('error', 'Houve um problema ao gravar o Tipo de Entidade!');
        }

        return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'Os Tipo de Entidade foram atualizados com sucesso!');
    }

    /**
     * Manage Sectors associated with a given program id
     * @param  int    $programId The selected program
     * @return string The view
     */
    public function sectors(int $programId): string
    {
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')->where('id', $programId)->first();
        if (empty($this->data['program'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->sectors = array_column(
            model('\App\Models\NoticeSectorsModel')
                ->where('extra_id', $this->data['program']->id)
                ->asArray()
                ->findAll(),
            'sector_id');

        $ntc = model('\App\Models\NoticeNationalModel')->where('id', $this->data['program']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb('Portugal 2030', site_url('notices/national'))
            ->addCrumb(word_limiter($ntc->designacaoPT, 5), site_url('notices/national/update/' . $ntc->id))
            ->addCrumb($this->data['program']->programaOperacionalDesignacao, site_url('notices/national/programs/' . $ntc->id))
            ->addCrumb('Setores', site_url('notices/national/programs/' . $ntc->id));

        return view('admin/notices/sectors', $this->data);
    }

    /**
     * Save Sectors into a program
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function sectorsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        $return = true;
        $program = model('\App\Models\NoticiesNationalExtraModel')
            ->where('id', $this->request->getPost('extra_id'))
            ->first();
        model('\App\Models\NoticeSectorsModel')
            ->where('extra_id', $this->request->getPost('extra_id'))
            ->delete();
        if (empty($_POST['sectors'])) {
            return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'Os Setores foram atualizados com sucesso!');
        }
        foreach ($this->request->getPost('sectors') as $sectorId) {
            $insert = model('\App\Models\NoticeSectorsModel')->insert([
                'extra_id' => $this->request->getPost('extra_id'),
                'sector_id' => $sectorId,
            ]);
            if (!$insert) {
                $return = false;
            }
        }
        if (!$return) {
            return redirect()->back()->with('error', 'Houve um problema ao gravar os Setores!');
        }

        return redirect()->to('notices/national/programs/' . $program->notice_id)->with('message', 'Os Setores foram atualizados com sucesso!');
    }

}
