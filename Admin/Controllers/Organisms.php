<?php
namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Organisms extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'organisms';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('Organismos', site_url('settings/organisms'));
    }

    /**
     * Show the list of Organisms
     * @return string
     */
    public function index(): string
    {
        // Get all Organisms
        $this->data['organisms'] = model('\App\Models\OrganismModel')->orderBy('entity', 'ASC')->findAll();

        return view('admin/organisms/index', $this->data);
    }

    /**
     * Insert organism view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/organisms/insert'));

        return view('admin/organisms/manage', $this->data);
    }

    /**
     * Save Organism
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\OrganismModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\OrganismModel')->errors())->withInput();
        }

        return redirect()->to('settings/organisms')->with('message', 'O Organismo Nacional foi gravado com sucesso!');
    }

    /**
     * Update Organism view
     * @param  int      $id The selected Organism
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['organism'] = model('\App\Models\OrganismModel')->where('id', $id)->first();
        if (empty($this->data['organism'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/organisms/update' . $id))
            ->addCrumb($this->data['organism']->entity, site_url('settings/organisms/update/' . $id));

        return view('admin/organisms/manage', $this->data);
    }

    /**
     * Delete Organism
     * @param  int    $id
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\OrganismModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Organismo Nacional');
        }

        return redirect()->back()->with('confirm', 'O Organismo Nacional foi apagado com sucesso.');
    }

}
