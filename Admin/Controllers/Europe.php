<?php
namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Europe extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'notices';
        $this->data['activeSubMenu'] = 'europe';
        $this->data['breadcrumbs']->addCrumb('Avisos', site_url('notices'))
            ->addCrumb('Europeus', site_url('notices/europe'));
    }

    /**
     * Show the list of Notices in Europe
     * @return string
     */
    public function index(): string
    {
        $this->data['notices'] = model('\App\Models\NoticeEuropeModel')->orderBy('start_date', 'ASC')->findAll();
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();

        return view('admin/europe/index', $this->data);
    }

    /**
     * Insert European Notices
     * @return string
     */
    public function insert(): string
    {
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('notices/europe/insert'));

        return view('admin/europe/manage', $this->data);
    }

    /**
     * Save European Notice
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {

        if (isset($_POST['id']) && !empty($_POST['id'])) {
            if (!model('\App\Models\NoticeEuropeModel')->save($this->request->getPost())) {
                return redirect()->back()->with('errors', model('\App\Models\NoticeEuropeModel')->errors())->withInput();
            }
        } else if (!model('\App\Models\NoticeEuropeModel')->insert($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticeEuropeModel')->errors())->withInput();
        }

        return redirect()->to('notices/europe')->with('message', 'O Aviso Europeu foi gravado com sucesso!');
    }

    /**
     * Update European Notice view
     * @param  int      $id The selected Notice
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['notice'] = model('\App\Models\NoticeEuropeModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('notices/europe/update' . $id));

        return view('admin/europe/manage', $this->data);
    }

    /**
     * Delete European Notice
     * @param  int    $id
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeEuropeModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Aviso Europeu');
        }

        return redirect()->back()->with('confirm', 'O Aviso Europeu foi apagado com sucesso.');
    }

}
