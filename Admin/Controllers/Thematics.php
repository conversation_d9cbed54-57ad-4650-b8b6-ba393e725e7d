<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Thematics extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'thematics';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/thematics'))
            ->addCrumb('Temáticas', site_url('settings/thematics'));
    }

    /**
     * Show the list of thematics
     * @return string
     */
    public function index(): string
    {
        // Get all thematics
        $this->data['thematics'] = model('\App\Models\ThematicModel')
            ->countEntitiesAndNoticies()
            ->orderBy('name', 'ASC')
            ->findAll();

        return view('admin/thematics/index', $this->data);
    }

    /**
     * Insert Segment view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/thematics/insert'));

        return view('admin/thematics/manage', $this->data);
    }

    /**
     * Save Segment
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ThematicModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\ThematicModel')->errors())->withInput();
        }

        return redirect()->to('settings/thematics');
    }

    /**
     * Update thematic view
     * @param  int      $id The selected thematic
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['thematic'] = model('\App\Models\ThematicModel')->where('id', $id)->first();
        if (empty($this->data['thematic'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/thematics/update' . $id))
            ->addCrumb($this->data['thematic']->name, site_url('settings/thematics/update/' . $id));

        return view('admin/thematics/manage', $this->data);
    }

    /**
     * Delete Segment
     * @param  int    $id        The selected thematic
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ThematicModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o temática');
        }

        return redirect()->back()->with('confirm', 'O temática foi apagado com sucesso.');
    }
}
