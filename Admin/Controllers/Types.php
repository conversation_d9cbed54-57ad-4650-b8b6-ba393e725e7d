<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Types extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'types';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/types'))
            ->addCrumb('Tipo de Entidade', site_url('settings/types'));
    }

    /**
     * Show the list of types
     * @return string
     */
    public function index(): string
    {
        // Get all types
        $this->data['types'] = model('\App\Models\TypeModel')
            ->countEntitiesAndNoticies()
            ->orderBy('name', 'ASC')
            ->findAll();

        return view('admin/types/index', $this->data);
    }

    /**
     * Insert type view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/types/insert'));

        return view('admin/types/manage', $this->data);
    }

    /**
     * Save type
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\TypeModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\TypeModel')->errors())->withInput();
        }

        return redirect()->to('settings/types');
    }

    /**
     * Update types view
     * @param  int      $id The selected types
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['type'] = model('\App\Models\TypeModel')->where('id', $id)->first();
        if (empty($this->data['type'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/types/update' . $id))
            ->addCrumb($this->data['type']->name, site_url('settings/types/update/' . $id));

        return view('admin/types/manage', $this->data);
    }

    /**
     * Delete type
     * @param  int    $id        The selected type
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\TypeModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a Tipologia');
        }

        return redirect()->back()->with('confirm', 'A Tipologia foi apagado com sucesso.');
    }
}
