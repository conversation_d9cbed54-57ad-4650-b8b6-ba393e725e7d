<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Sectors extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'sectors';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/sectors'))
            ->addCrumb('Setores', site_url('settings/sectors'));
    }

    /**
     * Show the list of sectors
     * @return string
     */
    public function index(): string
    {
        // Get all sectors
        $this->data['sectors'] = model('\App\Models\SectorModel')
            ->countEntitiesAndNoticies()
            ->orderBy('name', 'ASC')
            ->findAll();

        return view('admin/sectors/index', $this->data);
    }

    /**
     * Insert sector view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/sectors/insert'));

        return view('admin/sectors/manage', $this->data);
    }

    /**
     * Save sector
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\SectorModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\SectorModel')->errors())->withInput();
        }

        return redirect()->to('settings/sectors');
    }

    /**
     * Update sectors view
     * @param  int      $id The selected sectors
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['sector'] = model('\App\Models\SectorModel')->where('id', $id)->first();
        if (empty($this->data['sector'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/sectors/update' . $id))
            ->addCrumb($this->data['sector']->name, site_url('settings/sectors/update/' . $id));

        return view('admin/sectors/manage', $this->data);
    }

    /**
     * Delete sector
     * @param  int    $id        The selected sector
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\SectorModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a Tipologia');
        }

        return redirect()->back()->with('confirm', 'A Tipologia foi apagado com sucesso.');
    }
}
