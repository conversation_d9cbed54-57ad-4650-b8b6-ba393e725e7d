<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Parish extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'parish';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('Juntas de Freguesia', site_url('settings/parish'));
    }

    /**
     * Show the list of Parish
     * @return string
     */
    public function index(): string
    {
        // Get all Parish
        $this->data['parish'] = model('\App\Models\ParishModel')->orderBy('name', 'ASC')->findAll();

        return view('admin/parish/index', $this->data);
    }

    /**
     * Insert parish view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/parish/insert'));

        return view('admin/parish/manage', $this->data);
    }

    /**
     * Save Parish
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ParishModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\ParishModel')->errors())->withInput();
        }

        return redirect()->to('settings/parish')->with('message', 'A Junta de Freguesia foi gravada com sucesso!');
    }

    /**
     * Update Parish view
     * @param  int      $id The selected Parish
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['parish'] = model('\App\Models\ParishModel')->where('id', $id)->first();
        if (empty($this->data['parish'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/parish/update' . $id))
            ->addCrumb($this->data['parish']->name, site_url('settings/parish/update/' . $id));

        return view('admin/parish/manage', $this->data);
    }

    /**
     * Delete Parish
     * @param  int    $id        The selected Parish
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ParishModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a Junta de Freguesia');
        }

        return redirect()->back()->with('confirm', 'A Junta de Freguesia foi apagada com sucesso.');
    }
}
