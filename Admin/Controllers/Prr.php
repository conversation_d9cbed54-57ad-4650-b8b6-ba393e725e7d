<?php
namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Prr extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'notices';
        $this->data['activeSubMenu'] = 'prr';
        $this->data['archive'] = false;
        $this->data['breadcrumbs']->addCrumb('Avisos', site_url('notices'))
            ->addCrumb('Plano de Recuperação e Resiliência', site_url('notices/prr'));
    }

    /**
     * Show the list of Notices in Prr
     * @return string
     */
    public function index(): string
    {
        $this->data['archive'] = $_GET['archive'] ?? 0;
        $this->data['notices'] = model('\App\Models\NoticePrrModel')->orWhere([
            'date_end' => '', 'date_end >=' => date('Y-m-d'), 'date_end' => null, 'date_end' => '0000-00-00',
        ])->findAll();
        if ($this->data['archive']) {
            $this->data['notices'] = model('\App\Models\NoticePrrModel')->where('date_end <', date('Y-m-d'))->findAll();
        }

        return view('admin/prr/index', $this->data);
    }

    /**
     * Save Prr Notice
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticePrrModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticePrrModel')->errors())->withInput();
        }
        service('prrSegmentation')->segmentByNotice(
            $this->request->getPost('id'),
            $this->request->getPost('segmentation_type') ?? null,
            $this->request->getPost($_POST['segmentation_type']) ?? []
        );
        if (isset($_POST['segments']['thematics'])) {
            model('\App\Models\NoticePrrThematicModel')->where('notice_id', $this->request->getPost('id'))->delete();
            foreach ($this->request->getPost('segments')['thematics'] as $thematicId) {
                model('\App\Models\NoticePrrThematicModel')->insert(['notice_id' => $this->request->getPost('id'), 'thematic_id' => $thematicId]);
            }
        }
        if (isset($_POST['segments']['types'])) {
            model('\App\Models\NoticePrrTypeModel')->where('notice_id', $this->request->getPost('id'))->delete();
            foreach ($this->request->getPost('segments')['types'] as $typeId) {
                model('\App\Models\NoticePrrTypeModel')->insert(['notice_id' => $this->request->getPost('id'), 'type_id' => $typeId]);
            }
        }
        if (isset($_POST['segments']['sectors'])) {
            model('\App\Models\NoticePrrSectorModel')->where('notice_id', $this->request->getPost('id'))->delete();
            foreach ($this->request->getPost('segments')['sectors'] as $sectorId) {
                model('\App\Models\NoticePrrSectorModel')->insert(['notice_id' => $this->request->getPost('id'), 'sector_id' => $sectorId]);
            }
        }

        return redirect()->to('notices/prr')->with('message', 'O Aviso do Plano de Recuperação e Resiliência foi gravado com sucesso!');
    }

    /**
     * Update PRR Notice view
     * @param  int      $id The selected Notice
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['notice'] = model('\App\Models\NoticePrrModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['notice']->segmentation = service('prrSegmentation')->getByNotice($this->data['notice']);
        $this->data['notice']->thematics = array_column(
            model('\App\Models\NoticePrrThematicModel')->where('notice_id', $id)->findAll(),
            'thematic_id'
        );
        $this->data['notice']->types = array_column(
            model('\App\Models\NoticePrrTypeModel')->where('notice_id', $id)->findAll(),
            'type_id'
        );
        $this->data['notice']->sectors = array_column(
            model('\App\Models\NoticePrrSectorModel')->where('notice_id', $id)->findAll(),
            'sector_id'
        );
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['nuts1'] = model('\App\Models\CityModel')->distinct('nut_1')->select('nut_1 AS nut')
            ->orderBy('nut_1')
            ->findAll();
        $this->data['nuts2'] = model('\App\Models\CityModel')->distinct('nut_2')->select('nut_2 AS nut')
            ->orderBy('nut_2')
            ->findAll();
        $this->data['nuts3'] = model('\App\Models\CityModel')->distinct('nut_3')->select('nut_3 AS nut')
            ->orderBy('nut_3')
            ->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('notices/prr/update/' . $id))
            ->addCrumb($this->data['notice']->name, site_url('notices/prr/update/' . $id));

        return view('admin/prr/manage', $this->data);
    }

    /**
     * Delete Prr Notice
     * @param  int    $id
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticePrrModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Aviso do Plano de Recuperação e Resiliência');
        }

        return redirect()->back()->with('confirm', 'O Aviso do Plano de Recuperação e Resiliência foi apagado com sucesso.');
    }

    /**
     * Show a list of links associated with a PRR notice
     * @param  int    $id    The selected notice
     * @return string View
     */
    public function links(int $id): string
    {
        $this->data['notice'] = model('\App\Models\NoticePrrModel')->where('id', $id)->first();
        if (empty($this->data['notice'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['links'] = model('\App\Models\NoticePrrLinkModel')->where('notice_id', $id)->findAll();
        $this->data['breadcrumbs']->addCrumb($this->data['notice']->name, site_url('notices/prr/update/' . $id))
            ->addCrumb('Links', site_url('notices/prr/links/' . $id));

        return view('admin/prr/links', $this->data);
    }

    /**
     * Update LINK view
     * @param  int      $id The selected link
     * @return string
     */
    public function linksUpdate(int $id): string
    {
        $this->data['link'] = model('\App\Models\NoticePrrLinkModel')->where('id', $id)->first();
        if (empty($this->data['link'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['notice'] = model('\App\Models\NoticePrrModel')
            ->where('id', $this->data['link']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb($this->data['notice']->name, site_url('notices/prr/update/' . $id))
            ->addCrumb('Links', site_url('notices/prr/links/' . $id))
            ->addCrumb('Editar', site_url('notices/prr/links/' . $id));

        return view('admin/prr/links-update', $this->data);
    }

    /**
     * Save LINK Notice
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function linksSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticePrrLinkModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticePrrModel')->errors())->withInput();
        }

        return redirect()->to('notices/prr/links/' . $_POST['notice_id'])->with('message', 'O Link do Plano de Recuperação e Resiliência foi gravado com sucesso!');
    }

    /**
     * Delete LINK Notice
     * @param  int    $id
     * @return object redirect
     */
    public function linksDelete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticePrrLinkModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Link do Plano de Recuperação e Resiliência');
        }

        return redirect()->back()->with('confirm', 'O Link do Plano de Recuperação e Resiliência foi apagado com sucesso.');
    }

}
