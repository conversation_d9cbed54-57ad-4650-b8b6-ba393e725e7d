<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;
use App\Models\UserMdel;
use CodeIgniter\Shield\Config\AuthGroups;
use CodeIgniter\Shield\Entities\User;

class Employees extends BaseController
{
    public $helpers = ['text', 'form'];

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'companies';
        $this->data['breadcrumbs']->addCrumb('Entidades', site_url('companies'));
    }

    /**
     * Show the list of links that will be indexed
     * @param  int      $companyId
     * @return string
     */
    public function index(int $companyId): string
    {
        $this->data['employees'] = auth()->getProvider()->getEmployees($companyId);
        $this->data['companyId'] = $companyId;
        $username = auth()->getProvider()->findById($companyId)->username;
        if (empty($username)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException ();
        }
        $this->data['breadcrumbs']->addCrumb($username, site_url('companies/employees/' . $companyId))
            ->addCrumb('Utilizadores', site_url('companies/employees/' . $companyId));

        return view('admin/employees/index', $this->data);
    }

    /**
     * Insert employee view
     * @param  int      $companyId
     * @return string
     */
    public function insert(int $companyId): string
    {
        $username = auth()->getProvider()->findById($companyId)->username;
        if (empty($username)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException ();
        }
        $this->data['breadcrumbs']->addCrumb($username, site_url('companies/employees/' . $companyId))
            ->addCrumb('Inserir', site_url('companies/employees/insert/' . $companyId));
        $this->data['companyId'] = $companyId;

        return view('admin/employees/manage', $this->data);
    }

    /**
     * Save employee action
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        $postData = $this->request->getPost();
        $postData['group'] = 'employee';
        if (!service('user')->save($postData)) {
            return redirect()->back()->withInput();
        }

        return redirect()->to('companies/employees/' . $postData['company_id']);
    }

    /**
     * Update employee view
     * @param  int      $id The selected company
     * @param  int      $id The selected employee
     * @return string
     */
    public function update(int $companyId, int $employeeId): string
    {
        $this->data['employee'] = auth()->getProvider()->getUserDetails('employee', $employeeId)[0];
        $username = auth()->getProvider()->findById($companyId)->username;
        if (empty($this->data['employee']) || empty($username)) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException ();
        }
        $this->data['breadcrumbs']->addCrumb($username, site_url('companies/employees/' . $companyId))
            ->addCrumb('Utilizadores', site_url('companies/employees/' . $companyId))
            ->addCrumb('Editar', site_url('companies/employees/update/' . $companyId . '/' . $employeeId))
            ->addCrumb($this->data['employee']->email, site_url('companies/employees/update/' . $companyId . '/' . $employeeId));
        $this->data['companyId'] = $companyId;

        return view('admin/employees/manage', $this->data);
    }

    /**
     * Delete employee
     * @param  int    $id        The selected employee
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        $users = auth()->getProvider();
        // delete with false means softDelete
        if (!$users->delete($id, false)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o utilizador');
        }

        return redirect()->back()->with('confirm', 'O utilizador foi apagado com sucesso');
    }
}
