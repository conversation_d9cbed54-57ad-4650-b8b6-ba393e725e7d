<?php

namespace Admin\Controllers;

class Auth extends BaseController
{
    /**
     * Load helpers
     * @var array
     */
    public $helpers = ['text'];

    /**
     * Login page
     * @return string
     */
    public function loginView(): string
    {
        return view('admin/shield/login', $this->data);
    }

    /**
     * Reset Password page
     * @return string
     */
    public function forgotPasswordView(): string
    {
        return view('shared/shield/forgotPassword');
    }

    /**
     * Reset Password page
     * @return string
     */
    public function resetPasswordView(): string
    {
        $this->data['userId'] = $this->request->getGet('u');
        $this->data['token'] = $this->request->getGet('t');

        return view('shared/shield/resetPassword', $this->data);
    }

    /**
     * Handle admin login
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function loginAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        // Get the credentials for login
        $credentials = [
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'),
        ];
        $remember = (bool) $this->request->getPost('chk_remember');

        $loginAttempt = auth()->remember($remember)->attempt($credentials);
        if (!$loginAttempt->isOK()) {
            return redirect()->back()->with('error', $loginAttempt->reason());
        }

        return redirect()->to('/');
    }

    /**
     * Handle app forgotPassword
     * @todo send email to user $user->getEmailIdentity()->secret with token $tokenGenerated
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function forgotPasswordAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        $user = auth()->getProvider()->findByCredentials($this->request->getPost());
        if (!$user) {
            return redirect()->back()->with('error', 'Não existe nenhuma conta com o e-mail inserido');
        }
        if (!$user->inGroup('admin')) {
            return redirect()->back()->with('error', 'Não tem acesso para recuperar a sua password numa página da administração');
        }

        $tokenGenerated = service('user')->generateForgotPasswordToken($user);
        if (!$tokenGenerated) {
            return redirect()->back();
        }

        // button link for user to access the form
        $btnLink = 'auth/reset-password/?u=' . $user->id . '&t=' . $tokenGenerated;

        return redirect()->to($btnLink);
    }

    /**
     * Reset password
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function resetPasswordAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        $postData = $this->request->getPost();
        if (!service('user')->updatePassword($postData)) {
            return redirect()->back();
        }

        return redirect()->to('auth/login')->with('confirm', 'A sua password foi alterada com sucesso');
    }
}
