<?php
namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Program extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'europe';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/thematics'))
            ->addCrumb('Programas', site_url('settings/europe-programs'))
            ->addCrumb('Europeus', site_url('settings/europe-programs'));
    }

    /**
     * Show the list of Programs
     * @return string
     */
    public function index(): string
    {
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->select('nt_notices_europe_programs.*, nt_organisms.entity')
            ->join('nt_organisms', 'nt_notices_europe_programs.organism_id = nt_organisms.id', 'left')
            ->orderBy('program', 'ASC')
            ->findAll();

        return view('admin/program/index', $this->data);
    }

    /**
     * Insert European program View
     * @return string
     */
    public function insert(): string
    {
        $this->data['organisms'] = model('\App\Models\OrganismModel')->orderBy('entity', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/europe-programs/insert'));

        return view('admin/program/manage', $this->data);
    }

    /**
     * Save European Program
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeEuropeProgramModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticeEuropeProgramModel')->errors())->withInput();
        }

        return redirect()->to('notices/europe-programs')->with('message', 'O programa Europeu foi gravado com sucesso!');
    }

    /**
     * Update European view
     * @param  int      $id The selected Organism
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')->where('id', $id)->first();
        $this->data['organisms'] = model('\App\Models\OrganismModel')->orderBy('entity', 'ASC')->findAll();
        if (empty($this->data['program'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/europe-programs/update' . $id))
            ->addCrumb($this->data['program']->name, site_url('settings/europe-programs/update' . $id));

        return view('admin/program/manage', $this->data);
    }

    /**
     * Delete European Program
     * @param  int    $id
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticeEuropeProgramModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Programa Europeu');
        }

        return redirect()->back()->with('confirm', 'O Programa Europeu foi apagado com sucesso.');
    }

    /**
     * Set Segments for a Notice
     * @param  int    $noticeId The selected notice
     * @return string The view with all the notices
     */
    public function segments(int $noticeId): string
    {
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')->where('id', $noticeId)->first();
        if (empty($this->data['program'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }

        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->cims = array_column(
            model('\App\Models\NoticeEuropeCimModel')
                ->where('notice_id', $noticeId)
                ->asArray()
                ->findAll(),
            'cim_id');
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->thematics = array_column(
            model('\App\Models\NoticeEuropeThematicModel')
                ->where('notice_id', $noticeId)
                ->asArray()
                ->findAll(),
            'thematic_id');
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->types = array_column(
            model('\App\Models\NoticeEuropeTypeModel')
                ->where('notice_id', $noticeId)
                ->asArray()
                ->findAll(),
            'type_id');
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['program']->sectors = array_column(
            model('\App\Models\NoticeEuropeSectorModel')
                ->where('notice_id', $noticeId)
                ->asArray()
                ->findAll(),
            'sector_id');
        $this->data['breadcrumbs']->addCrumb($this->data['program']->name, site_url('settings/europe-programs/update/' . $noticeId))
            ->addCrumb('Segmentação', site_url('settings/europe-programs/segments/' . $noticeId));

        return view('admin/program/segments', $this->data);
    }

    /**
     * Save segments into a European Notice
     * @return object
     */
    public function segmentsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        $noticeId = $this->request->getPost('notice_id');
        model('\App\Models\NoticeEuropeCimModel')->where('notice_id', $noticeId)->delete();
        model('\App\Models\NoticeEuropeThematicModel')->where('notice_id', $noticeId)->delete();
        model('\App\Models\NoticeEuropeTypeModel')->where('notice_id', $noticeId)->delete();
        model('\App\Models\NoticeEuropeSectorModel')->where('notice_id', $noticeId)->delete();

        $cims = $this->request->getPost('cims') ?? [];
        $types = $this->request->getPost('types') ?? [];
        $thematics = $this->request->getPost('thematics') ?? [];
        $sectors = $this->request->getPost('sectors') ?? [];

        foreach ($cims as $cimId) {
            model('\App\Models\NoticeEuropeCimModel')->insert(['notice_id' => $noticeId, 'cim_id' => $cimId]);
        }
        foreach ($thematics as $thematicId) {
            model('\App\Models\NoticeEuropeThematicModel')->insert(['notice_id' => $noticeId, 'thematic_id' => $thematicId]);
        }
        foreach ($types as $typeId) {
            model('\App\Models\NoticeEuropeTypeModel')->insert(['notice_id' => $noticeId, 'type_id' => $typeId]);
        }
        foreach ($sectors as $sectorId) {
            model('\App\Models\NoticeEuropeSectorModel')->insert(['notice_id' => $noticeId, 'sector_id' => $sectorId]);
        }

        return redirect()->back()->with('confirm', 'A sua segmentação foi gravada com sucesso!');
    }

    /**
     * List all documents in a European Notice
     * @param  int    $noticeId The selected Program
     * @return string The view with all the notices
     */
    public function documents(int $noticeId): string
    {
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')->where('id', $noticeId)->first();
        if (empty($this->data['program'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['documents'] = model('\App\Models\NoticiesEuropeDocumentModel')->where('notice_id', $noticeId)->findAll();
        $this->data['breadcrumbs']->addCrumb($this->data['program']->name, site_url('settings/europe-programs/update/' . $noticeId))
            ->addCrumb('Documentos', site_url('settings/europe-programs/documents/' . $noticeId));

        return view('admin/program/documents', $this->data);
    }

    /**
     * Insert Documents View
     * @return string
     */
    public function DocumentsInsert(int $noticeId): string
    {
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')->where('id', $noticeId)->first();
        if (empty($this->data['program'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb($this->data['program']->name, site_url('notices/europe-programs/update/' . $noticeId))
            ->addCrumb('Documentos', site_url('notices/europe-programs/documents/' . $noticeId))
            ->addCrumb('Inserir', site_url('notices/europe-programs/documents/insert/' . $noticeId));

        return view('admin/program/documents-manage', $this->data);
    }

    /**
     * Save European Document
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function DocumentsSave(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticiesEuropeDocumentModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\NoticiesEuropeDocumentModel')->errors())->withInput();
        }

        return redirect()->to('notices/europe-programs/documents/' . $_POST['notice_id'])->with('message', 'O documento foi gravado com sucesso!');
    }

    /**
     * Update European Documents view
     * @param  int      $id The selected document
     * @return string
     */
    public function DocumentsUpdate(int $id): string
    {
        $this->data['document'] = model('\App\Models\NoticiesEuropeDocumentModel')->where('id', $id)->first();
        if (empty($this->data['document'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')->where('id', $this->data['document']->notice_id)->first();
        $this->data['breadcrumbs']->addCrumb($this->data['program']->program, site_url('notices/europe-programs/update/' . $this->data['program']->id))
            ->addCrumb('Documentos', site_url('notices/europe-programs/documents/' . $this->data['program']->id))
            ->addCrumb('Editar', site_url('notices/europe-programs/documents/update/' . $this->data['program']->id))
            ->addCrumb($this->data['document']->name, site_url('notices/europe-programs/documents/update/' . $this->data['document']->id));

        return view('admin/program/documents-manage', $this->data);
    }

    /**
     * Delete European Document
     * @param  int    $id
     * @return object redirect
     */
    public function DocumentsDelete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\NoticiesEuropeDocumentModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Documento');
        }

        return redirect()->back()->with('confirm', 'O Aviso Europeu foi apagado com sucesso.');
    }

}
