<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class CityHalls extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'city-halls';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/city-halls'))
            ->addCrumb('Municípios', site_url('settings/city-halls'));
    }

    /**
     * Show the list of city hall
     * @return string
     */
    public function index(): string
    {
        // Get all city hall
        $this->data['cityHalls'] = model('\App\Models\CityHallModel')->orderBy('name', 'ASC')->findAll();

        return view('admin/city-halls/index', $this->data);
    }

    /**
     * Insert city hall view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/city-hall/insert'));

        return view('admin/city-halls/manage', $this->data);
    }

    /**
     * Save city hall
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CityHallModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\CityHallModel')->errors())->withInput();
        }

        return redirect()->to('settings/city-halls')->with('message', 'A Junta de Freguesia foi gravada com sucesso!');
    }

    /**
     * Update city hall view
     * @param  int      $id The selected city hall
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['cityHall'] = model('\App\Models\CityHallModel')->where('id', $id)->first();
        if (empty($this->data['cityHall'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/city-hall/update' . $id))
            ->addCrumb($this->data['cityHall']->name, site_url('settings/city-hall/update/' . $id));

        return view('admin/city-halls/manage', $this->data);
    }

    /**
     * Delete city hall
     * @param  int    $id        The selected city hall
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CityHallModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a Junta de Freguesia');
        }

        return redirect()->back()->with('confirm', 'A Junta de Freguesia foi apagada com sucesso.');
    }
}
