<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Articles extends BaseController
{
    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'articles';
        $this->data['breadcrumbs']->addCrumb('Notícias', site_url('articles'));
        helper('slug');
    }

    /**
     * Show the list of articles
     * @return string
     */
    public function index(): string
    {
        $this->data['activeSubMenu'] = 'index';
        $this->data['articles'] = model('\App\Models\ArticleModel')
            ->orderBy('schedule', 'DESC')->findAll();

        return view('admin/articles/index', $this->data);
    }

    /**
     * Insert article view
     * @return string
     */
    public function insert(): string
    {
        $this->data['activeSubMenu'] = 'insert';
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('articles/insert'));

        return view('admin/articles/manage', $this->data);
    }

    /**
     * Insert/Update article action after post
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ArticleModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\ArticleModel')->errors())->withInput();
        }

        return redirect()->to('articles');
    }

    /**
     * Update article view
     * @param  int      $id The selected article
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['article'] = model('\App\Models\ArticleModel')->where('id', $id)->first();
        if (empty($this->data['article'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('articles/update' . $id))
            ->addCrumb($this->data['article']->title, site_url('articles/update/' . $id));

        return view('admin/articles/manage', $this->data);
    }

    /**
     * Delete article
     * @param  int    $id        The selected article
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\ArticleModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a notícia');
        }

        return redirect()->back()->with('confirm', 'A notícia foi apagada com sucesso');
    }
}
