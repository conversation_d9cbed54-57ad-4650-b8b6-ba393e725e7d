<?php

namespace Admin\Controllers;

use Admin\Controllers\BaseController;

class Companies extends BaseController
{
    public $helpers = ['text'];

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'companies';
        $this->data['breadcrumbs']->addCrumb('Entidades', site_url('companies'));
    }

    /**
     * Show the list of active companies
     * @return string
     */
    public function index(): string
    {
        $this->data['breadcrumbs']->addCrumb('Ativas', site_url('companies'));
        $this->data['activeSubMenu'] = 'active';
        // Get all users
        $this->data['companies'] = $this->data['companies'] = auth()->getProvider()->getUserDetails(
            'company', null, ['user_details.start_date !=' => null]
        );

        return view('admin/companies/index', $this->data);
    }

    /**
     * Show the list of pending companies
     * @return string
     */
    public function pendingCompanies(): string
    {
        $this->data['breadcrumbs']->addCrumb('Pendentes', site_url('companies/pending'));
        $this->data['activeSubMenu'] = 'pending';
        // Get all users
        $this->data['companies'] = auth()->getProvider()->getUserDetails(
            'company', null, ['user_details.start_date' => null]
        );

        return view('admin/companies/pending', $this->data);
    }

    /**
     * Insert company view
     * @return string
     */
    public function insert(): string
    {
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('companies/insert'));
        $this->data['activeSubMenu'] = 'active';

        return view('admin/companies/manage', $this->data);
    }

    /**
     * Insert/Update company action after post
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        $postData = $this->request->getPost();
        $postData['group'] = 'company';
        if (!service('user')->save($postData)) {
            return redirect()->back()->withInput();
        }
        model('\App\Models\UserDetailThematicsModel')
            ->where('user_details_id', $this->request->getPost('user_detail_id'))
            ->delete();
        if (isset($_POST['thematics'])) {
            foreach ($this->request->getPost('thematics') as $thematicId) {
                model('\App\Models\UserDetailThematicsModel')->insert([
                    'user_details_id' => $this->request->getPost('user_detail_id'),
                    'thematic_id' => $thematicId,
                ]);
            }
        }
        model('\App\Models\UserDetailTypesModel')
            ->where('user_details_id', $this->request->getPost('user_detail_id'))
            ->delete();
        if (isset($_POST['types'])) {
            foreach ($this->request->getPost('types') as $typeId) {
                model('\App\Models\UserDetailTypesModel')->insert([
                    'user_details_id' => $this->request->getPost('user_detail_id'),
                    'type_id' => $typeId,
                ]);
            }
        }
        model('\App\Models\UserDetailSectorsModel')
            ->where('user_details_id', $this->request->getPost('user_detail_id'))
            ->delete();
        if (isset($_POST['sectors'])) {
            foreach ($this->request->getPost('sectors') as $sectorId) {
                model('\App\Models\UserDetailSectorsModel')->insert([
                    'user_details_id' => $this->request->getPost('user_detail_id'),
                    'sector_id' => $sectorId,
                ]);
            }
        }

        if (empty($postData['old_subscription_start_date']) && !empty($postData['start_date'])) {
            $company = auth()->getProvider()->getUserDetails('company', $postData['id'])[0];
            $sent = service('notification')->initialize(['to' => $company->email])
                ->subject('A sua conta foi validada pelo suporte')
                ->message(view('emails/general', [
                    'title' => 'A sua conta foi validada pelo suporte',
                    'text' => 'Já pode aceder à sua conta do Radar de Fundos Europeus.<br>
                        Tem uma subscrição de ' . $postData['start_date'] . ' até ' . $postData['due_date'],
                ]))->send();
            if ($sent) {
                return redirect()->to('companies')->with('message', 'A conta foi validada com sucesso e o email foi enviado.');
            }
            return redirect()->to('companies')->with('message', 'A conta foi validada com sucesso, no entanto não foi possível notificar a conta ' . $company->email);
        }
        return redirect()->to('companies');
    }

    /**
     * Update company view
     * @param  int      $id The selected company
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['company'] = auth()->getProvider()->getUserDetails('company', $id)[0];
        if (empty($this->data['company'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['userDetailId'] = model('\App\Models\UserDetailModel')->where('user_id', $id)->first()->id;
        $this->data['userDetails'] = model('\App\Models\UserDetailModel')->where('user_id', $id)->first();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['userDetails']->thematics = array_column(
            model('\App\Models\UserDetailThematicsModel')
                ->where('user_details_id', $this->data['userDetails']->id)
                ->asArray()
                ->findAll(),
            'thematic_id');
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['userDetails']->types = array_column(
            model('\App\Models\UserDetailTypesModel')
                ->where('user_details_id', $this->data['userDetails']->id)
                ->asArray()
                ->findAll(),
            'type_id');
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['userDetails']->sectors = array_column(
            model('\App\Models\UserDetailSectorsModel')
                ->where('user_details_id', $this->data['userDetails']->id)
                ->asArray()
                ->findAll(),
            'sector_id');
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('companies/update' . $id))
            ->addCrumb($this->data['company']->email, site_url('companies/update/' . $id));

        return view('admin/companies/manage', $this->data);
    }

    /**
     * Delete company
     * @param  int    $id        The selected company
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        // delete with false means softDelete
        if (!auth()->getProvider()->delete($id, false)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a entidade');
        }

        return redirect()->back()->with('confirm', 'A entidade foi apagada com sucesso');
    }

}
