<?php

// DASHBOARD
$routes->get('/', 'Dashboard::index', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin']);
$routes->get('/release-log', 'Dashboard::releaseLog', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin']);
$routes->get('logs', 'Dashboard::logs', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin']);

// NOTICES
$routes->group('notices', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin'], static function ($routes) {
    // Show index link status
    $routes->get('index', 'Notices::index');
    // Manage pre-notices https://portaldosfundoseuropeus.pt
    $routes->group('pre-notice', static function ($routes) {
        $routes->get('/', 'Notices::preNotice');
        $routes->get('delete/(:num)', 'Notices::deletePreNotice/$1');
        $routes->get('update/(:num)', 'Notices::updatePreNotice/$1');
        $routes->post('save', 'Notices::savePreNotice');
        $routes->get('delete/(:num)', 'Notices::deletePreNotice/$1');
    });
    // Manage National notices https://portugal2030.pt
    $routes->group('national', static function ($routes) {
        $routes->get('/', 'Notices::national');
        $routes->get('info/(:num)', 'Notices::info/$1');
        $routes->get('update/(:num)', 'Notices::updateNational/$1');
        $routes->post('save', 'Notices::saveNational');
        $routes->post('global-save', 'Notices::globalSaveNational');
        $routes->get('delete/(:num)', 'Notices::deleteNational/$1');
        // Manage programs associated with a notice
        $routes->group('programs', static function ($routes) {
            $routes->get('(:num)', 'Notices::programsNational/$1');
            $routes->get('update/(:num)', 'Notices::programsUpdate/$1');
            $routes->post('save', 'Notices::programsSave');
            $routes->get('delete/(:num)', 'Notices::programsDelete/$1');
            $routes->get('thematics/(:num)', 'Notices::thematics/$1');
            $routes->post('thematics/save', 'Notices::ThematicsSave');
            $routes->get('types/(:num)', 'Notices::types/$1');
            $routes->post('types/save', 'Notices::TypesSave');
            $routes->get('sectors/(:num)', 'Notices::sectors/$1');
            $routes->post('sectors/save', 'Notices::SectorsSave');
            $routes->get('delete-img/(:num)', static function ($id) {
                model('\App\Models\NoticiesNationalExtraModel')->setUploadedImageToDelete(['id' => [$id]]);
                model('\App\Models\NoticiesNationalExtraModel')->deleteUploadedImage(['id' => [$id]]);

                return redirect()->back();
            });
        });
        // Manage documents associated with a National Notice
        $routes->group('documents', static function ($routes) {
            $routes->get('(:num)', 'Notices::documentsNational/$1');
            $routes->get('download/(:num)', 'Notices::documentsDownload/$1');
            $routes->get('update/(:num)', 'Notices::documentsUpdate/$1');
            $routes->post('save', 'Notices::documentsSave');
            // Manage texts associated with a document
            $routes->group('texts', static function ($routes) {
                $routes->get('(:num)', 'Notices::documentsTexts/$1');
                $routes->get('update/(:num)', 'Notices::documentsTextsUpdate/$1');
                $routes->post('save', 'Notices::documentsTextsSave');
                $routes->get('delete/(:num)', 'Notices::documentsTextsDelete/$1');
            });
        });
    });
    // Manage European Programs
    $routes->group('europe-programs', static function ($routes) {
        $routes->get('/', 'Program::index');
        $routes->get('insert', 'Program::insert');
        $routes->get('update/(:num)', 'Program::update/$1');
        $routes->post('save', 'Program::save');
        $routes->get('segments/(:num)', 'Program::segments/$1');
        $routes->post('segments/save', 'Program::segmentsSave');
        $routes->get('delete/(:num)', 'Program::delete/$1');
        $routes->get('delete-img/(:num)', static function ($id) {
            model('\App\Models\NoticeEuropeModel')->setUploadedImageToDelete(['id' => [$id]]);
            model('\App\Models\NoticeEuropeModel')->deleteUploadedImage(['id' => [$id]]);
            return redirect()->back();
        });
        $routes->group('documents', static function ($routes) {
            $routes->get('(:num)', 'Program::documents/$1');
            $routes->get('insert/(:num)', 'Program::DocumentsInsert/$1');
            $routes->get('update/(:num)', 'Program::DocumentsUpdate/$1');
            $routes->post('save', 'Program::DocumentsSave');
            $routes->get('delete/(:num)', 'Program::DocumentsDelete/$1');
        });
    });
    // Manage European Notices
    $routes->group('europe', static function ($routes) {
        $routes->get('/', 'Europe::index');
        $routes->get('insert', 'Europe::insert');
        $routes->get('update/(:num)', 'Europe::update/$1');
        $routes->post('save', 'Europe::save');
        $routes->get('delete/(:num)', 'Europe::delete/$1');
    });
    // Manage PRR
    $routes->group('prr', static function ($routes) {
        $routes->get('/', 'Prr::index');
        $routes->get('update/(:num)', 'Prr::update/$1');
        $routes->post('save', 'Prr::save');
        $routes->get('delete/(:num)', 'Prr::delete/$1');
        $routes->group('links', static function ($routes) {
            $routes->get('(:num)', 'Prr::links/$1');
            $routes->get('update/(:num)', 'Prr::linksUpdate/$1');
            $routes->post('save', 'Prr::linksSave');
            $routes->get('delete/(:num)', 'Prr::linksDelete/$1');
        });
    });
});

// COMPANIES
$routes->group('companies', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin'], static function ($routes) {
    $routes->get('/', 'Companies::index');
    $routes->get('pending', 'Companies::pendingCompanies');
    $routes->get('insert', 'Companies::insert');
    $routes->get('update/(:num)', 'Companies::update/$1');
    $routes->post('save', 'Companies::save');
    $routes->get('delete/(:num)', 'Companies::delete/$1');
    $routes->group('employees', static function ($routes) {
        $routes->get('(:num)', 'Employees::index/$1');
        $routes->get('insert/(:num)', 'Employees::insert/$1');
        $routes->get('update/(:num)/(:num)', 'Employees::update/$1/$2');
        $routes->post('save', 'Employees::save');
        $routes->get('delete/(:num)', 'Employees::delete/$1');
    });
    $routes->get('delete-img/(:num)', static function ($id) {
        model('\App\Models\UserDetailModel')->setUploadedImageToDelete(['id' => [$id]]);
        model('\App\Models\UserDetailModel')->deleteUploadedImage(['id' => [$id]]);
        return redirect()->back();
    });
});

// ARTICLES
$routes->group('articles', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin'], static function ($routes) {
    $routes->get('/', 'Articles::index');
    $routes->get('insert', 'Articles::insert');
    $routes->get('update/(:num)', 'Articles::update/$1');
    $routes->post('save', 'Articles::save');
    $routes->get('delete/(:num)', 'Articles::delete/$1');
    $routes->get('delete-img/(:num)', static function ($id) {
        model('\App\Models\ArticleModel')->setUploadedImageToDelete(['id' => [$id]]);
        model('\App\Models\ArticleModel')->deleteUploadedImage(['id' => [$id]]);

        return redirect()->back();
    });
});

// SETTINGS
$routes->group('settings', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin', 'filter' => 'group:admin'], static function ($routes) {
    // CRUD THEMATICS
    $routes->group('thematics', static function ($routes) {
        $routes->get('', 'Thematics::index');
        $routes->get('insert', 'Thematics::insert');
        $routes->get('update/(:num)', 'Thematics::update/$1');
        $routes->post('save', 'Thematics::save');
        $routes->get('delete/(:num)', 'Thematics::delete/$1');
    });
    // CRUD TYPES
    $routes->group('types', static function ($routes) {
        $routes->get('', 'Types::index');
        $routes->get('insert', 'Types::insert');
        $routes->get('update/(:num)', 'Types::update/$1');
        $routes->post('save', 'Types::save');
        $routes->get('delete/(:num)', 'Types::delete/$1');
    });
    // CRUD SECTORS
    $routes->group('sectors', static function ($routes) {
        $routes->get('', 'Sectors::index');
        $routes->get('insert', 'Sectors::insert');
        $routes->get('update/(:num)', 'Sectors::update/$1');
        $routes->post('save', 'Sectors::save');
        $routes->get('delete/(:num)', 'Sectors::delete/$1');
    });
    // CRUD CIMS
    $routes->group('cims', static function ($routes) {
        $routes->get('', 'Cims::index');
        $routes->get('insert', 'Cims::insert');
        $routes->get('update/(:num)', 'Cims::update/$1');
        $routes->post('save', 'Cims::save');
        $routes->get('delete/(:num)', 'Cims::delete/$1');
    });
    // CRUD CITY HALL (Municípios)
    $routes->group('city-halls', static function ($routes) {
        $routes->get('', 'CityHalls::index');
        $routes->get('insert', 'CityHalls::insert');
        $routes->get('update/(:num)', 'CityHalls::update/$1');
        $routes->post('save', 'CityHalls::save');
        $routes->get('delete/(:num)', 'CityHalls::delete/$1');
    });
    // CRUD PARISH (Juntas de frequesia)
    $routes->group('parish', static function ($routes) {
        $routes->get('', 'Parish::index');
        $routes->get('insert', 'Parish::insert');
        $routes->get('update/(:num)', 'Parish::update/$1');
        $routes->post('save', 'Parish::save');
        $routes->get('delete/(:num)', 'Parish::delete/$1');
    });
    // CRUD ORGANISMS
    $routes->group('organisms', static function ($routes) {
        $routes->get('', 'Organisms::index');
        $routes->get('insert', 'Organisms::insert');
        $routes->get('update/(:num)', 'Organisms::update/$1');
        $routes->post('save', 'Organisms::save');
        $routes->get('delete/(:num)', 'Organisms::delete/$1');
    });
    // Emails usados para pedidos de ajuda APP
    $routes->group('emails', static function ($routes) {
        $routes->get('', static function () {
            $data = [
                'activeMenu' => 'settings',
                'activeSubMenu' => 'emails',
                'breadcrumbs' => (new \ElePHPant\Breadcrumb\Breadcrumb(null))
                    ->base(site_url(), '<i class="fa-duotone fa-house"></i>')
                    ->addCrumb('Definições', site_url('settings/emails'))
                    ->addCrumb('Consultores', site_url('settings/emails')),
            ];
            return view('admin/settings/emails', $data);
        });
        $routes->post('save', static function () {
            service('settings')->set('Radar.consultingEmail', service('request')->getPost('emails'));
            return redirect()->back()->with('confirm', 'informação gravada com sucesso!');
        });
    });
});

// AUTHENTICATION
$routes->group('auth', ['namespace' => 'Admin\Controllers', 'subdomain' => 'admin'], static function ($routes) {
    $routes->get('login', 'Auth::loginView', ['filter' => \Admin\Filters\LoginRedirect::class]);
    $routes->post('login', 'Auth::loginAction', ['filter' => \Admin\Filters\LoginRedirect::class]);
    $routes->get('forgot-password', 'Auth::forgotPasswordView');
    $routes->post('forgot-password', 'Auth::forgotPasswordAction');
    $routes->get('reset-password', 'Auth::resetPasswordView', ['filter' => \App\Filters\CheckPasswordToken::class]);
    $routes->post('reset-password', 'Auth::resetPasswordAction');
    $routes->get('logout', static function () {
        auth()->logout();
        return redirect()->to('auth/login')->with('confirm', 'Terminou a sessão de administrador com sucesso');
    });
});
