### <PERSON><PERSON><PERSON> logs

    git log --pretty=format:"%h%x09%an%x09%ad%x09%s"

Depois limpar no sublime.


## CRON JOBS
    */2 * * * * php /var/www/html/spark scraper:europe >> /home/<USER>/logs/scraper_europe.log 2>&1
    */15 * * * * php /var/www/html/spark crawler:run --type europe >> /home/<USER>/logs/crawler_europe.log 2>&1
    */9 * * * * php /var/www/html/spark scraper:national >> /home/<USER>/logs/crawler_national.log 2>&1
    1-59/2 * * * * php /var/www/html/spark scraper:documents >> /home/<USER>/logs/scraper_documents.log 2>&1
    */30 * * * * php /var/www/html/spark scraper:parse-documents >> /home/<USER>/logs/parse_documents.log 2>&1
    0 */12 * * * php /var/www/html/spark crawler:run --type prr >> /home/<USER>/logs/crawler_prr.log 2>&1
    * * * * * php /var/www/html/spark crawler:prr >> /home/<USER>/logs/crawler_prr.log 2>&1
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov --type cim >> /home/<USER>/logs/basegov_cim.log 2>&1
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov --type city_hall >> /home/<USER>/logs/basegov_city_hall.log >
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov --type parish >> /home/<USER>/logs/basegov_parish.log 2>&1
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov-detail >> /home/<USER>/logs/basegov_detail.log 2>&1
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov-announcements >> /home/<USER>/logs/basegov_announcements.log>
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov-announcement-detail >> /home/<USER>/logs/basegov_announcemen>
    * 0-11,13-17,19-23 * * * php /var/www/html/spark crawler:basegov-dre >> /home/<USER>/logs/basegov_dre.log 2>&1
    0 * * * * php /var/www/html/spark basegov:recover >> /home/<USER>/logs/basegov_recover.log 2>&1
    0 10 * * * php /var/www/html/spark notify:alert-contracts >> /home/<USER>/logs/notify_alert_contracts.log 2>&1
    0 9 * * * php /var/www/html/spark notify:contracts >> /home/<USER>/logs/notify_contracts.log 2>&1
    59 23 * * * php /var/www/html/spark logs:notification
    # Alerta de hora a hora DRE
    0 * * * * php /var/www/html/spark notify:alert-dre >> /home/<USER>/logs/notify_dre.log 2>&1
    # Sync Dre RSS
    */15 * * * * php /var/www/html/spark crawler:dre >> /home/<USER>/logs/crawler_dre.log 2>&1

## DEVILBOX

No devilbox deve ter o executavel pdftotext instalado. Para tal devem correr dentro do devilbox:

    sudo apt update
    sudo apt-get install poppler-utils

