<?php

namespace App\Controllers;

use CodeIgniter\Shield\Authentication\Actions\EmailActivator;

class Auth extends BaseController
{
    /**
     * Load helpers
     * @var array
     */
    public $helpers = ['text', 'form'];

    /**
     * Register page
     * @return string
     */
    public function registerView(): string
    {
        return view('app/shield/register');
    }

    /**
     * Login page
     * @return string
     */
    public function loginView(): string
    {
        return view('app/shield/login');
    }

    /**
     * Reset Password page
     * @return string
     */
    public function forgotPasswordView(): string
    {
        return view('shared/shield/forgotPassword');
    }

    /**
     * Reset Password page
     * @return string
     */
    public function resetPasswordView(): string
    {
        $this->data['userId'] = $this->request->getGet('u');
        $this->data['token'] = $this->request->getGet('t');

        return view('shared/shield/resetPassword', $this->data);
    }

    /**
     * Reset password
     * @return string
     */
    public function manage(): string
    {
        $this->data['user'] = auth()->getProvider()->getUserDetails(null, user_id())[0];
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $key => $district) {
            $this->data['districts'][$key]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['userDetailId'] = model('\App\Models\UserDetailModel')->where('user_id', user_id())->first()->id;

        return view('app/shield/manage', $this->data);
    }

    /**
     * Handle app registration
     * @return object CodeIgniter\HTTP\RedirectResponse
     */
    public function registerAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        // Get the credentials for register
        $postData = $this->request->getPost();
        // Set group
        $postData['group'] = 'company';
        $postData['active'] = 0;
        if (!service('user')->save($postData)) {
            return redirect()->back()->withInput()->with('error', 'Houve um problema ao criar a sua conta!');
        }

        // generated user identity (auth_identities)
        $userIdentity = auth()->getProvider()->findById(auth()->getProvider()->getInsertID())->getEmailIdentity();

        // generate token to send email
        $token = service('user')->generateToken($userIdentity->id);
        if (!$token) {
            service('user')->delete($userIdentity->id);
            return redirect()->to('auth/login')->with('error', 'Houve um problema ao validar a sua conta.');
        }
        $linkBtn = 'auth/validate-account/?u=' . $userIdentity->user_id . '&t=' . $token;

        $sent = service('notification')->initialize(['to' => $postData['email']])
            ->subject('Confirmação de conta Radar Fundos Europeus')
            ->message(view('emails/validate-account', ['link' => $linkBtn]))
            ->send();

        if (!$sent) {
            service('user')->delete($userIdentity->id);
            return redirect()->to('auth/login')->with('error', 'Houve um problema ao enviar o seu email de validação de conta. Tente mais tarde ou contacte o suporte!');
        }

        return redirect()->to('auth/login')->with('confirm', 'A sua conta foi criada com sucesso! Por favor veja a sua caixa de email de modo a confirmar o email.');
    }

    /**
     * Handle app login
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function loginAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        // Get the credentials for login and clean empty values (NIF or Email)
        $loginData = array_filter($this->request->getPost(), 'strlen');
        // set remember before login attempt
        $remember = $loginData['chk_remember'] ?? 0;

        unset($loginData['chk_remember']);
        $loginAttempt = auth()->remember((bool) $remember)->attempt($loginData);
        if (!$loginAttempt->isOK()) {
            return redirect()->back()->with('error', $loginAttempt->reason());
        }

        return redirect()->to('/');
    }

    /**
     * Handle app forgotPassword
     * @todo send email to user $user->getEmailIdentity()->secret with token $tokenGenerated
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function forgotPasswordAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        $user = auth()->getProvider()->findByCredentials($this->request->getPost());
        if (!$user) {
            return redirect()->back()->with('error', 'Não existe nenhuma conta com o e-mail inserido');
        }

        $tokenGenerated = service('user')->generateForgotPasswordToken($user);
        if (!$tokenGenerated) {
            return redirect()->back()->with('error', 'Houve um problema ao recuperar a sua conta!');
        }

        // button link for user to access the form
        $btnLink = 'auth/reset-password/?u=' . $user->id . '&t=' . $tokenGenerated;
        $sent = service('notification')->initialize(['to' => $user->getEmailIdentity()->secret])
            ->subject('Confirmação de conta Radar Fundos Europeus')
            ->message(view('emails/reset-password', ['link' => $btnLink]))
            ->send();

        if (!$sent) {
            return redirect()->to('auth/login')->with('error', 'Houve um problema ao enviar o seu email de recuperação de password. Tente mais tarde ou contacte o suporte!');
        }

        return redirect()->to('auth/login')->with('confirm', 'Por favor verifique a sua caixa de email, deverá finalizar o processo de recuperação de password num prazo máximo de 30 minutos.');
    }

    /**
     * Reset password
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function resetPasswordAction(): \CodeIgniter\HTTP\RedirectResponse
    {
        $postData = $this->request->getPost();
        if (!service('user')->updatePassword($postData)) {
            return redirect()->back();
        }

        return redirect()->to('auth/login')->with('confirm', 'A sua password foi alterada com sucesso');
    }

    /**
     * Validate account with verification code sent by email
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function validateAccount(): \CodeIgniter\HTTP\RedirectResponse
    {
        $userId = $this->request->getGet('u');
        $token = $this->request->getGet('u');

        if (!$userId && !$token) {
            return redirect()->to('auth/login')->with('error', 'Houve um problema na validação de conta devido à falta de dados necessários');
        }

        if (!service('user')->validateAccount($userId, true)) {
            return redirect()->to('auth/login')->with('error', 'Ńão foi possível validar a sua conta. Entre em contacto com o técnico');
        }

        return redirect()->to('auth/login')->with('confirm', 'A sua conta foi validada com sucesso. Terá de esperar pela aprovação do administrador');
    }

    /**
     * Update user action after post
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!service('user')->save($this->request->getPost())) {
            return redirect()->back()->withInput();
        }

        return redirect()->to('auth/manage');
    }

}
