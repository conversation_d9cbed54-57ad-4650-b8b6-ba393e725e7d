<?php

namespace App\Controllers;

use \NumberFormatter;

class Bookmarks extends BaseController
{
    /**
     * Set of available bookmark types
     * @var array
     */
    protected array $bookmarkTypes = [
        'national' => 'extra_id',
        'prr' => 'prr_id',
        'europe' => 'europe_id',
    ];
    /**
     * Set of availabe urls
     * @var array
     */
    protected array $urls = [
        'national' => 'notices/detail/',
        'prr' => 'notices/prr/detail/',
        'europe' => 'notices/europe/detail/',
    ];

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);
        $this->data['page'] = 'bookmarks';
    }

    /**
     * Show a list of national notices
     * @return string The view with eveything
     */
    public function index(): string
    {
        $this->data['programCodes'] = model('NoticiesNationalExtraModel')
            ->distinct()->select('programaOperacionalDesignacao')
            ->orderBy('programaOperacionalDesignacao', 'ASC')
            ->findAll();
        $this->data['notices'] = service('national')->bookmarks()->findAll($this->request->getGet());

        return view('app/notices/index', $this->data);
    }

    /**
     * Show all the prr notices
     * @return string The view
     */
    public function prr(): string
    {
        $this->data['notices']['data'] = model('\App\Models\NoticePrrModel')
            ->withIconAndBookmark()->bookmarked(true)->filter($this->request->getGet())
            ->where('status', 'active')->paginate(15);
        $this->data['notices']['pagination'] = model('\App\Models\NoticePrrModel')->pager;

        return view('app/notices/prr', $this->data);
    }

    /**
     * Show european notices
     * @return string
     */
    public function europe(): string
    {
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->orderBy('program', 'ASC')->findAll();
        $this->data['notices']['data'] = model('\App\Models\NoticeEuropeModel')->withIconAndBookmark()
            ->bookmarked(true)->filter($this->request->getGet())->paginate(15);
        $this->data['notices']['pagination'] = model('\App\Models\NoticeEuropeModel')->pager;

        return view('app/notices/europe', $this->data);
    }

    /**
     * Save bookmark for user
     * @param  int    $programId                   The selected program
     * @return object \CodeIgniter\HTTP\Response
     */
    public function save(int $programId): \CodeIgniter\HTTP\Response
    {
        $type = (isset($_GET['type'])) ? $_GET['type'] : 'national';
        $data = [
            'type' => $type,
            'user_id' => auth()->user()->id,
            $this->bookmarkTypes[$type] => $programId,
        ];
        model('\App\Models\BookmarkModel')->where($data)->delete();
        $data['url'] = site_url($this->urls[$type] . $programId);
        model('\App\Models\BookmarkModel')->save($data);

        return redirect()->back();
    }

    /**
     * Remove a program from a user bookmarks
     * @param  int    $programId                   The selected program
     * @return object \CodeIgniter\HTTP\Response
     */
    public function remove(int $programId): \CodeIgniter\HTTP\Response
    {
        $type = (isset($_GET['type'])) ? $_GET['type'] : 'national';
        $data = [
            'type' => $type,
            'user_id' => auth()->user()->id,
            $this->bookmarkTypes[$type] => $programId,
        ];
        model('\App\Models\BookmarkModel')->where($data)->delete();

        return redirect()->back();
    }

}
