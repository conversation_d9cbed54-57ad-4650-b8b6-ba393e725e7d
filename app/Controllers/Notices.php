<?php

namespace App\Controllers;

use Dompdf\Dompdf;
use \NumberFormatter;

class Notices extends BaseController
{

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);
        $this->data['page'] = 'notice';
        $this->data['formatter'] = new NumberFormatter('pt_PT', NumberFormatter::CURRENCY);
        $this->data['layout'] = 'layouts/app';
        if (!auth()->user()) {
            $this->data['layout'] = 'layouts/app-no-auth';
        }
        helper('slug');
    }

    /**
     * Show a list of notices
     * @todo Filters / pagination / new view, for now we're just showing the difference bettwen Europe and National
     * @param  string $type The type we want
     * @return string The view with eveything
     */
    public function index(): string
    {
        $this->data['programCodes'] = model('\App\Models\NoticiesNationalExtraModel')
            ->distinct()->select('programaOperacionalDesignacao')
            ->orderBy('programaOperacionalDesignacao', 'ASC')
            ->findAll();
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $d => $district) {
            $this->data['districts'][$d]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['notices'] = service('national')->findAll($this->request->getGet());
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();

        return view('app/notices/index', $this->data);
    }

    /**
     * Show european notices whithout search
     * @return string
     */
    public function europe(): string
    {
        $this->data['organisms'] = model('\App\Models\OrganismModel')->orderBy('entity', 'ASC')->findAll();
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['programs'] = model('\App\Models\NoticeEuropeProgramModel')->orderBy('program', 'ASC')->findAll();
        $this->data['notices']['data'] = model('\App\Models\NoticeEuropeModel')->withIconAndBookmark()
            ->filter($this->request->getGet())->paginate(15);
        $this->data['notices']['pagination'] = model('\App\Models\NoticeEuropeModel')->pager;

        return view('app/notices/europe', $this->data);
    }

    /**
     * Show the detail of a european notice / program
     * @param  int    $noticeId The selected notice
     * @return string show all the details
     */
    public function europeDetail(int $noticeId): string
    {
        $this->data['notice'] = model('\App\Models\NoticeEuropeModel')->where('id', $noticeId)->first();
        if (!isset($this->data['notice']->id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['program'] = model('\App\Models\NoticeEuropeProgramModel')
            ->where('id', $this->data['notice']->program_id)->first();
        $this->data['organism'] = model('\App\Models\OrganismModel')
            ->where('id', $this->data['program']->organism_id)->first();
        $this->data['documents'] = model('\App\Models\NoticiesEuropeDocumentModel')->where('notice_id', $noticeId)->findAll();
        $this->data['isBookmarked'] = false;
        $this->data['seo'] = [
            'title' => 'Radar Fundos Europeus | ' . $this->data['notice']->name,
            'description' => word_limiter($this->data['program']->description, 30),
            'keywords' => '',
        ];

        return view('app/notices/detail-europe', $this->data);
    }

    /**
     * Show all the prr notice so we can filter them
     * @return string The view
     */
    public function prr(): string
    {
        $this->data['names'] = model('\App\Models\NoticePrrModel')->distinct()->select('name')
            ->orderBy('name', 'ASC')->findAll();
        $this->data['cims'] = model('\App\Models\CimModel')->orderBy('name', 'ASC')->findAll();
        $this->data['sectors'] = model('\App\Models\SectorModel')->orderBy('name', 'ASC')->findAll();
        $this->data['thematics'] = model('\App\Models\ThematicModel')->orderBy('name', 'ASC')->findAll();
        $this->data['types'] = model('\App\Models\TypeModel')->orderBy('name', 'ASC')->findAll();
        $this->data['districts'] = model('\App\Models\DistrictModel')->orderBy('name', 'ASC')->findAll();
        foreach ($this->data['districts'] as $d => $district) {
            $this->data['districts'][$d]->cities = model('\App\Models\CityModel')
                ->where('district_id', $district->id)
                ->orderBy('name', 'ASC')
                ->findAll();
        }
        $this->data['notices']['data'] = model('\App\Models\NoticePrrModel')
            ->withIconAndBookmark()->filter($this->request->getGet())
            ->where('status', 'active')->paginate(15);
        $this->data['notices']['pagination'] = model('\App\Models\NoticePrrModel')->pager;

        return view('app/notices/prr', $this->data);
    }

    /**
     * Show details to PRR notice
     * @param  int    $id The selected notice
     * @return string The view
     */
    public function prrDetail(int $id): string
    {
        $this->data['notice'] = model('\App\Models\NoticePrrModel')->where('id', $id)->first();
        if (!isset($this->data['notice']->id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['links'] = model('\App\Models\NoticePrrLinkModel')->where('notice_id', $id)->findAll();
        $this->data['seo'] = [
            'title' => 'Radar Fundos Europeus | ' . $this->data['notice']->name,
            'description' => word_limiter($this->data['notice']->texts, 30),
            'keywords' => '',
        ];

        return view('app/notices/prr-detail', $this->data);
    }

    /**
     * Show the selected notice or 404
     * @param  int         $programId The selected program
     * @param  string|null $slug      The slug, not used just here for SEO
     * @return string      The view
     */
    public function detail(int $programId, string $slug = null): string
    {
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')
            ->where('id', $programId)
            ->first();
        if (!isset($this->data['program']->notice_id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')
            ->where('id', $this->data['program']->notice_id)
            ->first();
        if (empty($this->data['notice']) || $this->data['notice']->status === 'inactive') {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['texts'] = model('\App\Models\NoticeNationalTextModel')
            ->where(['notice_id' => $this->data['notice']->id, 'status' => 'active'])
            ->findAll();
        $this->data['documents'] = model('\App\Models\NoticiesNationalDocumentModel')
            ->where(['notice_id' => $this->data['notice']->id, 'status' => 'active', 'local_document' => 1])
            ->findAll();
        $this->data['isBookmarked'] = count(model('\App\Models\BookmarkModel')->where([
            'user_id' => auth()->user()->id ?? null,
            'extra_id' => $programId,
            'type' => 'national',
        ])->findAll());
        $this->data['seo'] = [
            'title' => 'Radar Fundos Europeus | ' . $this->data['notice']->designacaoPT,
            'description' => $this->data['program']->programaOperacionalDesignacao,
            'keywords' => $this->data['notice']->classificacaoAvisoDesignacao,
        ];

        return view('app/notices/detail', $this->data);
    }

    /**
     * Download a local document
     * @param  int    $id                                 The selected document
     * @return object CodeIgniter\HTTP\DownloadResponse
     */
    public function documentsDownload(int $id): \CodeIgniter\HTTP\DownloadResponse
    {
        $this->data['document'] = model('\App\Models\NoticiesNationalDocumentModel')->where('id', $id)->first();
        if (empty($this->data['document'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return $this->response->download(WRITEPATH . '/downloads/' . $this->data['document']->documentoDesignacao, null);
    }

    /**
     * download generated PDF with all details about selected national program
     * @param  int    $id The selected program
     * @return void
     */
    public function exportNational(int $id): void
    {
        $this->data['program'] = model('\App\Models\NoticiesNationalExtraModel')->where('id', $id)->first();
        if (!isset($this->data['program']->notice_id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['notice'] = model('\App\Models\NoticeNationalModel')
            ->where('id', $this->data['program']->notice_id)->first();
        if (empty($this->data['notice']) || $this->data['notice']->status === 'inactive') {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['texts'] = model('\App\Models\NoticeNationalTextModel')
            ->where(['notice_id' => $this->data['notice']->id, 'status' => 'active'])->findAll();
        $this->data['documents'] = model('\App\Models\NoticiesNationalDocumentModel')
            ->where(['notice_id' => $this->data['notice']->id, 'status' => 'active', 'local_document' => 1])->findAll();

        $userDetails = model('\App\Models\UserDetailModel')->where('user_id', auth()->user()->id)->first();
        if ($userDetails->user_type === 'company') {
            $this->data['companyLogo'] = $userDetails->image;
        } else {
            $this->data['companyLogo'] = model('\App\Models\UserDetailModel')
                ->where('user_id', $userDetails->company_id)->first()->image;
        }

        $dompdf = new Dompdf();
        $dompdf->loadHtml(view('app/notices/national-export', $this->data));
        $dompdf->render();
        $dompdf->set_option('isRemoteEnabled', true);
        $dompdf->set_option('isHtml5ParserEnabled', true);
        $dompdf->setPaper('A4', 'potrait');
        $dompdf->stream('aviso-' . $id . '-' . date('Y-m-d') . '.pdf', ['Attachment' => 1]);
    }

    /**
     * download generated PDF with all details about selected PRR
     * @param  int    $id The selected program
     * @return void
     */
    public function exportPrr(int $id): void
    {
        $this->data['notice'] = model('\App\Models\NoticePrrModel')->where('id', $id)->first();
        if (!isset($this->data['notice']->id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $userDetails = model('\App\Models\UserDetailModel')->where('user_id', auth()->user()->id)->first();
        if ($userDetails->user_type === 'company') {
            $this->data['companyLogo'] = $userDetails->image;
        } else {
            $this->data['companyLogo'] = model('\App\Models\UserDetailModel')->where('user_id', $userDetails->company_id)->first()->image;
        }
        $dompdf = new Dompdf();
        $dompdf->loadHtml(view('app/notices/prr-export', $this->data));
        $dompdf->render();
        $dompdf->set_option('isRemoteEnabled', true);
        $dompdf->set_option('isHtml5ParserEnabled', true);
        $dompdf->setPaper('A4', 'potrait');
        $dompdf->stream('aviso-' . $id . '-' . date('Y-m-d') . '.pdf', ['Attachment' => 1]);
    }
}
