<?php

namespace App\Controllers;

use App\Controllers\BaseController;

class Articles extends BaseController
{
    /**
     * Article Model
     * @var object
     */
    protected $article;

    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['page'] = 'article';
        $this->article = new \App\Models\ArticleModel();
    }

    /**
     * Show the list of articles
     * @return string
     */
    public function index(): string
    {
        $this->data['activeSubMenu'] = 'index';

        $this->data['articles'] = $this->article
            ->where('schedule  <=', date('Y-m-d H:i:s'))
            ->searchBy($this->request->getGet('search'))
            ->orderBy('schedule', 'DESC')->paginate();

        $this->data['pager'] = $this->article->pager;

        return view('app/articles/index', $this->data);
    }

    /**
     * Show the selected article
     * @param  string $slug The selected article
     * @return string The view
     */
    public function detail(string $slug): string
    {
        $this->data['article'] = $this->article->where('slug', $slug)->first();
        if (empty($this->data['article'])) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }

        return view('app/articles/detail', $this->data);
    }
}
