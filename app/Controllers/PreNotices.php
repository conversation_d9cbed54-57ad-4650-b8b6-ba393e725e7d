<?php

namespace App\Controllers;

use \NumberFormatter;

class PreNotices extends BaseController
{

    public function initController(\CodeIgniter\HTTP\RequestInterface $request, \CodeIgniter\HTTP\ResponseInterface $response, \Psr\Log\LoggerInterface $logger)
    {
        parent::initController($request, $response, $logger);
        $this->data['page'] = 'pre-notice';
        helper('slug');
    }

    /**
     * Show a list of pre-notices
     * @return string The view with eveything
     */
    public function index(): string
    {
        $this->data['notices'] = [
            'data' => model('\App\Models\PreNoticeModel')
                ->filter($this->request->getGet())
                ->where('status', 'active')
                ->paginate(15),
            'pagination' => model('\App\Models\PreNoticeModel')->pager,
        ];

        return view('app/pre-notices/index', $this->data);
    }

    /**
     * Show the selected notice or 404
     * @param  int         $id   The selected pre-notice
     * @param  string|null $slug The slug, not used just here for SEO
     * @return string      The view
     */
    public function detail(int $id, string $slug = null): string
    {
        $this->data['notice'] = model('\App\Models\PreNoticeModel')
            ->where(['id' => $id, 'status' => 'active'])
            ->first();
        if (!isset($this->data['notice']->id)) {
            throw \CodeIgniter\Exceptions\PageNotFoundException::forPageNotFound();
        }
        $this->data['areas'] = array_column(model('\App\Models\PreNoticeAreaModel')->select('name')
                ->join('nt_areas', 'nt_prenotices_areas.area_id = nt_areas.id')
                ->where('europe_notice_id', $id)
                ->orderBy('name', 'ASC')
                ->asArray(true)
                ->findAll(), 'name');

        $this->data['areas'] = implode(', ', $this->data['areas']);
        $this->data['externalLinks'] = model('\App\Models\PreNoticeLinkModel')->where('notice_id', $id)->findAll();

        return view('app/pre-notices/detail', $this->data);
    }

}
