<?php

namespace App\Controllers;

class Dashboard extends BaseController
{
    public function index(): string | \CodeIgniter\HTTP\RedirectResponse
    {
        $filters = $this->request->getGet();

        // new notices
        $this->data['newNotices'] = [
            [
                'name' => 'Nacionais',
                'y' => model('\App\Models\NoticeNationalModel')->filter($filters)->where('status', 'active')->countAllResults(),
            ],
            [
                'name' => 'Regionais',
                'y' => model('\App\Models\NoticeNationalModel')->filter($filters)->getRegionalNotices($filters)->countAllResults(),
            ],
            [
                'name' => 'Pré-Avisos',
                'y' => model('\App\Models\PreNoticeModel')->filter($filters)->where('status', 'active')->countAllResults(),
            ],
        ];
        // regional programs
        $this->data['regionalPrograms'] = model('\App\Models\NoticiesNationalExtraModel')->filter($filters)->groupByRegionalPrograms()->findAll();
        // investment types
        $this->data['investmentTypes'] = model('\App\Models\NoticiesNationalExtraModel')->filter($filters)->groupByInvestmentTypes()->findAll();
        // pre notices by area
        $this->data['preNoticesByArea'] = model('\App\Models\PreNoticeModel')->where('status', 'active')->filter($filters)->groupByArea()->findAll();
        // pre notices by area
        $this->data['preNoticesByTag'] = model('\App\Models\PreNoticeModel')->where('status', 'active')->filter($filters)->groupByTag()->findAll();

        return view('app/dashboard/index', $this->data);
    }
}
