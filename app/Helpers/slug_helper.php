<?php

if (!function_exists('slug')) {
    function slug(string $string): string
    {
        $slug = strtolower(trim(preg_replace('/[\s-]+/', '-',
            preg_replace('/[^A-Za-z0-9-]+/', '-',
                preg_replace('/[&]/', 'and', preg_replace('/[\']/', '',
                    iconv('UTF-8', 'ASCII//TRANSLIT', $string))))), '-'));

        return (strlen($slug) > 255) ? substr($slug, 0, 255) : $slug;
    }
}
