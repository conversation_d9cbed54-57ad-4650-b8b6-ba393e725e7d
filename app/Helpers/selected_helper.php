<?php

if (!function_exists('selected')) {
    /**
     * Helper to set the selected value of a given option in a select input
     * @param  array  $fields The fields (normally in the get array)
     * @param  string $field  The current field we're validating
     * @param  string $value  The current value of said field
     * @return string Empty or selected
     */
    function selected(array $fields, string $field, string $value): string
    {
        if (isset($fields[$field]) && $fields[$field] === $value) {
            return ' selected ';
        }

        return '';
    }
}
if (!function_exists('checked')) {
    /**
     * Helper to set the selected value of a given option in a select input
     * @param  array  $fields The fields (normally in the get array)
     * @param  string $field  The current field we're validating
     * @param  string $value  The current value of said field
     * @return string Empty or selected
     */
    function checked(array $fields, string $field, string $value): string
    {
        if (isset($fields[$field]) && in_array($value, $fields[$field])) {
            return ' checked ';
        }

        return '';
    }

}
