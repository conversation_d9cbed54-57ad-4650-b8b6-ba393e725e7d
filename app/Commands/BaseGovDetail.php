<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use GuzzleHttp\Client;

class BaseGovDetail extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:basegov-detail';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Sync data from base.gov all the data from the items that were selected';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:basegov-detail';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $item = model('\Basegov\Models\BaseGovModel')->where('status', 'new')->orderBy('created_at', 'ASC')->first();

        if (empty($item)) {
            CLI::error('No new data to sync');
            exit;
        }
        try {
            $originalData = $this->getData($item->id);
        } catch (\Exception $e) {
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        $contract = (array) $originalData;
        $contract['publicationDate'] = $contract['signingDate'] = null;
        $contract['status'] = 'finished';
        if (isset($originalData->publicationDate) && !is_null($originalData->publicationDate)) {
            $contract['publicationDate'] = date('Y-m-d', strtotime($originalData->publicationDate));
        }
        if (!is_null($originalData->signingDate)) {
            $contract['signingDate'] = date('Y-m-d', strtotime($originalData->signingDate));
        }
        unset($contract['id'], $contract['contracting'], $contract['contracted'], $contract['groupMembers']);
        // Update Contract
        try {
            if (!model('\Basegov\Models\BaseGovModel')->skipValidation(true)->update($item->id, $contract)) {
                log_message('warning', 'Problem while updating contract id: ' . $item->id);
                CLI::error('Problem while updating contract id: ' . $item->id);
            }
        } catch (\Exception $e) {
            log_message('warning', 'Problem while saving data from baseGov with id' . $item->id);
            log_message('error', $e->getMessage());
            CLI::error($e->getMessage());
            model('\Basegov\Models\BaseGovModel')->update($item->id, ['status' => 'finished']);
            exit;
        }

        // Insert documents
        foreach ($originalData->documents as $document) {
            model('\Basegov\Models\BaseGovDocumentModel')->insert([
                'id' => $document->id,
                'base_gov_id' => $item->id,
                'description' => $document->description
            ]);
        }
        // Insert contestants
        foreach ($originalData->contestants as $contestant) {
            model('\Basegov\Models\BaseGovContestantModel')->insert([
                'remote_base_gov_id' => $contestant->id,
                'base_gov_id' => $item->id,
                'nif' => $contestant->nif,
                'description' => $contestant->description,
                'created_at' => date('Y-m-d H:s:i')
            ]);
        }
        // Insert invitees
        foreach ($originalData->invitees as $invitee) {
            model('\Basegov\Models\BaseGovInviteeModel')->insert([
                'remote_base_gov_id' => $invitee->id,
                'base_gov_id' => $item->id,
                'nif' => $invitee->nif,
                'description' => $invitee->description,
                'created_at' => date('Y-m-d H:s:i')
            ]);
        }

        CLI::write('Process was finished for contract id ' . $item->id, 'green');
    }

    /**
     * Get the data from the base.gov service
     * @param  int    $id The selected data
     * @return object The data or null
     */
    protected function getData(int $id)
    {
        $client = new Client([
            'base_uri' => 'https://www.base.gov.pt',
            'timeout' => 60.0
        ]);
        $r = $client->request('POST', 'https://www.base.gov.pt/Base4/pt/resultados/', [
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.361675787112'
            ],
            'form_params' => [
                'type' => 'detail_contratos',
                'version' => '112.0',
                'id' => $id
            ]
        ]);
        $body = $r->getBody();

        return json_decode($body);
    }

}
