<?php

namespace App\Commands;

use App\Models\CrawlerLinkModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class Scraper extends BaseCommand
{

    protected $group = 'Crawler';
    protected $name = 'scraper:pre-notices';
    protected $description = 'Get content from previous indexed urls from portaldosfundoseuropeus.pt';
    protected $usage = 'scraper:pre-notices';
    protected $arguments = [];
    protected $options = [];

    /**
     * save data from the various sources
     * @todo National / Tenders
     * @param array $params Type of data
     */
    public function run(array $params)
    {
        $crawlerLinkModel = new CrawlerLinkModel();
        $url = $crawlerLinkModel->where(['type' => 'pre-notice', 'status' => 'todo'])->first();
        if (!$this->preNotice($url)) {
            $crawlerLinkModel->update($url->id, ['status' => 'done']);
            log_message('error', 'There was an error while trying to save the Notice');
            CLI::error('There was an error while trying to save the Notice');
            exit;
        }

        $crawlerLinkModel->update($url->id, ['status' => 'done']);
        CLI::write('Your Notice was saved!', 'green');
    }

    /**
     * Save european data from a given url
     * @param  object $url The selected url
     * @return bool
     */
    protected function preNotice(object $url): bool
    {
        $web = new \Spekulatius\PHPScraper\PHPScraper;

        CLI::write('Link to scrap: ' . $url->link);
        $web->go($url->link);
        $texts = $web->filterTexts("//*[@class='et_pb_text_inner']");
        $links = $web->links;
        $data = [
            'link_id' => $url->id,
            'origin' => $url->link,
            'title' => $texts[0] ?? null,
            'tags' => $texts[1] ?? null,
            'area' => $texts[3] ?? null,
            'description' => $texts[5] ?? null,
            'dates' => trim(str_replace('Período de aplicação', '', $texts[6] ?? '')),
            'more_info' => [
                $links[5] ?? null,
                $links[6] ?? null,
            ],
        ];
        if (!service('prenotice')->create($data)) {
            return false;
        }

        return true;
    }
}
