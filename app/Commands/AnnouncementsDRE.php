<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use \App\Models\DreItemModel;
use \Exception;
use \SimpleXMLElement;

class AnnouncementsDRE extends BaseCommand
{
    protected $rss = 'https://files.dre.pt/rss/serie2&parte=l-html.xml';
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:dre';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Crawl the DRE RSS to for new announcements';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:dre';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $dreModel = new DreItemModel();
        try {
            $rssXML = file_get_contents($this->rss);
            $array = json_decode(json_encode(simplexml_load_string(file_get_contents($this->rss), "SimpleXMLElement", LIBXML_NOCDATA)), true);
        } catch (Exception $e) {
            log_message('critical', 'We cannot get info from the DRE RSS feed.');
            CLI::error('We cannot get info from the DRE RSS feed.');
            exit;
        }
        if (empty($array['channel']['item'])) {
            CLI::error('Nothing in the rss feed');
            exit;
        }

        foreach ($array['channel']['item'] as $dre) {
            if (empty($dreModel->where('link', $dre['link'])->first())) {
                $dreModel->insert($dre);
                CLI::write('New: ' . $dre['title'], 'green');
            } else {
                CLI::write('Skipped: ' . $dre['title'], 'yellow');
            }
        }
        CLI::write('Sync finished without any issues.', 'green');
    }
}
