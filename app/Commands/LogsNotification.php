<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class LogsNotification extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'logs:notification';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Notify developer team on all the logs created yesterday';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'command:name [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $folder = WRITEPATH . 'logs/';
        $logFile = $folder . 'log-' . date('Y-m-d') . '.log';
        if (!file_exists($logFile)) {

            exit;
        }
        $myfile = fopen($logFile, 'r') or die('Unable to open file!');
        $contents = fread($myfile, filesize($folder . 'log-' . date('Y-m-d') . '.log'));
        fclose($myfile);

        $sent = service('notification')->initialize([
            'to' => '<EMAIL>,<EMAIL>,<EMAIL>'
        ])->subject('Logs - Radar Fundos Europeus')
            ->message(nl2br($contents))
            ->send();
        if ($sent) {
            CLI::write(date('Y-m-d H:i:s') . ' - Notification with all the logs sent.', 'green');
        } else {
            log_message('critical', 'There was a problem while trying to send the logs.');
            CLI::error(date('Y-m-d H:i:s') . ' - There was a problem while trying to send the logs.');
        }
    }
}
