<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use DreApp\Libraries\SettingsCrud;

class DreManageEmails extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'dre:emails';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Manage the emails that are being used do send the notification for DRE';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'dre:emails [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = ['list', 'remove', 'add', 'set', 'update', 'email'];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = ['-l', '-r', '-a', '-s', '-u', '-e'];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        // RESET - There's a new set of recipient emails to manage
        if (isset($params['s'])) {
            service('settings')->set('Dre.emails', []);
            CLI::write('Reset the set of recipient emails', 'green');
            exit;
        }
        // Add email
        if (isset($params['a']) && !empty($params['a'])) {
            if (SettingsCrud::add('Dre.emails', $params['a'])) {
                CLI::error('Error in adding the email ' . $params['a'] . ' to our set of recipient emails');

                exit;
            }
            CLI::write('Added the email ' . $params['a'] . ' to our set of recipient emails', 'green');

            exit;
        }
        // Update email
        if (isset($params['u']) && !empty($params['u']) && isset($params['e']) && !empty($params['e'])) {
            if (SettingsCrud::update('Dre.emails', $params['u'], $params['e'])) {
                CLI::error('Error in updating to email ' . $params['e'] . ' in our set of recipient emails');

                exit;
            }
            CLI::write('Updated to email ' . $params['e'] . ' in our set of recipient emails', 'green');

            exit;
        }
        // Remove email based on the position on the array when we list email
        if (isset($params['r']) && !is_null($params['r']) && $params['r'] !== '') {
            if (SettingsCrud::delete('Dre.emails', $params['r'])) {
                CLI::error('Error in removing the email from the list');

                exit;
            }
            CLI::write('Removed the email from the list', 'green');
            exit;
        }
        // List recipient emails
        if (isset($params['l'])) {
            $emailData = SettingsCrud::get('Dre.emails', true);
            CLI::table($emailData['tbody'], $emailData['thead']);

            exit;
        }
        CLI::error('Not a valid command, you should use the flags -a -r or -l');
    }
}
