<?php

namespace App\Commands;

use Basegov\Models\BaseGovAlertModel;
use Basegov\Models\BaseGovEntityModel;
use Basegov\Models\BaseGovModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AlertContracts extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:alert-contracts';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Notify all emails that were set for new contracts!';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:alert-contracts';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $contractModel = new BaseGovModel();
        $alertModel = new BaseGovAlertModel();
        $entityModel = new BaseGovEntityModel();
        $now = date('Y-m-d H:i:s');
        $alerts = $alertModel->where(['last_check <' => $now, 'type' => 'contracts'])->findAll();

        foreach ($alerts as $alert) {
            $contractModel->where('created_at >=', $alert->last_check);
            $contractModel->where('created_at <=', $now);
            $entities = array_column(
                $entityModel->asArray()->where('list_id', $alert->list_id)->findAll(), 'vat'
            );
            $tags = explode(',', $alert->tags);
            if (!empty($cpvs)) {
                $contractModel->groupStart();
                foreach ($entities as $entity) {
                    $contractModel->orLike('vat', $entity);
                }
                $contractModel->groupEnd();
            }
            if (!empty($tags)) {
                $contractModel->groupStart();
                foreach ($tags as $tag) {
                    if (!empty($tag)) {
                        $contractModel->orLike('objectBriefDescription', trim($tag));
                        $contractModel->orLike('description', trim($tag));
                    }
                }
                $contractModel->groupEnd();
            }
            $data['contracts'] = $contractModel->findAll();
            if (empty($data['contracts'])) {
                CLI::error('No new alerts for the following emails: ' . $alert->emails);
                $alertModel->skipValidation(true)->update($alert->id, ['last_check' => $now]);
                continue;
            }
            $sent = service('notification')->initialize(['to' => $alert->emails])
                ->subject('Contratos Públicos')
                ->message(view('emails/alert-contracts', $data))
                ->send();
            if ($sent) {
                CLI::write('Notifications sent to the following emails: ' . $alert->emails, 'green');
            } else {
                log_message('critical', 'We had a serious problem while trying to send notifications to these emails: ' . $alert->emails);
                CLI::error('We had a serious problem while trying to send notifications to these emails: ' . $alert->emails);
            }

            $alertModel->skipValidation(true)->update($alert->id, ['last_check' => $now]);
        }
    }

}
