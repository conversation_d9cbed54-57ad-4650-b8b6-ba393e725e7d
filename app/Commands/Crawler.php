<?php

namespace App\Commands;

use App\Models\CrawlerLinkModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use \Exception;

class Crawler extends BaseCommand
{
    protected $group = 'Crawler';
    protected $name = 'crawler:run';
    protected $description = 'Syncs new links crawler:run --type [pre-notice, national, tenders, prr]';
    /**
     * List of sources to collect
     * @var array
     */
    protected $sources = [
        'pre-notice' => 'https://portaldosfundoseuropeus.pt/project/',
        'tenders' => 'https://ec.europa.eu/info/funding-tenders/opportunities/data/topic-list.html',
        'national' => 'https://portugal2030.pt/aviso-sitemap.xml', // Do not use for now
        'prr' => 'https://recuperarportugal.gov.pt/candidatura-sitemap.xml',
    ];

    /**
     * Run the crawler on a given source
     * @param array $params List of params to use they must exist on $this->sources
     */
    public function run(array $params)
    {
        if (!isset($params['type'])) {
            CLI::error('You must set a --type in your command');
            exit;
        }
        if (!isset($this->sources[$params['type']])) {
            CLI::error('Type must be europe, national or tenders');
            exit;
        }
        // National has its own function
        if (in_array($params['type'], ['national', 'prr'])) {
            $this->syncSitemap($params['type']);
            exit;
        }

        $url = $this->sources[$params['type']];
        $linkDepth = 1;
        // Initiate crawl, by default it will use http client (GoutteClient),
        $crawler = new \Arachnid\Crawler($url, $linkDepth);
        $crawlerLinkModel = new CrawlerLinkModel();
        $crawler->traverse()->getLinks();
        $links = $crawler->getLinksArray();

        $insertData = 0;
        $checkLinks = array_column($crawlerLinkModel->asArray()->where(['type' => $params['type']])->findAll(), 'link');

        foreach ($links as $link => $value) {
            if (!in_array($link, $checkLinks)) {
                CLI::write('New link found: ' . $link, 'cyan');
                $crawlerLinkModel->insert(['link' => $link, 'type' => $params['type']]);
                $insertData++;
            } else {
                CLI::write('Link already in database: ' . $link, 'blue');
            }
        }

        CLI::write('Found and collected ' . $insertData . ' links', 'green');
    }

    /**
     * National info must sync from xml file instead of normal crawlers
     */
    protected function syncSitemap(string $type = 'national')
    {
        $crawlerLinkModel = new CrawlerLinkModel();
        $arrContextOptions = [
            "ssl" => [
                "verify_peer" => false,
                "verify_peer_name" => false,
            ],
        ];
        try {
            $response = file_get_contents($this->sources[$type], false, stream_context_create($arrContextOptions));
        } catch (Exception $e) {
            CLI::error($e->getMessage());
            exit;
        }
        $xml = simplexml_load_string($response);
        $json = json_encode($xml);
        $links = json_decode($json, true);

        $data = []; // iterate data
        foreach ($links as $l => $link) {
            foreach ($link as $value) {
                $data[] = $value['loc'];
            }
        }

        $insertData = 0;
        $checkLinks = array_column($crawlerLinkModel->asArray()->where(['type' => $type])->findAll(), 'link');

        foreach ($data as $link) {
            if (!in_array($link, $checkLinks)) {
                CLI::write('New link found: ' . $link, 'cyan');
                $crawlerLinkModel->insert(['link' => $link, 'type' => $type]);
                $insertData++;
            } else {
                CLI::write('Link already in database: ' . $link, 'blue');
            }
        }

        CLI::write('Found and collected ' . $insertData . ' links', 'green');
    }

}
