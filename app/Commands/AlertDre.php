<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use \App\Models\DreItemModel;

class AlertDre extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:alert-dre';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Notify emails from the list of recipients about new DRE items by default it' .
        'will exclude the ones in the words array or passing the --all true it will send all';
    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:alert-dre --type';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $subject = 'Alertas Diário da República ' . date('Y-m-d / H') . ' Horas';
        $excluded = false;
        if (isset($params['type']) && $params['type'] === 'all') {
            $items = $this->itemsToday();
            $subject = 'Alertas Diário da República EXCLUIDOS ' . date('Y-m-d');
            $excluded = true;
        } else {
            $items = $this->filteredItems();
        }
        if (empty($items)) {
            CLI::error('No new alerts for DRE.');
            exit;
        }
        if (empty(service('settings')->get('Dre.emails'))) {
            CLI::error('There are no e-mails in the list of recipients.');
            exit;
        }
        $emails = ($excluded) ? ['<EMAIL>'] : service('settings')->get('Dre.emails');
        foreach ($emails as $email) {
            $sent = service('notification')->initialize(['to' => $email])
                ->subject($subject)
                ->message(view('emails/alert-dre', ['items' => $items, 'email' => $email]))
                ->send();
            if ($sent) {
                CLI::write('Notifications sent to the following email: ' . $email, 'green');
                continue;
            }
            log_message('critical', 'We had a serious problem while trying to send notifications to this email: ' . $email);
            CLI::error('We had a serious problem while trying to send notifications to this email: ' . $email);
        }
        $this->markAsSent($items);
    }

    /**
     * Only filtered items
     * @return list of filtered items
     */
    protected function filteredItems()
    {
        $dreModel = new DreItemModel();
        $items = $dreModel->where('sent', '0')->orderBy('title', 'ASC')->findAll();
        $items = array_filter($items, function ($object) {

            return empty($object->excluded);
        });

        return $items;
    }

    /**
     * All the items that were synced today
     * @return list of all the items
     */
    protected function itemsToday()
    {
        $dreModel = new DreItemModel();

        return $dreModel->where(['DATE(created_at)' => date('Y-m-d'), 'sent' => '0'])->orderBy('title', 'ASC')->findAll();
    }

    /**
     * Mark all the sent items as sent
     * @param  array  $items The selected items
     * @return void
     */
    protected function markAsSent(array $items): void
    {
        $dreModel = new DreItemModel();
        foreach ($items as $item) {
            $dreModel->update($item->id, ['sent' => 1]);
        }
    }
}
