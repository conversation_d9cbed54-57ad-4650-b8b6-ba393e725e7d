<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class NewData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:dailyNewData';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '[DEPRECATED] Notify admins about daily new data that was added by scrappers';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:dailyNewData --date yyyy-mm-dd';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $date = $params['date'] ?? date('Y-m-d', strtotime('-1 days'));
        $data['preNotices'] = model('\App\Models\PreNoticeModel')
            ->where(['DATE(created_at)' => $date, 'status' => 'inactive'])
            ->findAll();
        $data['noticesNational'] = model('\App\Models\NoticiesNationalExtraModel')
            ->where(['DATE(created_at)' => $date, 'status' => 'inactive'])
            ->findAll();
        $admins = auth()->getProvider()->getAdmins();

        $emails = [];
        foreach ($admins as $admin) {
            $emails[] = $admin->email;
        }

        if (empty($data['preNotices']) && empty($data['noticesNational'])) {
            CLI::error('There\s no new data!');
            exit;
        }

        $sent = service('notification')->initialize(['to' => implode(',', $emails)])
            ->subject('Nova informação sincronizada no dia ' . $date)
            ->message(view('emails/new-data', $data))
            ->send();

        if ($sent) {
            CLI::write('All notifications were sent!', 'green');
            exit;
        }
        log_message('alert', 'There was a problem while sending the email.');
        CLI::error('There was a problem while sending the email.');
    }
}
