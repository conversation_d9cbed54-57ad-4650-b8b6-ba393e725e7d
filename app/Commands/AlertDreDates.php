<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AlertDreDates extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:alert-dre-dates';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Alert from approaching dates about clarifications, candidacy and proposals for a Dre item.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:alert-dre-dates --days (1,2,3...) --type (single)';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [
        'days' => 'Number of days from today to warn about contest dates',
        'type' => 'None to send do superior, single to send to each of the interested emails'
    ];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        'days' => 'number of days, set none for today',
        'type' => 'single, set none to send to superiors'
    ];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $date = date('Y-m-d');
        $subject = 'Concursos Públicos com datas limite para o dia ' . $date;
        if (isset($params['days'])) {
            $date = date('Y-m-d', strtotime($date . ' + ' . $params['days'] . ' days'));
            $subject = 'Concursos Públicos com datas limite em ' . $params['days'] . ' dias';
        }
        $clarifications = $this->getContests('date_clarify', $date);
        $candidacies = $this->getContests('date_candidacy', $date);
        $proposals = $this->getContests('date_proposal', $date);
        if (empty($clarifications) && empty($candidacies) && empty($proposals)) {
            CLI::write('No new dates to alert about for ' . $date . '.', 'green');

            exit;
        }
        $emails = '<EMAIL>,<EMAIL>,<EMAIL>';
        if (isset($params['type']) && $params['type'] === 'single') {
            $groupedContests = [];
            $this->groupByEmail($groupedContests, $clarifications, 'clarifications');
            $this->groupByEmail($groupedContests, $candidacies, 'candidacies');
            $this->groupByEmail($groupedContests, $proposals, 'proposals');
            foreach ($groupedContests as $email => $contest) {
                if (in_array($email, explode(',', $emails))) {
                    continue;
                }
                $sent = service('notification')->initialize(['to' => $email])
                    ->subject($subject)
                    ->message(view('emails/alert-dre-dates', [
                        'clarifications' => $contest['clarifications'], 'candidacies' => $contest['candidacies'],
                        'proposals' => $contest['proposals'], 'date' => $date
                    ]))
                    ->send();
                if ($sent) {
                    CLI::write('Notifications sent to the following email: ' . $email, 'green');
                    continue;
                }
                log_message('critical', 'We had a serious problem while trying to send notifications to this email: ' . $email);
                CLI::error('We had a serious problem while trying to send notifications to this email: ' . $email);
            }
            exit;
        }
        $sent = service('notification')->initialize(['to' => $emails])
            ->subject($subject)
            ->message(view('emails/alert-dre-dates', [
                'clarifications' => $clarifications, 'candidacies' => $candidacies, 'proposals' => $proposals, 'date' => $date
            ]))
            ->send();
        if ($sent) {
            CLI::write('Notifications sent to the following emails: ' . $emails, 'green');

            exit;
        }
        log_message('critical', 'We had a serious problem while trying to send notifications to these emails: ' . $emails);
        CLI::error('We had a serious problem while trying to send notifications to these emails: ' . $emails);
    }

    /**
     * Get contests grouped by their chosen dates
     * @param  string  $dateType date type
     * @param  string  $date     date
     * @return array
     */
    protected function getContests(string $dateType, string $date): array
    {
        $contests = model('\App\Models\DreItemModel')
            ->where($dateType, $date)
            ->where('dre_items.status !=', 'revoked')
            ->findAll();

        return $contests;
    }

    /**
     * Group contests by interest emails
     * @param  array  &$groupedContests grouped contests array
     * @param  array  $contests         contests array
     * @param  string $type             type of contest
     * @return void
     */
    protected function groupByEmail(array &$groupedContests, array $contests, string $type): void
    {
        foreach ($contests as $contest) {
            $emails = array_map('trim', explode(',', $contest->interest_emails));

            foreach ($emails as $email) {
                if (empty($email)) {
                    continue;
                }
                if (!isset($groupedContests[$email])) {
                    $groupedContests[$email] = [
                        'clarifications' => [],
                        'candidacies' => [],
                        'proposals' => []
                    ];
                }

                $groupedContests[$email][$type][] = $contest;
            }
        }
    }
}
