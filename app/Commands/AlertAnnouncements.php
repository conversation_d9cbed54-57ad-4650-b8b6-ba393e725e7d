<?php

namespace App\Commands;

use Basegov\Models\BaseGovAlertCpvModel;
use Basegov\Models\BaseGovAlertModel;
use Basegov\Models\BaseGovAnnouncementModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AlertAnnouncements extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:alert-announcements';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '[DEPRECATED] Notify all emails that were set for new announcements!';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:alert-announcements';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $announcement = new BaseGovAnnouncementModel();
        $alertModel = new BaseGovAlertModel();
        $cpvModel = new BaseGovAlertCpvModel();
        $now = date('Y-m-d H:i:s');
        $alerts = $alertModel->where(['last_check <' => $now, 'type' => 'announcements'])->findAll();

        foreach ($alerts as $alert) {
            $announcement->where('created_at >=', $alert->last_check);
            $announcement->where('created_at <=', $now);
            $announcement->where('proposalDeadline >=', $now);
            $cpvs = array_column(
                $cpvModel->asArray()->where('alert_id', $alert->id)->findAll(), 'cpv'
            );
            $tags = explode(',', $alert->tags);
            if (!empty($cpvs)) {
                $announcement->groupStart();
                foreach ($cpvs as $cpv) {
                    $announcement->orLike('cpvs', $cpv);
                }
                $announcement->groupEnd();
            }
            if (!empty($tags)) {
                $announcement->groupStart();
                foreach ($tags as $tag) {
                    if (!empty($tag)) {
                        $announcement->orLike('contractDesignation', trim($tag));
                    }
                }
                $announcement->groupEnd();
            }
            $data['announcements'] = $announcement->findAll();
            if (empty($data['announcements'])) {
                CLI::error('No new alerts for the following emails: ' . $alert->emails);
                $alertModel->skipValidation(true)->update($alert->id, ['last_check' => $now]);
                continue;
            }
            $sent = service('notification')->initialize(['to' => $this->cleanUpEmail($alert->emails)])
                ->subject('Concursos Públicos')
                ->message(view('emails/alert-announcements', $data))
                ->send();
            if ($sent) {
                CLI::write('Notifications were sent to the following emails: ' . $alert->emails, 'green');
            } else {
                log_message('critical', 'We had a serious problem while trying to send notifications to these emails: ' . $alert->emails);
                CLI::error('We had a serious problem while trying to send notifications to these emails: ' . $alert->emails);
            }

            $alertModel->skipValidation(true)->update($alert->id, ['last_check' => $now]);
        }
    }

    /**
     * Verify if the last email ends with [.,; or space] and erases it
     * @param  string   $emailList String that contains emails separated by coma, dot or semi-coma
     * @return string
     */
    public function cleanUpEmail(string $emailList): string
    {
        $emailList = trim($emailList);
        if (!empty($emailList) && substr($emailList, -1) === ',' || substr($emailList, -1) === '.' || substr($emailList, -1) === ';') {
            $emailList = substr($emailList, 0, -1);
        }

        return $emailList;
    }
}
