<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use CodeIgniter\Shield\Entities\User;

class Admin extends BaseCommand
{

    protected $group = 'Shield';
    protected $name = 'shield:createAdmin';
    protected $description = 'Create a new super admin for the app';

    public function run(array $params)
    {
        $email = CLI::prompt('What is the user email?', null, 'required|valid_email');
        $username = CLI::prompt('What is the NIF?', null, 'required|min_length[9]|max_length[9]|is_numeric');
        $password = CLI::prompt('What is the user password?', null, 'required|min_length[8]');
        $passwordConfirm = CLI::prompt('Please confirm the password.', null, 'required|min_length[8]');

        if ($password !== $passwordConfirm) {
            CLI::error('Password must be equal to the confirmation');
            exit;
        }

        $thead = ['Email', 'Username', 'Password'];
        $tbody = [
            [$email, $username, $password],
        ];
        CLI::table($tbody, $thead);

        $confirm = CLI::prompt('Do you confirm?', ['y', 'n']);

        if ($confirm === 'n') {
            CLI::error('Operation canceled!');
            exit;
        }

        $users = auth()->getProvider();
        $user = new User([
            'username' => $username,
            'email' => $email,
            'password' => $password,
        ]);
        if (!$users->save($user)) {
            CLI::error('There was a problem while creating your user');
            exit;
        }
        $user = $users->findById($users->getInsertID());
        $user->addGroup('admin');

        CLI::write('The user ' . $email . ' was created!', 'green');
    }
}
