<?php

namespace App\Commands;

use App\Models\CrawlerLinkModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use \DateTime;

class Prr extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:prr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Sync data from links collected from PRR';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:prr [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $texts = $externalLinks = $docs = [];
        $endAt = $startAt = $date = null;
        $externalLinksThatWeDoNotNeed = ['linkedin', 'twitter', 'youtube'];
        $crawlerLinkModel = new CrawlerLinkModel();
        $web = new \Spekulatius\PHPScraper\PHPScraper;
        $url = $crawlerLinkModel->where(['type' => 'prr', 'status' => 'todo'])->first();
        if (empty($url)) {
            CLI::error('There are no new links to sync');
            exit;
        }
        $uri = service('uri', $url->link);
        // English data, skip this one
        if ($uri->getQuery() === 'lang=en') {
            model('\App\Models\CrawlerLinkModel')->skipValidation(true)->update($url->id, ['status' => 'done']);
            CLI::write('This is an english language link, we skip this one.');
            exit;
        }
        CLI::write('Link to scrap: ' . $url->link);
        // Crawl the data
        $web->go($url->link);
        $tags = $web->filterTexts("//*[@class='benef-labels']");
        $allLinks = $web->links();
        $paragraphs = $web->paragraphs();
        // Clean up all the links that we do not want
        foreach ($allLinks as $key => $link) {
            if (strpos($link, 'uploads') !== false) {
                $docs[$key] = $link;
            }
            if (!strpos($link, 'recuperarportugal')) {
                $externalLinks[$key] = $link;
            }
        }
        foreach ($externalLinks as $key => $external) {
            foreach ($externalLinksThatWeDoNotNeed as $exclude) {
                if (strpos($external, $exclude)) {
                    unset($externalLinks[$key]);
                }
                if ($external === 'javascript:void(0);') {
                    unset($externalLinks[$key]);
                }
            }
        }
        // Get just the texts that we want and parse the dates
        foreach ($paragraphs as $key => $value) {
            if (strpos($value, 'Aviso') || strpos($value, 'Candidaturas') || strpos($value, 'esclarecimentos')) {
                $texts[$key] = $value;
                if (strpos($value, 'Aviso')) {
                    $tmp = explode(':', $value);
                    if (isset($tmp[1])) {
                        $date = $this->setDate(trim($tmp[1]));
                    }
                }
                if (strpos($value, 'Candidaturas')) {
                    $regex = '/\b\d{1,2}[\/.-]\d{1,2}[\/.-]\d{2,4}\b/';
                    preg_match_all($regex, $value, $matches);
                    if (isset($matches[0][0])) {
                        $startAt = $this->setDate(trim($matches[0][0]));
                        $endAt = $this->setDate(end($matches[0]));
                    }
                }
            }
        }
        // Set the data to be stored
        $data = [
            'link' => $url->link,
            'name' => $web->filter("//h6")->text(),
            'program' => $web->title(),
            'date' => $date,
            'date_start' => $startAt,
            'date_end' => $endAt,
            'tags' => implode(',', $tags),
            'texts' => implode('<br>', $texts),
            'document' => $docs[0] ?? null,
        ];
        if (!model('\App\Models\NoticePrrModel')->insert($data)) {
            model('\App\Models\CrawlerLinkModel')->skipValidation(true)->update($url->id, ['status' => 'done']);
            CLI::error('There was a problem while indexing this link.');
            exit;
        }
        CLI::write('Link was indexed', 'green');
        foreach ($externalLinks as $externalLink) {
            model('\App\Models\NoticePrrLinkModel')->insert([
                'notice_id' => model('\App\Models\NoticePrrModel')->getInsertID(),
                'link' => $externalLink,
            ]);
        }
        model('\App\Models\CrawlerLinkModel')->skipValidation(true)->update($url->id, ['status' => 'done']);

        CLI::write('Everything went fine!', 'green');
    }

    /**
     * Set date accross the process
     * @param string $date
     */
    protected function setDate(string $date): string
    {
        // Try the first format
        $dateObj = DateTime::createFromFormat('d/m/Y', trim($date));
        if (!$dateObj) {
            // Try the second
            $dateObj = DateTime::createFromFormat('Y/m/d', trim($date));
        }
        if (!$dateObj) {
            return '';
        }

        return $dateObj->format('Y-m-d');
    }
}
