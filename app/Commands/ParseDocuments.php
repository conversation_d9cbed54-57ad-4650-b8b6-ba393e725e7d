<?php

namespace App\Commands;

use App\Models\NoticeNationalTextModel;
use App\Models\NoticiesNationalDocumentModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\NoticeDocumentStructure;

class ParseDocuments extends BaseCommand
{
    protected $group = 'Crawler';
    protected $name = 'scraper:parse-documents';
    protected $description = 'Parse documents in nt_notices_national_documents already downloaded and not parsed.';
    protected $usage = 'scraper:parse-documents';
    protected $arguments = [];
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $documentModel = new NoticiesNationalDocumentModel();
        $parser = new \Smalot\PdfParser\Parser();
        $docStructure = new NoticeDocumentStructure();
        $documentTextModel = new NoticeNationalTextModel();

        $document = $documentModel->where(['status' => 'active', 'local_document' => 1, 'parsed' => 0])->first();
        if (empty($document)) {
            CLI::error('We have no documents to parse at the moment.');
            exit;
        }
        $documentModel->skipValidation(true)->update($document->id, ['parsed' => 1]);
        // Check document to see if its a pdf file
        $file = explode('.', $document->documentoDesignacao);
        if (end($file) !== 'pdf') {
            log_message('info', 'Document ' . $document->documentoDesignacao . ' is not a PDF document. We will skip this one.');
            CLI::error('Document ' . $document->documentoDesignacao . ' is not a PDF document. We will skip this one.');
            exit;
        }

        $pdf = $parser->parseFile(WRITEPATH . '/downloads/' . $document->documentoDesignacao);
        $numberOfPages = $pdf->getDetails()['Pages'];
        $rawText = preg_replace('/\s+/', ' ', $pdf->getText());
        $chapters = [];
        foreach ($docStructure->chapters as $key => $chapter) {
            if (!isset($docStructure->chapters[$key + 1]) || !$chapter['sync']) {
                continue;
            }
            $nextChapter = $docStructure->chapters[$key + 1];
            $chapters[$key] = [
                'notice_id' => $document->notice_id,
                'document_id' => $document->id,
                'title' => $chapter['name'],
                'content' => $this->clearUpFooterStrings(
                    $this->getStringBetween($rawText, $chapter['name'], $nextChapter['name']),
                    $numberOfPages
                )
            ];
            if (!$documentTextModel->insert($chapters[$key])) {
                foreach ($documentTextModel->errors() as $error) {
                    log_message('error', $error);
                    CLI::error($error);
                }
                continue;
            }
        }

        CLI::write('Texts from your document were saved', 'green');
    }

    /**
     * Separate the texts and associate them with their titles
     * @param  string $string Original string (entire document)
     * @param  string $start  The string we're looking for the start
     * @param  string $end    The string we're looking for the end
     * @return string The content we're looking for
     */
    private function getStringBetween(string $string, string $start, string $end): string
    {
        $string = ' ' . $string;
        $ini = strpos($string, $start);
        if ($ini == 0) {
            return '';
        }

        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;

        return trim(substr($string, $ini, $len));
    }

    /**
     * Clear up footer texts that exist on all pages and pagination
     * @param  string $text          The original text
     * @param  int    $numberOfpages Number of pages in a document
     * @return string The fixed text
     */
    private function clearUpFooterStrings(string $text, int $numberOfpages): string
    {
        $remove = 'Os Fundos Europeus mais próximos de si.';
        $text = str_replace($remove, '', $text);
        for ($i = 1; $i < $numberOfpages; $i++) {
            $text = str_replace($i . '/' . $numberOfpages, '', $text);
        }

        return $text;
    }

}
