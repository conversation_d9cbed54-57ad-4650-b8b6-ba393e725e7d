<?php

namespace App\Commands;

use App\Models\NoticiesNationalDocumentModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class Documents extends BaseCommand
{
    protected $group = 'Crawler';
    protected $name = 'scraper:documents';
    protected $description = 'Get documents from portugal2030.protected';
    protected $usage = 'scraper:documents';
    protected $arguments = [];
    protected $options = [];
    protected $url = 'https://portugal2030.pt';
    protected $downloadUrl = '/wp-json/avisos/download?container=siag-prod-container&path=';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $documentModel = new NoticiesNationalDocumentModel();
        $document = $documentModel->where(['status' => 'inactive', 'local_document' => 0])->first();
        if (empty($document)) {
            CLI::error('We have no documents to download at the moment.');
            exit;
        }

        $path = WRITEPATH . 'downloads/' . $document->documentoDesignacao;
        $ch = curl_init($this->url . $this->downloadUrl . $document->path);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_REFERER, $this->url . $this->downloadUrl . $document->path);
        $data = curl_exec($ch);
        curl_close($ch);
        $result = file_put_contents($path, $data);

        if (!$result) {
            log_message('error', 'There was a problem while trying to download the document: ' . $documentModel->id);
            CLI::error('There was a problem while trying to download the document: ' . $documentModel->id);
            exit;
        }

        CLI::write('The file  ' . $document->documentoDesignacao . ' was downloaded', 'green');
        $documentModel->update($document->id, ['local_document' => 1, 'status' => 'active']);
        CLI::write('Database updated!', 'green');
    }
}
