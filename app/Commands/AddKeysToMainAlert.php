<?php

namespace App\Commands;

use Basegov\Models\BaseGovAlertModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class AddKeysToMainAlert extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'alerts:add-main';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Add all keywords to the main alert';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'alerts:add-main';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $alertModel = new BaseGovAlertModel();

        if ($alertModel->addToMain()) {
            CLI::write('The keys were copied to the main alert', 'green');
            exit;
        }
        CLI::error('We were not able to copy anything');
        exit;
    }
}
