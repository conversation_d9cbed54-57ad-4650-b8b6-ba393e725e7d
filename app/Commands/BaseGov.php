<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Exception;
use GuzzleHttp\Client;

class BaseGov extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:basegov';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Sync data from base.gov you need to specify the type of data --type [cim, city_hall, parish, list]';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:basegov [type] [cim, city_hall, parish, list]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = ['type'];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = ['cim', 'city_hall', 'parish', 'list'];

    protected $model;

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        if (!isset($params['type'])) {
            CLI::error('You must set a valid type using ' . $this->usage);
            exit;
        }
        $entity = $this->setEntity($params['type']);
        if (empty($entity)) {
            CLI::error('We couldn\'t find any entities to sync');
            exit;
        }
        CLI::write('Selected ' . $entity->vat . ' - ' . $entity->name, 'green');
        try {
            $contracts = $this->getData($entity);
        } catch (\Exception $e) {
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        if (!isset($contracts->total)) {
            $fails = (empty($entity->count_fails)) ? 1 : $entity->count_fails++;
            $this->model->skipValidation(true)->update($entity->id, ['count_fails' => $fails]);
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        CLI::write('There are a total of ' . $contracts->total . ' on base.gov.pt', 'green');
        $this->updateEntity($entity, $contracts->total);
        if (!isset($contracts->items) || empty($contracts->items)) {
            CLI::error('No contracts to save');
            exit;
        }

        foreach ($contracts->items as $contract) {
            $contract->vat = $entity->vat;
            $contract->publicationDate = date('Y-m-d', strtotime($contract->publicationDate));
            $contract->signingDate = date('Y-m-d', strtotime($contract->signingDate));
            $contract = (array) $contract;
            $this->saveContract($contract);
        }
        $this->updateEntity($entity, $contracts->total);
        exit;
    }

    /**
     * Save contract into database (can't use the save method since the id comes from the base-gov service)
     * @param  array  $contract The selected contract
     * @return bool
     */
    protected function saveContract(array $contract): bool
    {
        $existingContract = model('\Basegov\Models\BaseGovModel')->where('id', $contract['id'])->first();
        if ($existingContract) {
            return model('\Basegov\Models\BaseGovModel')->update($contract['id'], $contract);
        }

        return model('\Basegov\Models\BaseGovModel')->insert($contract);
    }

    /**
     * Update entity with the totals and dates of sync
     * @param  object $entity The selected entity
     * @param  int    $total  The current total
     * @return bool
     */
    protected function updateEntity(object $entity, int $total): bool
    {
        $fails = null;
        $countData = model('\Basegov\Models\BaseGovModel')
            ->select('COUNT(id) AS total')
            ->where('vat', $entity->vat)
            ->first();

        if ($total === 0) {
            CLI::write('Nothing to sync entity updated: ' . $entity->name, 'green');
        } else {
            CLI::write('We now have : ' . $countData->total . ' local contracts!', 'green');
        }
        if ($countData->total > $total) {
            $total = $countData->total;
        }
        // If we end up with the same numbers as we started and the local total is still bellow the remote the process failed
        if ($countData->total < $total && $countData->total === $entity->local_total) {
            $fails = (empty($entity->count_fails)) ? 1 : $entity->count_fails++;
            if ($fails === 3) {
                model('\Basegov\Models\BaseGovFailedModel')->insert(['vat' => $entity->vat]);
            }
        }
        return $this->model->update(
            $entity->id, [
                'remote_total' => $total,
                'local_total' => $countData->total,
                'last_connection' => date('Y-m-d H:m:i'),
                'direction' => ($entity->direction === 'normal') ? 'first' : 'normal',
                'count_fails' => $fails
            ]
        );
    }

    /**
     * Set the entity they can come from any table depending on the selected type
     * @param string $type The selected type
     */
    protected function setEntity(string $type): object
    {
        switch ($type) {
            case 'cim':
                $this->model = new \App\Models\CimModel();
                break;
            case 'city_hall':
                $this->model = new \App\Models\CityHallModel();
                break;
            case 'parish':
                $this->model = new \App\Models\ParishModel();
                break;
            case 'list':
                $this->model = new \Basegov\Models\BaseGovEntityModel();
                break;

            default:
                CLI::error('Not a valid type.');
                exit;
                break;
        }
        // First we select the ones that were never connected
        $entity = $this->model->where(['last_connection' => null, 'count_fails <' => 3])->first();

        // If theres none we select those who were connected but still have rows to get
        if (empty($entity)) {
            $entity = $this->model->where('local_total < remote_total AND count_fails < 3', null, false)->first();
        }
        // Then we get the oldest and try to see if there's new data to sync
        if (empty($entity)) {
            $entity = $this->model->where(['last_connection != ' => null])
                ->orderBy('last_connection', 'ASC')
                ->first();
        }

        return $entity;
    }

    /**
     * Get the data from the base.gov service
     * @param  object $entity The selected entity (vat)
     * @return object The data or NULL in case of failure from the base.gov.pt
     */
    protected function getData(object $entity)
    {
        $perPage = 50;
        $page = 0;
        // Sync history
        if ($entity->local_total <= $entity->remote_total) {
            $totalLocalPages = (int) round($entity->local_total / $perPage);
            $page = $totalLocalPages;
        }
        if ($entity->direction === 'normal') {
            $page = 0;
        }

        $client = new Client([
            'base_uri' => 'https://www.base.gov.pt',
            'timeout' => 60.0
        ]);
        $r = $client->request('POST', 'https://www.base.gov.pt/Base4/pt/resultados/', [
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.361675787112'
            ],
            'form_params' => [
                'type' => 'search_contratos',
                'version' => '112.0',
                'query' => 'tipo=2&tipocontrato=2&adjudicante=' . $entity->vat . '&desdedatacontrato=&atedatacontrato=&pais=0&distrito=0&concelho=0',
                'sort' => '-publicationDate',
                'page' => $page,
                'size' => $perPage
            ]
        ]);
        $body = $r->getBody();

        return json_decode($body);
    }

}
