<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use GuzzleHttp\Client;
use \Exception;

class BaseGovRecover extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'basegov:recover';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Re-syncs data from entities that were marked with 3 errors';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'basegov:recover';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    protected $model;

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $entity = model('\Basegov\Models\BaseGovFailedModel')->first();
        if (empty($entity)) {
            CLI::error('We couldn\'t find any entities to sync');
            exit;
        }
        CLI::write('Selected ' . $entity->vat, 'green');

        try {
            $contracts = $this->getData($entity);
        } catch (\Exception $e) {
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        if (!isset($contracts->total)) {
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        CLI::write('There are a total of ' . $contracts->total . ' on base.gov.pt', 'green');

        foreach ($contracts->items as $contract) {
            $contract->vat = $entity->vat;
            $contract->publicationDate = date('Y-m-d', strtotime($contract->publicationDate));
            $contract->signingDate = date('Y-m-d', strtotime($contract->signingDate));
            $contract = (array) $contract;
            $this->saveContract($contract);
        }
        $data = [
            'id' => $entity->id,
            'total_remote' => $contracts->total,
            'remote_pages' => $contracts->total / 50,
            'last_page' => (is_null($entity->last_page)) ? 0 : $entity->last_page + 1
        ];

        if ($data['last_page'] > $data['remote_pages']) {
            CLI::write('Process complete. The entity will be removed from the failed status.', 'green');
            model('\Basegov\Models\BaseGovFailedModel')->where('id', $data['id'])->delete();
        } else {
            model('\Basegov\Models\BaseGovFailedModel')->save($data);
        }
        $localContracts = count(model('\Basegov\Models\BaseGovModel')->where('vat', $entity->vat)->findAll());
        if ((int) $localContracts >= (int) $contracts->total) {
            CLI::write('Process complete, we have all the contracts. The entity will be removed from the failed status.', 'green');
            model('\Basegov\Models\BaseGovFailedModel')->delete($entity->id);
            $this->removeFailedStatus($entity->vat);
        }
        exit;
    }

    /**
     * Clear up
     * @param string $vat
     */
    protected function removeFailedStatus(string $vat): void
    {
        $cityHall = model('\App\Models\CityHallModel')->where('vat', $vat)->first();
        if (isset($cityHall->id)) {
            model('\App\Models\CityHallModel')->skipValidation(true)->update($cityHall->id, ['count_fails' => 0]);
        }
        $parish = model('\App\Models\ParishModel')->where('vat', $vat)->first();
        if (isset($parish->id)) {
            model('\App\Models\ParishModel')->skipValidation(true)->update($parish->id, ['count_fails' => 0]);
        }
        $cim = model('\App\Models\CimModel')->where('vat', $vat)->first();
        if (isset($cim->id)) {
            model('\App\Models\CimModel')->skipValidation(true)->update($cim->id, ['count_fails' => 0]);
        }
        $entity = model('\Basegov\Models\BaseGovEntityModel')->where('vat', $vat)->first();
        if (isset($entity->id)) {
            model('\App\Models\BaseGovEntityModel')->skipValidation(true)->update($entity->id, ['count_fails' => 0]);
        }
    }

    /**
     * Save contract into database (can't use the save method since the id comes from the base-gov service)
     * @param  array  $contract The selected contract
     * @return bool
     */
    protected function saveContract(array $contract): bool
    {
        $existingContract = model('\Basegov\Models\BaseGovModel')->where('id', $contract['id'])->first();
        if ($existingContract) {
            return model('\Basegov\Models\BaseGovModel')->update($contract['id'], $contract);
        }

        return model('\Basegov\Models\BaseGovModel')->insert($contract);
    }

    /**
     * Get the data from the base.gov service
     * @param  object $entity The selected entity (vat)
     * @return object The data or NULL in case of failure from the base.gov.pt
     */
    protected function getData(object $entity)
    {
        $perPage = 50;
        $page = (is_null($entity->last_page)) ? 0 : $entity->last_page + 1;
        $body = [];
        try {
            $client = new Client([
                'base_uri' => 'https://www.base.gov.pt',
                'timeout' => 60.0
            ]);
            $r = $client->request('POST', 'https://www.base.gov.pt/Base4/pt/resultados/', [
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.361675787112'
                ],
                'form_params' => [
                    'type' => 'search_contratos',
                    'version' => '112.0',
                    'query' => 'tipo=2&tipocontrato=2&adjudicante=' . $entity->vat . '&desdedatacontrato=&atedatacontrato=&pais=0&distrito=0&concelho=0',
                    'sort' => '-publicationDate',
                    'page' => $page,
                    'size' => $perPage
                ]
            ]);
            $body = $r->getBody();
        } catch (Exception $e) {
            CLI::error($e->getMessage());
        }
        if (!is_string($body)) {
            return [];
        }

        return json_decode($body);
    }

}
