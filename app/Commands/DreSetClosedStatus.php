<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class DreSetClosedStatus extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'dre:set-closed-status';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Set a DRE contest status as Closed if a proposal date is not met.';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'dre:set-closed-status';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $date = date('Y-m-d', strtotime('-1 day'));
        $update = model('\App\Models\DreItemModel')
            ->where('date_proposal', $date)
            ->groupStart()
            ->where('decision', 0)
            ->orWhere('decision', null)
            ->groupEnd()
            ->set('status', 'closed')
            ->update();
        if (!$update) {
            log_message('critical', 'We had a serious problem while trying to set closed status for the day ' . $date);
            CLI::error('We had a serious problem while trying to set closed status for the day ' . $date);

            exit;
        }
        CLI::write(
            'Undecided contests that had their proposal date ending in ' . $date .
            ' had their status set to closed successfully', 'green'
        );
    }
}
