<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use GuzzleHttp\Client;

class BaseGovAnnouncementDetail extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:basegov-announcement-detail';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Sync data from base.gov all the data from the items that were selected';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:basegov-announcement-detail';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $item = model('\Basegov\Models\BaseGovAnnouncementModel')->where('status', 'new')->orderBy('created_at', 'ASC')->first();

        if (empty($item)) {
            CLI::error('No new data to sync');
            exit;
        }
        try {
            $originalData = $this->getData($item->id);
        } catch (\Exception $e) {
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        $announcement = (array) $originalData;
        $announcement['status'] = 'finished';
        unset(
            $announcement['id'], $announcement['drPublicationDate'], $announcement['proposalDeadline'],
            $announcement['contractingEntities']
        );

        // Update Contract
        try {
            if (!model('\Basegov\Models\BaseGovAnnouncementModel')->skipValidation(true)->update($item->id, $announcement)) {
                CLI::error('Problem while updating contract id: ' . $item->id);
            }
        } catch (\Exception $e) {
            log_message('warning', 'Problem while saving data from baseGov with id' . $item->id);
            log_message('error', $e->getMessage());
            CLI::error($e->getMessage());
            model('\Basegov\Models\BaseGovAnnouncementModel')->update($item->id, ['status' => 'finished']);
            exit;
        }

        foreach ($originalData->contractingEntities as $entity) {
            $entity = (array) $entity;
            $entity['announcement_id'] = $item->id;
            $entity['base_gov_id'] = $entity['id'];
            unset($entity['id']);
            model('\Basegov\Models\BasegovAnnouncementsContractingEntitiesModel')->insert($entity);
        }

        CLI::write('Process was finished for contract id ' . $item->id, 'green');
    }

    /**
     * Get the data from the base.gov service
     * @param  int    $id The selected data
     * @return object The data
     */
    protected function getData(int $id): object
    {
        $client = new Client([
            'base_uri' => 'https://www.base.gov.pt',
            'timeout' => 60.0
        ]);
        $r = $client->request('POST', 'https://www.base.gov.pt/Base4/pt/resultados/', [
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/108.0.0.0 Safari/537.361675787112'
            ],
            'form_params' => [
                'type' => 'detail_anuncios',
                'version' => '120.0',
                'id' => $id
            ]
        ]);
        $body = $r->getBody();

        return json_decode($body);
    }

}
