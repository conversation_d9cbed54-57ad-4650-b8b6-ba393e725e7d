<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use DreApp\Libraries\SettingsCrud;

class DreManageWords extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Housekeeping';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'dre:words';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Manage the words that are being excluded from the notification for DRE';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'dre:words [arguments] [options]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = ['list', 'remove', 'add', 'set', 'update', 'word'];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = ['-l', '-r', '-a', '-s', '-u', '-w'];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        // RESET - There's a new set of exclude words to manage
        if (isset($params['s'])) {
            service('settings')->set('Dre.words', []);
            CLI::write('Reset the set of exclude words', 'green');
            exit;
        }
        // Add word
        if (isset($params['a']) && !empty($params['a'])) {
            if (SettingsCrud::add('Dre.words', $params['a'])) {
                CLI::error('Error in adding the word(s) ' . $params['a'] . ' to our set of exclude words');

                exit;
            }
            CLI::write('Added the word(s) ' . $params['a'] . ' to our set of exclude words', 'green');

            exit;
        }
        // Update word
        if (isset($params['u']) && !empty($params['u']) && isset($params['w']) && !empty($params['w'])) {
            if (SettingsCrud::update('Dre.words', $params['u'], $params['w'])) {
                CLI::error('Error in updating to word ' . $params['w'] . ' in our set of exclude words');

                exit;
            }
            CLI::write('Updated to word ' . $params['w'] . ' in our set of exclude words', 'green');

            exit;
        }
        // Remove word based on the position on the array when we list words
        if (isset($params['r']) && !is_null($params['r']) && $params['r'] !== '') {
            if (SettingsCrud::delete('Dre.words', $params['r'])) {
                CLI::error('Error in removing the word from the list');

                exit;
            }
            CLI::write('Removed the word from the list', 'green');
            exit;
        }
        // List words that we do not want to alert
        if (isset($params['l'])) {
            $wordData = SettingsCrud::get('Dre.words', true);
            CLI::table($wordData['tbody'], $wordData['thead']);

            exit;
        }
        CLI::error('Not a valid command, you should use the flags -a -r or -l');
    }
}
