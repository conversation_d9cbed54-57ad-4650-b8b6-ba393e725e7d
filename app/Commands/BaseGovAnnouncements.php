<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use GuzzleHttp\Client;

class BaseGovAnnouncements extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:basegov-announcements';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Sync data from base.gov [Announcements]';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:basegov-announcements';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $cpv = model('\App\Models\CpvModel')->orderBy('last_sync', 'ASC')->first();
        CLI::write('Selected ' . $cpv->cpv . ' ' . $cpv->description, 'green');
        try {
            $announcements = $this->getData($cpv->cpv);
        } catch (\Exception $e) {
            log_message('info', 'There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            CLI::error('There was an error while trying to get data from base.gov.pt, it took too long or the service is down.');
            exit;
        }
        if (!empty($announcements->items)) {
            foreach ($announcements->items as $announcement) {
                $announcement->proposalDeadline = date('Y-m-d', strtotime($announcement->proposalDeadline));
                $announcement->drPublicationDate = date('Y-m-d', strtotime($announcement->drPublicationDate));
                $announcement = (array) $announcement;
                if ($this->saveAnnouncement($announcement)) {
                    CLI::write('Announcement inserted with  ' . $announcement['id'], 'green');
                } else {
                    CLI::error('Announcement not inserted with  ' . $announcement['id']);
                }
            }
        }

        model('\App\Models\CpvModel')->update($cpv->id, ['last_sync' => date('Y-m-d H:i:s')]);
    }

    /**
     * Get the data from the base.gov service
     * @param  object $entity The selected entity (vat)
     * @return object The data
     */
    protected function getData(string $cpv)
    {
        $client = new Client([
            'base_uri' => 'https://www.base.gov.pt',
            'timeout' => 60.0
        ]);
        $r = $client->request('POST', 'https://www.base.gov.pt/Base4/pt/resultados/', [
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.361675787112'
            ],
            'form_params' => [
                'type' => 'search_anuncios',
                'version' => '123.0',
                'query' => 'tipoacto=0&tipomodelo=0&tipocontrato=0&cpv=' . $cpv,
                'sort' => '-drPublicationDate',
                'page' => 0,
                'size' => 100
            ]
        ]);
        $body = $r->getBody();

        return json_decode($body);
    }

    /**
     * Save Announcement into database
     * @param  array  $announcement The selected annoucement
     * @return bool
     */
    protected function saveAnnouncement(array $announcement): bool
    {
        $existingAnnouncement = model('\Basegov\Models\BaseGovAnnouncementModel')->where('id', $announcement['id'])->first();

        if (isset($existingAnnouncement->id)) {
            return model('\Basegov\Models\BaseGovAnnouncementModel')->update($existingAnnouncement->id, $announcement);
        }

        return model('\Basegov\Models\BaseGovAnnouncementModel')->insert($announcement);
    }

}
