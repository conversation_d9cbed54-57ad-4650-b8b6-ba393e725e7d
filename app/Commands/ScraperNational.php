<?php

namespace App\Commands;

use App\Models\NoticeNationalModel;
use App\Models\NoticiesNationalDocumentModel;
use App\Models\NoticiesNationalExtraModel;
use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use \Exception;

class ScraperNational extends BaseCommand
{

    protected $group = 'Crawler';
    protected $name = 'scraper:national';
    protected $description = 'Get content from national portugal2030.pt';
    protected $usage = 'scraper:national';
    protected $arguments = [];
    protected $options = [];
    protected $url = 'https://portugal2030.pt';
    protected $jsonUrl = '/wp-json/avisos/query?estadoAvisoId=7&programaId=&fundoId=&page=';
    protected $downloadUrl = '/wp-json/avisos/download?container=siag-prod-container&path=';

    /**
     * save data from the various sources
     * @todo National / Tenders
     * @param array $params Type of data
     */
    public function run(array $params)
    {
        $page = $params['page'] ?? null;
        if (!$this->national($page)) {
            log_message('error', 'There was an error while trying to save the Notice from portugal2030');
            CLI::error('There was an error while trying to save the Notice from portugal2030');
            exit;
        }
    }

    /**
     * Save national data from json
     * @return bool
     */
    protected function national($page = null): bool
    {
        $web = new \Spekulatius\PHPScraper\PHPScraper;
        $noticeModel = new NoticeNationalModel();
        $noticeExtraModel = new NoticiesNationalExtraModel();
        $noticeDocumentModel = new NoticiesNationalDocumentModel();
        try {
            $json = $web->parseJson($this->url . $this->jsonUrl . $page);
        } catch (Exception $e) {
            CLI::error($e->getMessage());
            exit;
        }

        $return = true;

        if (!isset($json['avisos'])) {
            CLI::error('No new data');
            exit;
        }
        foreach ($json['avisos'] as $a) {
            if (!empty($noticeModel->where('avisoGlobalId', $a['aviso']['avisoGlobalId'])->first())) {
                log_message('notice', 'Skipped: ' . $a['aviso']['avisoGlobalId'] . ' / ' . $a['aviso']['designacaoPT']);
                CLI::error('Skipped: ' . $a['aviso']['avisoGlobalId'] . ' / ' . $a['aviso']['designacaoPT']);
                continue;
            }
            if ($noticeId = $noticeModel->insert($this->transformNotice($a))) {
                foreach ($a['estrutura'] as $extra) {
                    $noticeExtraModel->allowCallbacks(false)->insert($this->extraData($noticeId, $extra));
                }
                foreach ($a['documentos'] as $document) {
                    $noticeDocumentModel->allowCallbacks(false)->insert($this->documentData($noticeId, $document));
                }
                CLI::write('Added: ' . $a['aviso']['avisoGlobalId'] . ' / ' . $a['aviso']['designacaoPT'], 'green');
            } else {
                $return = false;
                CLI::error('Error: ' . $a['aviso']['avisoGlobalId'] . ' / ' . $a['aviso']['designacaoPT']);
            }
        }

        return $return;
    }

    /**
     * Set the default data from the national notice
     * @param  array   $notice The input notice data
     * @return array
     */
    private function transformNotice(array $notice): array
    {
        return [
            'avisoGlobalId' => $notice['aviso']['avisoGlobalId'] ?? null,
            'codigoAviso' => $notice['aviso']['codigoAviso'] ?? null,
            'designacaoPT' => $notice['aviso']['designacaoPT'] ?? null,
            'classificacaoAvisoDesignacao' => $notice['aviso']['classificacaoAvisoDesignacao'] ?? null,
            'instrumentoTerritorialDesignacao' => $notice['aviso']['instrumentoTerritorialDesignacao'] ?? null,
            'contextoAvisoInstrumentoDesignacao' => $notice['aviso']['contextoAvisoInstrumentoDesignacao'] ?? null,
            'dataUltimaAlteracao' => $notice['aviso']['dataUltimaAlteracao'] ?? null,
            'dataPublicacao' => $notice['calendario']['dataPublicacao'] ?? null,
            'dataInicio' => $notice['calendario']['dataInicio'] ?? null,
            'dataFim' => $notice['calendario']['dataFim'] ?? null,
            'dataFimAtual' => $notice['calendario']['dataFimAtual'] ?? null,
            'tempoMedioDecisaoFinal' => $notice['calendario']['tempoMedioDecisaoFinal'] ?? null,
            'status' => 'inactive',
        ];
    }

    /**
     * Extra notice data
     * @param  int     $noticeId The selected notice id
     * @param  array   $data     The extra data to be inserted
     * @return array
     */
    private function extraData(int $noticeId, array $data): array
    {
        return [
            'notice_id' => $noticeId,
            "programaOperacionalDesignacao" => $data['programaOperacionalDesignacao'] ?? null,
            "prioridadeDesignacao" => $data['prioridadeDesignacao'] ?? null,
            "objetivoEstrategicoDesignacao" => $data['objetivoEstrategicoDesignacao'] ?? null,
            "objetivoEspecificoDesignacao" => $data['objetivoEspecificoDesignacao'] ?? null,
            "tipologiaAcaoDesignacao" => $data['tipologiaAcaoDesignacao'] ?? null,
            "tipologiaIntervencaoDesignacao" => $data['tipologiaIntervencaoDesignacao'] ?? null,
            "tipologiaOperacaoDesignacao" => $data['tipologiaOperacaoDesignacao'] ?? null,
            "tipoFinanciamentoDesignacao" => $data['tipoFinanciamentoDesignacao'] ?? null,
            "fundoDesignacao" => $data['fundoDesignacao'] ?? null,
            "fonteFinanciamentoNacionalDesignacao" => $data['fonteFinanciamentoNacionalDesignacao'] ?? null,
            "dotacao" => $data['dotacao'] ?? null,
            "estrategiaDesignacao" => $data['estrategiaDesignacao'] ?? null,
        ];
    }

    /**
     * Set document data
     * @param  int     $noticeId The selected notice id
     * @param  array   $document The current document data to be inserted
     * @return array
     */
    private function documentData(int $noticeId, array $document): array
    {
        return [
            'notice_id' => $noticeId,
            'documentoId' => $document['documentoId'] ?? null,
            'documentoDesignacao' => $document['documentoDesignacao'] ?? null,
            'tipoDocumentoDesignacao' => $document['tipoDocumentoDesignacao'] ?? null,
            'documentoData' => $document['documentoData'] ?? null,
            'path' => $document['path'] ?? null,
            'status' => 'inactive',
        ];
    }

}
