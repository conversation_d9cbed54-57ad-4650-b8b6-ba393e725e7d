<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class BaseGovDre extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'Crawler';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'crawler:basegov-dre';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Gets data from DRE document';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'crawler:basegov-dre --id 1';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = ['id'];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $announcement = model('\Basegov\Models\BaseGovAnnouncementModel')->where('doc !=', 1)
            ->orderBy('created_at', 'DESC')->first();
        if (isset($params['id']) && !empty($params['id'])) {
            $announcement = model('\Basegov\Models\BaseGovAnnouncementModel')->where('id', $params['id'])->first();
        }
        if (empty($announcement)) {
            CLI::error('There is no data to work with');
            exit;
        }

        // Get DRE document (generate local path and start to call the redirects)
        $remoteDoc = explode('/', $announcement->reference);
        $path = WRITEPATH . 'downloads/' . end($remoteDoc) . '.pdf';
        $finalRedirect = $midRedirect = $dreRedirect = null;
        // First url is the one we havem this one returns only one localtion object, we need the 1st one
        exec('curl -i ' . $announcement->reference, $dreRedirect);
        $midRedirect = $this->parseHeaders($dreRedirect);
        if (!isset($midRedirect[0])) {
            CLI::error('Error while trying to parse the array.');
            exit;
        }
        // Now we call the second redirection, this is a bit different and returns more than one location, we need the 2nd one
        exec('curl -i ' . $midRedirect[0], $dreRedirect);
        $finalRedirect = $this->parseHeaders($dreRedirect);
        if (!isset($midRedirect[1])) {
            CLI::error('Error while trying to parse the array on key 1.');
            exit;
        }
        // We use the second location to get the binary
        $ch = curl_init($finalRedirect[1]);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_REFERER, $finalRedirect[1]);
        $data = curl_exec($ch);
        curl_close($ch);
        $result = file_put_contents($path, $data);
        // We didn't got the binary, update the record so we can move to the next one
        if (!$result) {
            model('\Basegov\Models\BaseGovAnnouncementModel')->update($announcement->id, ['doc' => 1]);
            log_message('warning', 'There was a problem while trying to download the document: ' . $announcement->reference);
            CLI::error('There was a problem while trying to download the document: ' . $announcement->reference);
            exit;
        }
        // We got a pdf file lets start getting the contents
        CLI::write('We created a local file in: ' . $path);
        $finalContent = null;
        $content = [];
        exec('pdftotext ' . $path . ' - | less', $content);
        $start = false;
        foreach ($content as $line) {
            // we only want this part here some PDF files do not have it
            if (trim($line) === '12 - CRITÉRIO DE ADJUDICAÇÃO') {
                $start = true;
                continue;
            }
            if (trim($line) === '13 - PRESTAÇÃO DE CAUÇÃO') {
                $start = false;
            }
            if ($start) {
                $finalContent .= $line . "\n\r";
            }
        }
        if (empty($finalContent)) {
            log_message('warning', 'We couldnt get the data, skip and go to the next one.');
            CLI::error('We couldnt get the data, skip and go to the next one.');
        } else {
            CLI::write('Data stored: ' . $finalContent);
        }
        model('\Basegov\Models\BaseGovAnnouncementModel')->update($announcement->id, ['doc' => 1, 'award_criteria' => $finalContent]);
    }

    /**
     * Parse Headers so we know what location to go to
     * @param  array   $url THe selected url data
     * @return array
     */
    protected function parseHeaders(array $url): array
    {
        $locations = [];
        foreach ($url as $value) {
            $strings = explode(':', $value);
            if ($strings[0] === 'location') {
                $locations[] = str_replace('location: ', '', $value);
            }
        }

        return $locations;
    }

}
