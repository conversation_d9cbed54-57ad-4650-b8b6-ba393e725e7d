<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class NotifyContracts extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'notifications';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'notify:contracts';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = 'Notify with all the contracts that were synced yesterday';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'notify:contracts';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [];

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        $data['date'] = $date = $params['date'] ?? date('Y-m-d', strtotime('-1 days'));
        $data['contracts'] = model('BaseGovModel')->where('publicationDate', $date)->orderBy('created_at', 'ASC')->findAll();
        if (empty($data['contracts'])) {
            CLI::error('No new alerts to send');
            exit;
        }

        $sent = service('notification')->initialize(['to' => '<EMAIL>'])
            ->subject('Contratos Públicos')
            ->message(view('emails/alert-contracts', $data))
            ->send();
        if ($sent) {
            CLI::write('The following email got the notification: <EMAIL>', 'green');
            exit;
        }
        log_message('critical', 'There was a problem while trying to send the <NAME_EMAIL>');
        CLI::error('There was a problem while trying to send the <NAME_EMAIL>');
    }
}
