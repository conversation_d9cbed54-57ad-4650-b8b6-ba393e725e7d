<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Cast language settings
return [
    'jsonErrorDepth'         => 'Profundidade máxima da pilha excedida',
    'jsonErrorStateMismatch' => 'Underflow ou a incompatibilidade de modos',
    'jsonErrorCtrlChar'      => 'Caractere de controlo inesperado encontrado',
    'jsonErrorSyntax'        => 'Erro de sintaxe, JSON formatado incorretamente',
    'jsonErrorUtf8'          => 'Caracteres UTF-8 formatados incorretamente, possivelmente codificação errada',
    'jsonErrorUnknown'       => 'Erro desconhecido',
];
