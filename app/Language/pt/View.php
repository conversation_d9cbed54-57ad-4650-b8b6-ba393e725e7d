<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// View language settings
return [
    'invalidCellMethod'     => '{class}::{method} não é um método válido.',
    'missingCellParameters' => '{class}::{method} não tem parâmetros.',
    'invalidCellParameter'  => '{0} não é um nome de parâmetro válido.',
    'noCellClass'           => 'Nenhuma classe view cell fornecida.',
    'invalidCellClass'      => 'Não é possível localizar a classe view cell: {0}.',
    'tagSyntaxError'        => 'Há um erro de sintaxe nas tags de Parser: {0}',
];
