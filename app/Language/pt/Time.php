<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Time language settings
return [
    'invalidMonth'   => 'Os meses devem estar entre 1 e 12. Fornecido: {0}',
    'invalidDay'     => 'Os dias devem estar entre 1 e 31. Fornecido: {0}',
    'invalidOverDay' => 'Os dias devem estar entre 1 e {0}. Fornecido: {1}',
    'invalidHours'   => 'As horas devem estar entre 0 e 23. Fornecido: {0}',
    'invalidMinutes' => 'Os minutos devem estar entre 0 e 59. Fornecido: {0}',
    'invalidSeconds' => 'Os segundos devem estar entre 0 e 59. Fornecido: {0}',
    'years'          => '{0, plural, =1{# ano} other{# anos}}',
    'months'         => '{0, plural, =1{# mês} other{# meses}}',
    'weeks'          => '{0, plural, =1{# semana} other{# semanas}}',
    'days'           => '{0, plural, =1{# dia} other{# dias}}',
    'hours'          => '{0, plural, =1{# hora} other{# horas}}',
    'minutes'        => '{0, plural, =1{# minuto} other{# minutos}}',
    'seconds'        => '{0, plural, =1{# segundo} other{# segundos}}',
    'ago'            => '{0} atás',
    'inFuture'       => 'em {0}',
    'yesterday'      => 'Ontem',
    'tomorrow'       => 'Amanhã',
    'now'            => 'Agora',
];
