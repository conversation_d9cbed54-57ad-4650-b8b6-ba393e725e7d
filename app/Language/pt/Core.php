<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Core language settings
return [
    'copyError'        => 'Um erro foi encontrado ao tentar substituir o arquivo. Por favor, certifique-se de que seu diretório tem permissões de escrita.',
    'invalidFile'      => 'Arquivo inválido: {0}',
    'missingExtension' => 'A extensão {0} não está carregada.',
    'noHandlers'       => '{0} deve fornecer pelo menos um handler.',
];
