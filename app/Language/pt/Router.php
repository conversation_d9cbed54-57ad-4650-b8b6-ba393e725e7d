<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Router language settings
return [
    'invalidParameter'    => 'Um parâmetro não corresponde ao tipo esperado.',
    'missingDefaultRoute' => 'Não é possível determinar o que deve ser exibido. Uma rota padrão não foi especificada no arquivo de redirecionamento.',
];
