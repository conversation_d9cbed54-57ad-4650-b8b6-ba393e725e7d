<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Database language settings
return [
    'invalidEvent'                     => '{0} não é um Model Event callback.',
    'invalidArgument'                  => 'Você deve fornecer um {0} válido.',
    'invalidAllowedFields'             => 'Os campos permitidos devem ser especificados para o model: {0}',
    'emptyDataset'                     => 'Não há dados para {0}.',
    'failGetFieldData'                 => 'Não foi possível receber informação da base de dados.',
    'failGetIndexData'                 => 'Não foi possível receber o index da base de dados.',
    'failGetForeignKeyData'            => 'Não foi possível receber a Chave Forasteira da base de dados.',
    'parseStringFail'                  => 'Análise da chave string falhou.',
    'featureUnavailable'               => 'Esta funcionalidade não se encontra disponível na base de dados selecionada.',
    'tableNotFound'                    => 'A tabela `{0}` não foi encontrada na base de dados selecionada.',
    'noPrimaryKey'                     => 'Classe model `{0}` não especifica uma Chave Primária.',
    'noDateFormat'                     => 'Classe model `{0}` não tem uma data válida.',
    'fieldNotExists'                   => 'Campo `{0}` não encontrado.',
    'forEmptyInputGiven'               => 'Declaração vazia passada para o campo `{0}`',
    'forFindColumnHaveMultipleColumns' => 'Apenas uma coluna é permitida no nome da coluna.',
];
