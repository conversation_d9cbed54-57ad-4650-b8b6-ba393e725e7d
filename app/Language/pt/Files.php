<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Files language settings
return [
    'fileNotFound' => 'Arquivo não encontrado: {0}',
    'cannotMove'   => 'Não foi possível mover o arquivo {0} para {1} ({2})',
];
