<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Encryption language settings
return [
    'noDriverRequested'    => 'Nenhum driver solicitado; Miss Daisy vai ficar tão chateada!',
    'noHandlerAvailable'   => 'Não foi possível encontrar um manipulador de criptografia {0} disponível.',
    'unKnownHandler'       => '"{0}" não pode ser configurado.',
    'starterKeyNeeded'     => 'A biblioteca de encriptação precisa de uma chave inicial.',
    'authenticationFailed' => 'Descriptografando: autenticação falhou.',
    'encryptionFailed'     => 'Encriptação falhou.',
];
