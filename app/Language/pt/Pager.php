<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Pager language settings
return [
    'pageNavigation'         => 'Navegação de página',
    'first'                  => 'Primeiro',
    'previous'               => 'Anterior',
    'next'                   => 'Próxima',
    'last'                   => 'Última',
    'older'                  => 'Antiga',
    'newer'                  => 'Nova',
    'invalidTemplate'        => '{0} não é um modelo de paginação válido.',
    'invalidPaginationGroup' => '{0} não é um grupo de paginação válido.',
];
