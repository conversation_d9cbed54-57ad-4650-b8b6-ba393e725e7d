<?php

/**
 * This file is part of CodeIgniter 4 framework.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

// Filters language settings
return [
    'noFilter'           => 'O filtro \'{0}\' deve ter um alias (alcunha) correspondente definido.',
    'incorrectInterface' => '{0} deve ser implementado CodeIgniter\Filters\FilterInterface.',
];
