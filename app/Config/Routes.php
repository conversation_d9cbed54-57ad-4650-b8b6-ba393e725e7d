<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Dashboard::index', ['subdomain' => 'app', 'filter' => 'isLoggedIn']);

// PRE-NOTICES
$routes->group('pre-notices', ['subdomain' => 'app', 'filter' => 'isLoggedIn'], static function ($routes) {
    $routes->get('/', 'PreNotices::index');
    $routes->get('detail/(:num)', 'PreNotices::detail/$1');
    $routes->get('detail/(:num)/(:any)', 'PreNotices::detail/$1/$2');
});

// NOTICES
$routes->group('notices', ['subdomain' => 'app'], static function ($routes) {
    $routes->get('/', 'Notices::index', ['filter' => 'setDefaultSearch']);
    $routes->get('europe', 'Notices::europe', ['filter' => 'isLoggedIn']);
    $routes->get('europe/detail/(:num)/(:any)', 'Notices::europeDetail/$1');
    $routes->get('detail/(:num)', 'Notices::detail/$1');
    $routes->get('detail/(:num)/(:any)', 'Notices::detail/$1/$2');
    $routes->get('documents/download/(:num)', 'Notices::documentsDownload/$1', ['filter' => 'isLoggedIn']);
    $routes->get('documents/export/national/(:num)', 'Notices::exportNational/$1', ['filter' => 'isLoggedIn']);
    $routes->get('documents/export/prr/(:num)', 'Notices::exportPrr/$1', ['filter' => 'isLoggedIn']);
    $routes->get('prr', 'Notices::prr');
    $routes->get('prr/detail/(:num)/(:any)', 'Notices::prrDetail/$1');
});

// ARTICLES
$routes->group('articles', ['subdomain' => 'app', 'filter' => 'isLoggedIn'], static function ($routes) {
    $routes->get('/', 'Articles::index');
    $routes->get('detail/(:any)', 'Articles::detail/$1');
});

// BOOKMARKS
$routes->group('bookmarks', ['subdomain' => 'app', 'filter' => 'isLoggedIn'], static function ($routes) {
    $routes->get('/', 'Bookmarks::index');
    $routes->get('prr', 'Bookmarks::prr');
    $routes->get('europe', 'Bookmarks::europe');
    $routes->get('save/(:num)', 'Bookmarks::save/$1');
    $routes->get('remove/(:num)', 'Bookmarks::remove/$1');
});

// AUTHENTICATION
$routes->group('auth', ['namespace' => 'App\Controllers', 'subdomain' => 'app'], static function ($routes) {
    $routes->get('login', 'Auth::loginView', ['filter' => \App\Filters\LoginRedirect::class]);
    $routes->post('login', 'Auth::loginAction', ['filter' => \App\Filters\CheckUserActivated::class]);
    $routes->get('register', 'Auth::registerView');
    $routes->post('register', 'Auth::registerAction');
    $routes->get('forgot-password', 'Auth::forgotPasswordView');
    $routes->post('forgot-password', 'Auth::forgotPasswordAction');
    $routes->get('reset-password', 'Auth::resetPasswordView', ['filter' => \App\Filters\CheckPasswordToken::class]);
    $routes->post('reset-password', 'Auth::resetPasswordAction');
    $routes->get('validate-account', 'Auth::validateAccount');
    $routes->get('manage', 'Auth::manage', ['filter' => 'isLoggedIn']);
    $routes->post('save', 'Auth::save', ['filter' => 'isLoggedIn']);
    $routes->get('logout', static function () {
        auth()->logout();
        return redirect()->to('auth/login')->with('confirm', 'Terminou a sessão com sucesso');
    });
    $routes->get('delete-img/(:num)', static function ($id) {
        model('\App\Models\UserDetailModel')->setUploadedImageToDelete(['id' => [$id]]);
        model('\App\Models\UserDetailModel')->deleteUploadedImage(['id' => [$id]]);
        return redirect()->back();
    });
});

$routes->group('auth', static function ($routes) {
    service('auth')->routes($routes, ['except' => ['auth/login', 'auth/register']]);
});

$routes->group('alerts', static function ($routes) {
    $routes->post('ask-for-help', static function () {
        $message = null;
        foreach (service('request')->getPost() as $name => $value) {
            $message .= '<strong>' . $name . '</strong>: ' . $value . '<br>';
        }
        $sent = service('notification')->initialize(['to' => service('settings')->get('Radar.consultingEmail')])
            ->subject('Pedido de consultoria Radar de Fundos Europeus')
            ->message(view('emails/general', [
                'title' => 'Foi efetuado um pedido de consultoria no Radar de Fundos Europeus',
                'text' => $message,
            ]))->send();
        if ($sent) {
            return redirect()->back()->with('confirm', 'O seu pedido de contacto foi enviado com sucesso!');
        }

        return redirect()->back()->with('error', 'Houve um problema ao enviar o seu pedido de contacto. Por favor contacte diretamente ' . service('settings')->get('Radar.consultingEmail'));
    });
});
