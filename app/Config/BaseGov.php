<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class BaseGov extends BaseConfig
{
    public array $export = [
        [
            'local' => 'A',
            'name' => 'ID',
            'field' => 'id',
        ],
        [
            'local' => 'B',
            'name' => 'NIF',
            'field' => 'vat',
        ],
        [
            'local' => 'C',
            'name' => 'Tipo de procedimento',
            'field' => 'contractingProcedureType',
        ],
        [
            'local' => 'D',
            'name' => 'Procedimento centralizado',
            'field' => 'centralizedProcedure',
        ],
        [
            'local' => 'E',
            'name' => 'Prazo de execução',
            'field' => 'executionDeadline',
        ],
        [
            'local' => 'F',
            'name' => 'Tipo de contrato CS',
            'field' => 'contractTypeCS',
        ],
        [
            'local' => 'G',
            'name' => 'Local de execução',
            'field' => 'executionPlace',
        ],
        [
            'local' => 'H',
            'name' => 'CPVS',
            'field' => 'cpvs',
        ],
        [
            'local' => 'I',
            'name' => 'Justificação para não redução a escrito do contrato',
            'field' => 'nonWrittenContractJustificationTypes',
        ],
        [
            'local' => 'J',
            'name' => 'Data da publicação',
            'field' => 'publicationDate',
        ],
        [
            'local' => 'K',
            'name' => 'Peças do procedimento',
            'field' => 'contractingProcedureUrl',
        ],
        [
            'local' => 'L',
            'name' => 'Tipo de Finalização contrato',
            'field' => 'endOfContractType',
        ],
        [
            'local' => 'M',
            'name' => 'Preço total efetivo',
            'field' => 'totalEffectivePrice',
        ],
        [
            'local' => 'N',
            'name' => 'Anúncio',
            'field' => 'announcementId',
        ],
        [
            'local' => 'O',
            'name' => 'CCP',
            'field' => 'ccp',
        ],
        [
            'local' => 'P',
            'name' => 'Entidades adjudicantes',
            'field' => 'contracting',
        ],
        [
            'local' => 'Q',
            'name' => 'Entidades adjudicatárias',
            'field' => 'contracted',
        ],
        [
            'local' => 'R',
            'name' => 'Preço contratual',
            'field' => 'initialContractualPrice',
        ],
        [
            'local' => 'S',
            'name' => 'Critérios materiais',
            'field' => 'materialCriteria',
        ],
        [
            'local' => 'T',
            'name' => 'Tipos de contrato',
            'field' => 'contractTypes',
        ],
        [
            'local' => 'V',
            'name' => 'Objeto do contrato',
            'field' => 'objectBriefDescription',
        ],
        [
            'local' => 'W',
            'name' => 'Data do contrato',
            'field' => 'signingDate',
        ],
        [
            'local' => 'X',
            'name' => 'Membro de aquisição UE',
            'field' => 'aquisitionStateMemberUE',
        ],
        [
            'local' => 'Y',
            'name' => 'Info Membro de aquisição UE',
            'field' => 'infoAquisitionStateMemberUE',
        ],
        [
            'local' => 'AA',
            'name' => 'Tipologia da medida especial',
            'field' => 'specialMeasures',
        ],
        [
            'local' => 'AB',
            'name' => 'Regime',
            'field' => 'regime',
        ],
        [
            'local' => 'AC',
            'name' => 'Tipologia CPVS',
            'field' => 'cpvsType',
        ],
        [
            'local' => 'AD',
            'name' => 'Designação CPVS',
            'field' => 'cpvsDesignation',
        ],
        [
            'local' => 'AE',
            'name' => 'Valor CPVS',
            'field' => 'cpvsValue',
        ],
        [
            'local' => 'AF',
            'name' => 'Descrição',
            'field' => 'description',
        ],
        [
            'local' => 'AG',
            'name' => 'Fundamentação',
            'field' => 'contractFundamentationType',
        ],
        [
            'local' => 'AH',
            'name' => 'Incrementos',
            'field' => 'increments',
        ],
        [
            'local' => 'AI',
            'name' => 'Data do fecho do contrato',
            'field' => 'closeDate',
        ],
        [
            'local' => 'AJ',
            'name' => 'Causas das alterações ao prazo',
            'field' => 'causesDeadlineChange',
        ],
        [
            'local' => 'AK',
            'name' => 'Causas das alterações ao preço',
            'field' => 'causesPriceChange',
        ],
        [
            'local' => 'AL',
            'name' => 'Nº do acordo quadro',
            'field' => 'frameworkAgreementProcedureId',
        ],
        [
            'local' => 'AM',
            'name' => 'Descrição do acordo quadro',
            'field' => 'frameworkAgreementProcedureDescription',
        ],
        [
            'local' => 'AN',
            'name' => 'Fundamentação para recurso ao ajuste direto',
            'field' => 'directAwardFundamentationType',
        ],
        [
            'local' => 'AO',
            'name' => 'Critérios ambientais',
            'field' => 'ambientCriteria',
        ],
        [
            'local' => 'AP',
            'name' => 'Observações',
            'field' => 'observations',
        ],
    ];
}
