<?php

namespace Config;

use CodeIgniter\Config\BaseService;

/**
 * Services Configuration file.
 *
 * Services are simply other classes/libraries that the system uses
 * to do its job. This is used by CodeIgniter to allow the core of the
 * framework to be swapped out easily without affecting the usage within
 * the rest of your application.
 *
 * This file holds any application-specific services, or service overrides
 * that you might need. An example has been included with the general
 * method format you should use for your service methods. For more examples,
 * see the core Services file at system/Config/Services.php.
 */
class Services extends BaseService
{

    /**
     * Prenotice service for all european data related
     * @param boolean $getShared
     */
    public static function prenotice($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('prenotice');
        }

        return new \App\Services\Prenotice(
            new \App\Models\PreNoticeModel(),
            new \App\Models\AreaModel(),
            new \App\Models\PreNoticeAreaModel(),
            new \App\Models\PreNoticeLinkModel()
        );
    }

    /**
     * Users service to manage users
     * @param boolean $getShared
     */
    public static function user($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('user');
        }

        return new \App\Services\User(
            new \App\Models\UserDetailModel(),
            new \App\Models\UserIdentityModel(),
        );
    }

    /**
     * Get national notices
     * @param boolean $getShared
     */
    public static function national($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('national');
        }

        return new \App\Services\National(
            new \App\Models\NoticeNationalModel(),
            new \App\Models\NoticiesNationalExtraModel(),
            new \App\Models\NoticeNationalTextModel(),
            new \App\Models\NoticiesNationalDocumentModel(),
            new \App\Models\CityModel()
        );
    }

    /**
     * Program segmentation
     * @param boolean $getShared
     */
    public static function segmentation($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('segmentation');
        }

        return new \Admin\Services\Segmentation(
            new \App\Models\CityModel(),
            new \App\Models\NoticiesNationalExtraModel(),
            new \App\Models\NoticiesNationalExtraCityModel(),
            new \App\Models\NoticiesNationalExtraCimModel(),
        );
    }

    /**
     * PRR segmentation
     * @param boolean $getShared
     */
    public static function prrSegmentation($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('prrSegmentation');
        }

        return new \Admin\Services\PrrSegmentation(
            new \App\Models\CityModel(),
            new \App\Models\NoticePrrModel(),
            new \App\Models\NoticePrrCityModel(),
            new \App\Models\NoticePrrCimModel(),
        );
    }

    /**
     * Notification service
     * @param boolean $getShared
     */
    public static function notification($getShared = true)
    {
        if ($getShared) {
            return static::getSharedInstance('notification');
        }

        return new \App\Services\Notification(
            \Config\Services::email()
        );
    }
}
