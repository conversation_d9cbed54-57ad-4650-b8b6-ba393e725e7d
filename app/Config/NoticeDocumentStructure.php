<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class NoticeDocumentStructure extends BaseConfig
{
    public $chapters = [
        [
            'name' => 'Finalidades e objetivos',
            'sync' => true,
        ],
        [
            'name' => 'Dota<PERSON>',
            'sync' => false,
        ],
        [
            'name' => 'Enquadramento em instrumentos territoriais',
            'sync' => true,
        ],
        [
            'name' => 'Legislação nacional',
            'sync' => false,
        ],
        [
            'name' => 'Ações elegíveis',
            'sync' => true,
        ],
        [
            'name' => 'Entidades beneficiárias (incluindo destinatários, quando relevante)',
            'sync' => true,
        ],
        [
            'name' => 'Condições específicas ou normas técnicas a observar pelos beneficiários ou operações',
            'sync' => true,
        ],
        [
            'name' => 'Modalidade de apresentação',
            'sync' => false,
        ],
        [
            'name' => 'Condições de atribuição de financiamento da operação',
            'sync' => false,
        ],
        [
            'name' => 'Auxílios de Estado',
            'sync' => false,
        ],
        [
            'name' => 'Custos elegíveis',
            'sync' => true,
        ],
        [
            'name' => 'Regras ou limites específicos à elegibilidade de despesa (Quando apl icável)',
            'sync' => true,
        ],
        [
            'name' => 'Formas de pagamento',
            'sync' => false,
        ],
        [
            'name' => 'Consequências do incumprimento dos indicadores',
            'sync' => false,
        ],
    ];
}
