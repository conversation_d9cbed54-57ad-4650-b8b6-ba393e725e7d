<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class SetDefaultSearch implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param  RequestInterface $request
     * @param  array|null       $arguments
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        if (!auth()->user()) {
            return redirect()->to('auth/login');
        }
        if (!empty($_GET)) {
            return;
        }

        $search = [
            'clearUp' => 0,
            'search' => null,
            'dateEnd' => null,
        ];

        // Check if user has any segmentation set
        $userId = auth()->user()->id;
        $userDetail = model('\App\Models\UserDetailModel')->where('user_id', $userId)->first();
        $search['districts'] = [$userDetail->district_id];
        $search['cities'] = [$userDetail->city_id];
        $search['cims'] = [$userDetail->cim_id];
        $userTypes = array_column(
            model('App\Models\UserDetailTypesModel')->asArray()->where('user_details_id', $userDetail->id)->findAll(),
            'type_id'
        );
        if (!empty($userTypes)) {
            $search['types'] = $userTypes;
        }
        $userThematics = array_column(
            model('App\Models\UserDetailThematicsModel')->asArray()->where('user_details_id', $userDetail->id)->findAll(),
            'thematic_id'
        );
        if (!empty($userThematics)) {
            $search['thematics'] = $userThematics;
        }
        $userSectors = array_column(
            model('App\Models\UserDetailSectorsModel')->asArray()->where('user_details_id', $userDetail->id)->findAll(),
            'sector_id'
        );
        if (!empty($userSectors)) {
            $search['sectors'] = $userSectors;
        }

        return redirect()->to('notices?' . http_build_query($search));
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param  RequestInterface  $request
     * @param  ResponseInterface $response
     * @param  array|null        $arguments
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {

    }
}
