<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;
use DateTime;

class CheckPasswordToken implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param  RequestInterface $request
     * @param  array|null       $arguments
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $userId = $request->getGet('u');
        $token = $request->getGet('t');

        if (!$userId || !$token) {
            return redirect()->to('auth/forgot-password')->with('error', 'Não foi possível ser redirecionado para a recuperação da password');
        }

        $user = auth()->getProvider()->findById($userId);
        if (!$user) {
            // que mensagem devo mostrar?
            return redirect()->to('auth/forgot-password')->with('error', 'Os pârametros não correspondem a nenhum utilizador');
        }

        $identityData = $user->getEmailIdentity();
        if (!$identityData->force_reset) {
            return redirect()->to('auth/forgot-password')->with('error', 'Não foi feito nenhum pedido de recuperação de password para este utilizador');
        }
        if ($identityData->extra !== $token) {
            return redirect()->to('auth/forgot-password')->with('error', 'Não é possível recuperar a sua password com o token fornecido');
        }
        if (strtotime($identityData->expires) < strtotime(date('Y/m/d H:i:s'))) {
            // set force_reset = 0
            $user->undoForcePasswordReset();
            return redirect()->to('auth/forgot-password')->with('error', 'Excedeu o tempo limite para recuperar a sua password');
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param  RequestInterface  $request
     * @param  ResponseInterface $response
     * @param  array|null        $arguments
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
