<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class CheckUserActivated implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param  RequestInterface $request
     * @param  array|null       $arguments
     * @return mixed
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        // get post data and unset password to find user
        $postData = array_filter($request->getPost(), 'strlen');
        unset($postData['chk_remember']);

        $user = auth()->check($postData);
        if (!$user->isOK()) {
            return redirect()->back()->with('error', $user->reason());
        }

        $userData = $user->extraInfo();
        if (!$userData->inGroup('company', 'employee')) {
            return redirect()->back()->with('error', 'As credenciais inseridas correspondem a uma conta de administração. Efetue o login na página da administração');
        }
        if (!$userData->active) {
            return redirect()->back()->with('error', 'O utilizador ainda não validou a sua conta. Faça-o a partir do email enviado ou contacte um técnico');
        }

        if (!service('user')->validateUserSubscription($userData->id)) {
            return redirect()->back();
        }
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param  RequestInterface  $request
     * @param  ResponseInterface $response
     * @param  array|null        $arguments
     * @return mixed
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
