<?php

namespace App\Models;

use CodeIgniter\Model;

class DreItemDocumentModel extends Model
{
    protected $table = 'dre_items_documents';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'dre_item_id', 'name', 'local_document'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'dre_item_id' => 'required',
        'name' => 'required',
        'document' => 'uploaded[document]|max_size[document,40960]'
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedAttachmentToDelete'];
    protected $afterDelete = ['deleteUploadedAttachment'];
    protected $uploadedAttachment = null;

    /**
     * Upload attachment
     * @param  array      $data The program data
     * @return array|bool The program data with attachment file
     */
    protected function upload(array $data): array | bool
    {
        $document = service('request')->getFile('document');
        if (!is_file($document)) {
            return $data;
        }

        if (!$this->validate(['document' => $document])) {
            service('session')->setFlashdata('errors', 'Não foi possivel fazer upload do anexo.');
            return $data;
        }

        if ($document->getName()) {
            $fileName = $document->getRandomName();
            $document->move('uploads/dre/attachments', $fileName);
            $data['data']['local_document'] = $fileName;
        }

        return $data;
    }

    /**
     * Set selected attachment
     * @param  array $data The selected program
     * @return array The program data
     */
    protected function setUploadedAttachmentToDelete(array $data): array
    {
        $attachment = $this->find($data['id'][0])->local_document ?? null;
        if (!empty($attachment)) {
            $this->uploadedAttachment = $attachment;
        }

        return $data;
    }

    /**
     * Delete attachment
     * @param  array $data The selected program
     * @return array The program data
     */
    protected function deleteUploadedAttachment(array $data): array
    {
        if (!isset($data['result']) && !$this->update($data['id'][0], ['local_document' => null])) {

            return $data;
        }
        if (!empty($this->uploadedAttachment) && file_exists(FCPATH . '../public/uploads/dre/attachments/' . $this->uploadedAttachment)) {
            unlink(FCPATH . '../public/uploads/dre/attachments/' . $this->uploadedAttachment);
        }
        $this->uploadedAttachment = null;

        return $data;
    }
}
