<?php

namespace App\Models;

use CodeIgniter\Model;

class UserDetailModel extends Model
{
    protected $table = 'user_details';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id', 'user_type', 'company_id', 'start_date', 'due_date', 'max_employees', 'district_id', 'city_id', 'cim_id', 'image',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required',
        'user_type' => 'required',
        'image' => 'permit_empty|is_image[image]',
    ];
    protected $validationMessages = [
        'image' => [
            'is_image' => 'Não foi possivel fazer upload da imagem. Deve fazer upload de ficheiro de imagem válido',
        ],
    ];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload', 'setDistrict'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload', 'setDistrict'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedImageToDelete'];
    protected $afterDelete = ['deleteUploadedImage'];

    // property used to set uploaded image path before deleting the image
    protected $uploadedImage = null;

    /**
     * delete image -> used whenever an userdetail is deleted
     * @param  array $data The selected userdetail
     * @return array The userdetail data
     */
    public function deleteUploadedImage(array $data): array
    {
        // update image column
        if (!isset($data['result']) && !$this->update($data['id'][0], ['image' => null])) {
            return $data;
        }

        // delete image
        if (!empty($this->uploadedImage) && file_exists(FCPATH . '../public/uploads/users/images/' . $this->uploadedImage)) {
            unlink(FCPATH . '../public/uploads/users/images/' . $this->uploadedImage);
        }

        $this->uploadedImage = null;

        return $data;
    }

    /**
     * set uploaded image path before delete image
     * @param  array $data The selected userDetail
     * @return array The userDetail data
     */
    public function setUploadedImageToDelete(array $data): array
    {
        $image = $this->find($data['id'][0])->image;
        if (!empty($image)) {
            $this->uploadedImage = $image;
        }

        return $data;
    }

    /**
     * Add district information to a user
     * @param array $user data
     */
    protected function setDistrict(array $user)
    {
        if (!isset($user['data']['city_id'])) {
            return $user;
        }
        $db = \Config\Database::connect();
        $builder = $db->table('geo_cities');
        $city = $builder->where('id', $user['data']['city_id'])->get()->getRow();
        $user['data']['district_id'] = $city->district_id;

        return $user;
    }

    /**
     * Upload image
     * @param  array      $data The userDetail data
     * @return array|bool The userDetail data with image file
     */
    protected function upload(array $data): array | bool
    {
        $image = service('request')->getFile('image');
        if (!is_file($image)) {
            return $data;
        }

        if (!$this->validate(['image' => $image])) {
            return $data;
        }

        if ($image->getName()) {
            $fileName = $image->getRandomName();
            $image->move('uploads/users/images', $fileName);
            $data['data']['image'] = $fileName;
        }

        return $data;
    }
}
