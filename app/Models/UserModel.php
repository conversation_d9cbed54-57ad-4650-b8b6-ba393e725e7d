<?php

declare (strict_types = 1);

namespace App\Models;

use CodeIgniter\Shield\Models\UserModel as ShieldUserModel;

class UserModel extends ShieldUserModel
{
    protected function initialize(): void
    {
        parent::initialize();

        $this->allowedFields = [
            ...$this->allowedFields,
        ];

        $this->validationRules = [
            'username' => [
                'label' => 'NIF',
                'rules' => [
                    'permit_empty',
                    'max_length[9]',
                    'is_numeric',
                    'min_length[9]',
                    'is_unique[users.username]',
                ],
                'errors' => [
                    'min_length' => 'O campo NIF deve conter 9 digitos',
                    'is_unique' => 'Já existe um utilizador com o NIF inserido',
                    'is_numeric' => 'O NIF deve ser composto apenas por dígitos',
                ],
            ],
        ];
    }

    /**
     * Get all user details -> if userId is null, show all
     * @param  string $group
     * @param  int    $userId
     * @param  array  $filters  filters to build query
     * @return array  result data
     */
    public function getUserDetails(string $group = null, int $userId = null, array $filters = []): array
    {
        $this->select('users.*, user_details.id as userDetailId, user_details.max_employees, user_details.start_date,
            user_details.due_date, auth_identities.secret, user_details.user_type, user_details.district_id,
            user_details.city_id, user_details.cim_id, user_details.company_id, user_details.image,
            (
                SELECT COUNT(*)
                FROM user_details
                WHERE user_details.company_id = users.id
            ) as count_employees');
        $this->join('auth_groups_users', 'auth_groups_users.user_id = users.id');
        $this->join('user_details', 'user_details.user_id = users.id');
        $this->join('auth_identities', 'auth_identities.user_id = users.id');
        if ($group !== null) {
            $this->where('auth_groups_users.group', $group);
        }
        if ($userId !== null) {
            $this->where('users.id', $userId);
        }
        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this->findAll();
    }

    /**
     * Get all employees from company
     * @param  int   $companyId
     * @return array result data
     */
    public function getEmployees(int $companyId): array
    {
        $this->select('users.*');
        $this->join('auth_groups_users', 'auth_groups_users.user_id = users.id');
        $this->join('user_details', 'user_details.user_id = users.id');
        $this->where('auth_groups_users.group', 'employee');
        $this->where('user_details.company_id', $companyId);

        return $this->findAll();
    }

    /**
     * Get only the admins
     * @return array
     */
    public function getAdmins(): array
    {
        $this->select('users.*');
        $this->join('auth_groups_users', 'auth_groups_users.user_id = users.id');
        $this->whereIn('auth_groups_users.group', ['admin', 'superadmin']);

        return $this->findAll();
    }
}
