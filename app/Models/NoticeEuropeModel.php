<?php
namespace App\Models;

use CodeIgniter\Model;
use \DateTime;

class NoticeEuropeModel extends Model
{
    protected $table = 'nt_notices_europe';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'program_id', 'thematic_id', 'code', 'name', 'start_date', 'end_date', 'link',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'program_id' => 'required',
        'start_date' => 'required',
        'end_date' => 'required',
        'link' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get an icon from one of the sectors that might be associated with the notice
     * @return object
     */
    public function withIconAndBookmark(): object
    {
        $this->select('nt_notices_europe.*, (
            SELECT icon
                FROM nt_sectors
                JOIN nt_notice_europe_sectors ON nt_sectors.id = nt_notice_europe_sectors.sector_id
                WHERE nt_notice_europe_sectors.notice_id = nt_notices_europe.id
                ORDER BY RAND()
                LIMIT 1
            ) AS icon,
            (
            SELECT COUNT(user_bookmarks.europe_id)
                FROM user_bookmarks
                WHERE user_id = ' . auth()->user()->id . '
                AND europe_id = nt_notices_europe.id
                AND type = "europe"
            ) as bookmarks
        ');

        return $this;
    }

    /**
     * Show only bookmarked notices
     * @param bool $isBookmarked
     */
    public function bookmarked(bool $isBookmarked): NoticeEuropeModel
    {
        if (!$isBookmarked) {
            return $this;
        }
        $this->join('user_bookmarks', 'nt_notices_europe.id = user_bookmarks.europe_id');
        $this->where('user_id', auth()->user()->id);

        return $this;
    }

    /**
     * set filters for all the possible outcomes
     * @param  array    $filters Filters
     * @return object
     */
    public function filter(array $filters = [])
    {
        $filters = array_filter($filters);
        if (empty($filters)) {
            return $this;
        }
        if (isset($filters['orderBy'])) {
            $this->orderBy((($filters['orderBy'] === 'date') ? 'end_date' : 'id'), 'ASC');
        }
        if (isset($filters['search'])) {
            $this->like('nt_notices_europe.name', $filters['search']);
        }
        if (isset($filters['dateEnd'])) {
            $date = DateTime::createFromFormat('m/d/Y', $filters['dateEnd']);
            $this->where('end_date <=', $date->format('Y-m-d'));
        }
        if (isset($filters['organism_id'])) {
            $this->join('nt_notices_europe_programs', 'nt_notices_europe.program_id = nt_notices_europe_programs.id');
            $this->like('nt_notices_europe_programs.organism_id', $filters['organism_id']);
        }
        if (isset($filters['sectors'])) {
            $this->join('nt_notice_europe_sectors', 'nt_notices_europe.id = nt_notice_europe_sectors.notice_id');
            foreach ($filters['sectors'] as $sectorId) {
                $this->where('nt_notice_europe_sectors.sector_id', $sectorId);
            }
        }
        if (isset($filters['thematics'])) {
            $this->join('nt_notice_europe_thematics', 'nt_notices_europe.id = nt_notice_europe_thematics.notice_id');
            foreach ($filters['thematics'] as $thematicId) {
                $this->where('nt_notice_europe_thematics.thematic_id', $thematicId);
            }
        }
        if (isset($filters['types'])) {
            $this->join('nt_notice_europe_types', 'nt_notices_europe.id = nt_notice_europe_types.notice_id');
            foreach ($filters['types'] as $thematicId) {
                $this->where('nt_notice_europe_types.type_id', $thematicId);
            }
        }
        if (isset($filters['cims'])) {
            $this->join('nt_notices_europe_cims', 'nt_notices_europe.id = nt_notices_europe_cims.notice_id');
            foreach ($filters['cims'] as $thematicId) {
                $this->where('nt_notices_europe_cims.cim_id', $thematicId);
            }
        }
        unset(
            $filters['orderBy'], $filters['search'], $filters['dateEnd'], $filters['clearUp'],
            $filters['organism_id'], $filters['sectors'], $filters['thematics'], $filters['types'],
            $filters['cims']
        );

        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }

}
