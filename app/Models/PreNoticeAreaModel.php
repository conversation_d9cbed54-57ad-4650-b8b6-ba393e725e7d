<?php

namespace App\Models;

use CodeIgniter\Model;

class PreNoticeAreaModel extends Model
{
    protected $table = 'nt_prenotices_areas';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = ['europe_notice_id', 'area_id'];

    // Dates
    protected $useTimestamps = false;

    // Validation
    protected $validationRules = [
        'europe_notice_id' => 'required',
        'area_id' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];
}
