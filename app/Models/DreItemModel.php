<?php

namespace App\Models;

use CodeIgniter\Model;

class DreItemModel extends Model
{
    protected $table = 'dre_items';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'title', 'name', 'description', 'link', 'sent', 'promoter', 'ref', 'base_price', 'procedure',
        'platform', 'mono_price', 'date_clarify', 'hour_clarify', 'clarifications',
        'date_candidacy', 'hour_candidacy', 'date_proposal', 'hour_proposal',
        'decision', 'candidate', 'grouping', 'status', 'interest', 'interest_emails', 'notified', 'obs'
    ];
    protected $cleanUp = [
        'name', 'promoter', 'ref', 'base_price', 'procedure',
        'platform', 'mono_price', 'date_clarify', 'hour_clarify', 'clarifications',
        'date_candidacy', 'hour_candidacy', 'date_proposal', 'hour_proposal',
        'decision', 'candidate', 'grouping', 'interest_emails', 'obs', 'link'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'title' => 'required',
        'description' => 'required',
        'interest_emails' => 'permit_empty|valid_emails'
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['cleanUpFields'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['cleanUpFields'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = ['excludedWords', 'companyData'];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Filters
     * @param  array  $filters Set of base filters
     * @return this
     */
    public function filter(array $filters): DreItemModel
    {
        if (isset($filters['from']) && !empty($filters['from'])) {
            $filters['created_at >='] = $filters['from'];
        }
        if (isset($filters['to']) && !empty($filters['to'])) {
            $filters['created_at <='] = $filters['to'];
        }
        if (isset($filters['clarify_from']) && !empty($filters['clarify_from'])) {
            $filters['date_clarify >='] = $filters['clarify_from'];
        }
        if (isset($filters['clarify_to']) && !empty($filters['clarify_to'])) {
            $filters['date_clarify <='] = $filters['clarify_to'];
        }
        if (isset($filters['candidacy_from']) && !empty($filters['candidacy_from'])) {
            $filters['date_candidacy >='] = $filters['candidacy_from'];
        }
        if (isset($filters['candidacy_to']) && !empty($filters['candidacy_to'])) {
            $filters['date_candidacy <='] = $filters['candidacy_to'];
        }
        if (isset($filters['proposal_from']) && !empty($filters['proposal_from'])) {
            $filters['date_proposal >='] = $filters['proposal_from'];
        }
        if (isset($filters['proposal_to']) && !empty($filters['proposal_to'])) {
            $filters['date_proposal <='] = $filters['proposal_to'];
        }
        if (isset($filters['status']) && empty($filters['status'])) {
            unset($filters['status']);
        }
        if (isset($filters['search']) && !empty($filters['search'])) {
            $terms = explode(' ', $filters['search']);
            foreach ($terms as $term) {
                $this->groupStart();
                $this->orLike('name', $term);
                $this->orLike('title', $term);
                $this->orLike('description', $term);
                $this->orLike('ref', $term);
                $this->orLike('promoter', $term);
                $this->orLike('procedure', $term);
                $this->groupEnd();
            }
        }
        unset(
            $filters['from'], $filters['to'], $filters['clarify_from'], $filters['clarify_to'],
            $filters['candidacy_from'], $filters['candidacy_to'],
            $filters['proposal_from'], $filters['proposal_to'], $filters['search'],
            $filters['excluded'], $filters['page'], $filters['interest'], $filters['others']
        );

        return $this->where($filters);
    }

    /**
     * Get the company data foreach contest
     * @param  array $contests The contest data
     * @return array The contest data plus the company data
     */
    protected function companyData(array $contests): array
    {
        if (empty($contests['data'])) {

            return $contests;
        }
        $db = \Config\Database::connect();
        $builder = $db->table('dre_items_companies');
        if (isset($contests['data']->id)) {
            $contests['data']->companies = $builder->where('item_id', $contests['data']->id)
                ->select('dre_items_companies.*, dre_companies.name')
                ->join('dre_companies', 'dre_items_companies.company_id = dre_companies.id')
                ->get()->getResult();

            return $contests;
        }
        foreach ($contests['data'] as $data) {
            $data->companies = $builder->where('item_id', $data->id)
                ->select('dre_items_companies.*, dre_companies.name')
                ->join('dre_companies', 'dre_items_companies.company_id = dre_companies.id')
                ->get()->getResult();
        }

        return $contests;
    }

    /**
     * Get excluded words for each context if they exist
     * @param  array $contest The dre contests
     * @return array The dre contest with a excluded words field
     */
    protected function excludedWords(array $contests): array
    {
        helper('word');
        if (empty($contests['data'])) {

            return $contests;
        }
        foreach ($contests['data'] as $data) {
            if (!empty($data->description)) {
                $data->excluded = excluded(service('settings')->get('Dre.words'), $data->description);
            }
        }

        return $contests;
    }

    /**
     * Clean up empty fields
     * @param  array $contest The submitted dre contest
     * @return array The dre contest with fields cleaned up
     */
    protected function cleanUpFields(array $contest): array
    {
        foreach ($this->cleanUp as $field) {
            if (isset($contest['data'][$field]) && $contest['data'][$field] === '') {
                $contest['data'][$field] = null;
            }
        }

        return $contest;
    }
}
