<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticiesNationalExtraModel extends Model
{
    protected $table = 'nt_notices_national_extra';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'notice_id', 'programaOperacionalDesignacao', 'prioridadeDesignacao', 'objetivoEspecificoDesignacao',
        'tipologiaAcaoDesignacao', 'tipologiaIntervencaoDesignacao', 'tipologiaOperacaoDesignacao',
        'tipoFinanciamentoDesignacao', 'fundoDesignacao', 'fonteFinanciamentoNacionalDesignacao', 'dotacao',
        'estrategiaDesignacao', 'segmentation_type', 'cim', 'image', 'status',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'notice_id' => 'required',
        'programaOperacionalDesignacao' => 'required',
        'image' => 'permit_empty|is_image[image]',
    ];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedImageToDelete'];
    protected $afterDelete = ['deleteUploadedImage'];

    // property used to set uploaded image path before deleting the image
    protected $uploadedImage = null;

    /**
     * delete image -> used whenever an program/image is deleted
     * @param  array $data The selected program
     * @return array The program data
     */
    public function deleteUploadedImage(array $data): array
    {
        if (!isset($data['result']) && !$this->update($data['id'][0], ['image' => null])) {
            return $data;
        }
        if (!empty($this->uploadedImage) && file_exists(FCPATH . '../public/uploads/programs/images/' . $this->uploadedImage)) {
            unlink(FCPATH . '../public/uploads/programs/images/' . $this->uploadedImage);
        }
        $this->uploadedImage = null;

        return $data;
    }

    /**
     * set uploaded image path before delete image
     * @param  array $data The selected program
     * @return array The program data
     */
    public function setUploadedImageToDelete(array $data): array
    {
        $image = $this->find($data['id'][0])->image ?? null;
        if (!empty($image)) {
            $this->uploadedImage = $image;
        }

        return $data;
    }

    /**
     * Filter notices national extra
     * @param  array  $filters Filters to be used
     * @return object $this NoticiesNationalExtraModel
     */
    public function filter(array $filters = []): NoticiesNationalExtraModel
    {
        if (isset($filters['dateStart'])) {
            $this->where('dataInicio >=', date('Y-m-d H:i:s', strtotime($filters['dateStart'])));
        }
        if (isset($filters['dateEnd'])) {
            $this->where('dataFim <=', date('Y-m-d H:i:s', strtotime($filters['dateEnd'])));
        }

        unset($filters['dateStart'], $filters['dateEnd']);
        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }

    /**
     * Group programs by regions
     * @return object
     */
    public function groupByRegionalPrograms(): NoticiesNationalExtraModel
    {
        $this->select('geo_cities.nut_2 as name, COUNT(DISTINCT nt_notices_national_extra.id) as y')
            ->join('nt_notices_national_extra_cities as extra_cities', 'extra_cities.extra_id = nt_notices_national_extra.id')
            ->join('geo_cities', 'geo_cities.id = extra_cities.city_id')
            ->join('nt_notices_national', 'nt_notices_national.id = nt_notices_national_extra.notice_id')
            ->where('nt_notices_national.status', 'active')
            ->where('nt_notices_national_extra.status', 'active')
            ->groupBy('geo_cities.nut_2');

        return $this;
    }

    /**
     * Groups programs by investment types
     * @return object
     */
    public function groupByInvestmentTypes(): NoticiesNationalExtraModel
    {
        $this->select('nt_types.name, COUNT(DISTINCT nt_notices_national_extra.id) as count')
            ->join('nt_notice_types', 'nt_notice_types.extra_id = nt_notices_national_extra.id')
            ->join('nt_types', 'nt_types.id = nt_notice_types.type_id')
            ->join('nt_notices_national', 'nt_notices_national.id = nt_notices_national_extra.notice_id')
            ->where('nt_notices_national.status', 'active')
            ->where('nt_notices_national_extra.status', 'active')
            ->groupBy('nt_types.name');

        return $this;
    }

    /**
     * Upload image
     * @param  array $data The program data
     * @return array The program data with image file
     */
    protected function upload(array $data): array
    {
        $image = service('request')->getFile('image');
        if (!is_file($image)) {
            return $data;
        }
        if (!$this->validate(['image' => $image])) {
            service('session')->setFlashdata('errors', 'Não foi possivel fazer upload da imagem. Deve fazer upload de ficheiro de imagem válido.');
            return $data;
        }
        if ($image->getName()) {
            $fileName = $image->getRandomName();
            $image->move('uploads/programs/images', $fileName);
            $data['data']['image'] = $fileName;
        }

        return $data;
    }
}
