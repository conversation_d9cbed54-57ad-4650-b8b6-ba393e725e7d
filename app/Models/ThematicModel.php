<?php

namespace App\Models;

use CodeIgniter\Model;

class ThematicModel extends Model
{
    protected $table = 'nt_thematics';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = ['name'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'name' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Count uses of item
     * @return object
     */
    public function countEntitiesAndNoticies(): ThematicModel
    {
        $this->select('nt_thematics.*,
            (SELECT COUNT(id) FROM user_details_thematics WHERE thematic_id = nt_thematics.id) as user_thematics,
            (SELECT COUNT(id) FROM nt_notice_thematics WHERE thematic_id = nt_thematics.id) as notice_thematics
        ');

        return $this;
    }

}
