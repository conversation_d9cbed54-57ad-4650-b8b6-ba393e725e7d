<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticeNationalModel extends Model
{
    protected $table = 'nt_notices_national';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'avisoGlobalId', 'codigoAviso', 'designacaoPT', 'classificacaoAvisoDesignacao',
        'instrumentoTerritorialDesignacao', 'contextoAvisoInstrumentoDesignacao', 'dataUltimaAlteracao',
        'dataPublicacao', 'dataInicio', 'dataFim', 'dataFimAtual', 'tempoMedioDecisaoFinal', 'status',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'avisoGlobalId' => 'required',
        'designacaoPT' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Filter national notices
     * @param  array  $filters Filters to be used
     * @return object $this PreNoticeModel
     */
    public function filter(array $filters = []): NoticeNationalModel
    {
        if (isset($filters['dateStart'])) {
            $this->where('dataInicio >=', date('Y-m-d H:i:s', strtotime($filters['dateStart'])));
        }
        if (isset($filters['dateEnd'])) {
            $this->where('dataFim <=', date('Y-m-d H:i:s', strtotime($filters['dateEnd'])));
        }
        unset($filters['dateStart'], $filters['dateEnd']);

        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }

    /**
     * Get regional notices
     * @return object
     */
    public function getRegionalNotices(): NoticeNationalModel
    {
        $this->select('DISTINCT(nt_notices_national.id)')
            ->join('nt_notices_national_extra', 'nt_notices_national_extra.notice_id = nt_notices_national.id')
            ->join('nt_notices_national_extra_cities', 'nt_notices_national_extra_cities.extra_id = nt_notices_national_extra.id')
            ->where('nt_notices_national.status', 'active')
            ->groupBy('nt_notices_national.id');

        return $this;
    }
}
