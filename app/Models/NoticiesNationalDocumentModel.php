<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticiesNationalDocumentModel extends Model
{
    protected $table = 'nt_notices_national_documents';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'notice_id', 'documentoId', 'documentoDesignacao', 'tipoDocumentoDesignacao', 'documentoData', 'path',
        'local_document', 'status', 'parsed',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'notice_id' => 'required',
        'documentoId' => 'required',
        'documentoDesignacao' => 'required',
        'documentoData' => 'required',
        'path' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];
}
