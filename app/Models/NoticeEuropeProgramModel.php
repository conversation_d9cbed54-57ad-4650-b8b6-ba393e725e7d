<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticeEuropeProgramModel extends Model
{
    protected $table = 'nt_notices_europe_programs';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'organism_id', 'program', 'name', 'description', 'priority', 'objetives', 'to_whom', 'expenses', 'budget', 'tax',
        'starts_at', 'ends_at', 'website', 'image',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'program' => 'required',
        'image' => 'permit_empty|is_image[image]',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload', 'cleanUpOrganism'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload', 'cleanUpOrganism'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedImageToDelete'];
    protected $afterDelete = ['deleteUploadedImage'];
    protected $uploadedImage = null;

    /**
     * Clean up field because it would insert invalid string on empty
     * @param  array   $program Data program
     * @return array
     */
    protected function cleanUpOrganism(array $program): array
    {
        if (empty($program['data']['organism_id'])) {
            unset($program['data']['organism_id']);
        }

        return $program;
    }

    /**
     * delete image -> used whenever an program/image is deleted
     * @param  array $data The selected program
     * @return array The program data
     */
    public function deleteUploadedImage(array $data): array
    {
        // update image column
        if (!isset($data['result']) && !$this->update($data['id'][0], ['image' => null])) {
            return $data;
        }

        // delete image
        if (!empty($this->uploadedImage) && file_exists(FCPATH . '../public/uploads/programs/images/' . $this->uploadedImage)) {
            unlink(FCPATH . '../public/uploads/programs/images/' . $this->uploadedImage);
        }

        $this->uploadedImage = null;

        return $data;
    }

    /**
     * set uploaded image path before delete image
     * @param  array $data The selected program
     * @return array The program data
     */
    public function setUploadedImageToDelete(array $data): array
    {
        $image = $this->find($data['id'][0])->image ?? null;
        if (!empty($image)) {
            $this->uploadedImage = $image;
        }

        return $data;
    }

    /**
     * Upload image
     * @param  array      $data The program data
     * @return array|bool The program data with image file
     */
    protected function upload(array $data): array | bool
    {
        $image = service('request')->getFile('image');
        if (!is_file($image)) {
            return $data;
        }

        if (!$this->validate(['image' => $image])) {
            service('session')->setFlashdata('errors', 'Não foi possivel fazer upload da imagem. Deve fazer upload de ficheiro de imagem válido.');
            return $data;
        }

        if ($image->getName()) {
            $fileName = $image->getRandomName();
            $image->move('uploads/programs/images', $fileName);
            $data['data']['image'] = $fileName;
        }

        return $data;
    }
}
