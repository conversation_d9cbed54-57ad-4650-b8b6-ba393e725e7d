<?php

namespace App\Models;

use CodeIgniter\Model;

class UserDetailThematicsModel extends Model
{
    protected $table = 'user_details_thematics';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = ['user_details_id', 'thematic_id'];

    // Dates
    protected $useTimestamps = false;

    // Validation
    protected $validationRules = [
        'user_details_id' => 'required',
        'thematic_id' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];
}
