<?php

namespace App\Models;

use CodeIgniter\Model;

class PreNoticeModel extends Model
{
    protected $table = 'nt_prenotices';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'link_id', 'origin', 'title', 'tags', 'description', 'start_date', 'end_date', 'status',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'link_id' => 'required',
        'origin' => 'required',
        'title' => 'required',
        'tags' => 'required',
        'description' => 'required',
        'start_date' => 'required',
        'end_date' => 'required',
        'status' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Filter pre-notices
     * @param  array  $filters Filters to be used
     * @return object $this PreNoticeModel
     */
    public function filter(array $filters = []): PreNoticeModel
    {
        if (isset($filters['search'])) {
            $this->groupStart()
                ->orLike('title', $filters['search'])
                ->orLike('description', $filters['search']);
            $this->groupEnd();
        }
        if (isset($filters['dateStart'])) {
            $this->where('start_date >=', date('Y-m-d H:i:s', strtotime($filters['dateStart'])));
        }
        if (isset($filters['dateEnd'])) {
            $this->where('end_date <=', date('Y-m-d H:i:s', strtotime($filters['dateEnd'])));
        }

        unset($filters['search'], $filters['dateStart'], $filters['dateEnd']);
        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }

    /**
     * Group pre-notices by area
     * @return object
     */
    public function groupByArea(): PreNoticeModel
    {
        $this->select('nt_areas.name, COUNT(nt_prenotices.id) as count')
            ->join('nt_prenotices_areas', 'nt_prenotices_areas.europe_notice_id = nt_prenotices.id')
            ->join('nt_areas', 'nt_areas.id = nt_prenotices_areas.area_id')
            ->groupBy('nt_areas.name');

        return $this;
    }

    /**
     * Group pre-notices by tag
     * @return object
     */
    public function groupByTag(): PreNoticeModel
    {
        $this->select('nt_prenotices.tags as name, COUNT(nt_prenotices.id) as count')
            ->where('nt_prenotices.tags !=', '')
            ->groupBy('tags');

        return $this;
    }
}
