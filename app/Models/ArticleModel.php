<?php

namespace App\Models;

use CodeIgniter\Model;

class ArticleModel extends Model
{
    protected $table = 'articles';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = ['title', 'intro', 'description', 'image', 'tags', 'source', 'schedule'];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'title' => 'required',
        'intro' => 'required',
        'description' => 'required',
        'schedule' => 'required',
        'image' => 'permit_empty|is_image[image]',
    ];
    protected $validationMessages = [
        'description' => [
            'required' => 'O conteúdo da notícia é obrigatório',
        ],
        'image' => [
            'is_image' => 'Não foi possivel fazer upload da imagem. Deve fazer upload de ficheiro de imagem válido.',
        ],
    ];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload', 'generateSlug'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload', 'generateSlug'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedImageToDelete'];
    protected $afterDelete = ['deleteUploadedImage'];

    // property used to set uploaded image path before deleting the image
    protected $uploadedImage = null;

    /**
     * delete image -> used whenever an article/image is deleted
     * @param  array $data The selected article
     * @return array The article data
     */
    public function deleteUploadedImage(array $data): array
    {
        // update image column
        if (!isset($data['result']) && !$this->update($data['id'][0], ['image' => null])) {
            return $data;
        }

        // delete image
        if (!empty($this->uploadedImage) && file_exists(FCPATH . '../public/uploads/images/' . $this->uploadedImage)) {
            unlink(FCPATH . '../public/uploads/images/' . $this->uploadedImage);
        }

        $this->uploadedImage = null;

        return $data;
    }

    /**
     * set uploaded image path before delete image
     * @param  array $data The selected article
     * @return array The article data
     */
    public function setUploadedImageToDelete(array $data): array
    {
        $image = $this->find($data['id'][0])->image;
        if (!empty($image)) {
            $this->uploadedImage = $image;
        }

        return $data;
    }

    /**
     * search by text
     * @param  string $search
     * @return this   result data
     */
    public function searchBy(string $search = null)
    {
        if (empty($search)) {
            return $this;
        }

        $this->groupStart();
        $this->like('title', $search)
            ->orLike('intro', $search)
            ->orLike('tags', $search)
            ->orLike('description', $search)
            ->orLike('source', $search);
        $this->groupEnd();

        return $this;
    }

    /**
     * Upload image
     * @param  array $data The article data
     * @return array The article data with image file
     */
    protected function upload(array $data): array
    {
        $image = service('request')->getFile('image');
        if (!is_file($image)) {
            return $data;
        }

        if ($image->getName()) {
            $fileName = $image->getRandomName();
            $image->move('uploads/images', $fileName);
            $data['data']['image'] = $fileName;
        }

        return $data;
    }

    /**
     * generate slug
     * @param  array $data The article data
     * @return array The article data with slug
     */
    protected function generateSlug(array $data): array
    {
        $id = $data['id'][0] ?? null;
        if (!$id) {
            // get last id and increment to concatenate in the slug
            $lastID = $this->orderBy('id', 'desc')->limit(1)->first()->id ?? 0;
            $id = $lastID + 1;
        }

        if (isset($data['data']['title'])) {
            $slug = $id . '-' . url_title(convert_accented_characters($data['data']['title']), '-', true);
            $data['data']['slug'] = (strlen($slug) > 255) ? substr($slug, 0, 255) : $slug;
        }

        return $data;
    }
}
