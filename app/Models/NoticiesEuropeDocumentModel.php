<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticiesEuropeDocumentModel extends Model
{
    protected $table = 'nt_notices_europe_documents';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'name', 'status', 'notice_id', 'local_document',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'notice_id' => 'required',
        'name' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['upload'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['upload'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = ['setUploadedDocumentToDelete'];
    protected $afterDelete = ['deleteUploadedDocument'];
    protected $uploadedDocument = null;

    /**
     * Upload image
     * @param  array      $data The program data
     * @return array|bool The program data with image file
     */
    protected function upload(array $data): array | bool
    {
        $document = service('request')->getFile('document');
        if (!is_file($document)) {
            return $data;
        }

        if (!$this->validate(['document' => $document])) {
            service('session')->setFlashdata('errors', 'Não foi possivel fazer upload do documento.');
            return $data;
        }

        if ($document->getName()) {
            $fileName = $document->getRandomName();
            $document->move('uploads/programs/documents', $fileName);
            $data['data']['local_document'] = $fileName;
        }

        return $data;
    }

    /**
     * Set selected document
     * @param  array $data The selected program
     * @return array The program data
     */
    protected function setUploadedDocumentToDelete(array $data): array
    {
        $image = $this->find($data['id'][0])->image ?? null;
        if (!empty($image)) {
            $this->uploadedDocument = $image;
        }

        return $data;
    }

    /**
     * Delete document
     * @param  array $data The selected program
     * @return array The program data
     */
    protected function deleteUploadedDocument(array $data): array
    {
        if (!isset($data['result']) && !$this->update($data['id'][0], ['image' => null])) {
            return $data;
        }
        if (!empty($this->uploadedDocument) && file_exists(FCPATH . '../public/uploads/programs/documents/' . $this->uploadedDocument)) {
            unlink(FCPATH . '../public/uploads/programs/documents/' . $this->uploadedDocument);
        }
        $this->uploadedDocument = null;

        return $data;
    }
}
