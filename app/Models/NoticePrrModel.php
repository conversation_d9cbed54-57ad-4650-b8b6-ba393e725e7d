<?php

namespace App\Models;

use CodeIgniter\Model;

class NoticePrrModel extends Model
{
    protected $table = 'nt_notices_prr';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'link', 'name', 'program', 'date', 'date_start', 'date_end', 'tags', 'texts', 'docs', 'external_links', 'cim',
        'segmentation_type', 'status',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'link' => 'required',
        'name' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Get an icon from one of the sectors that might be associated with the notice
     * @return object
     */
    public function withIconAndBookmark(): object
    {
        $this->select('nt_notices_prr.*, (
            SELECT icon
                FROM nt_sectors
                    JOIN nt_notices_prr_sectors ON nt_sectors.id = nt_notices_prr_sectors.sector_id
                    WHERE nt_notices_prr_sectors.notice_id = nt_notices_prr.id
                    ORDER BY RAND()
                    LIMIT 1
                ) AS icon, (
            SELECT COUNT(user_bookmarks.prr_id)
            FROM user_bookmarks
            WHERE user_id = ' . auth()->user()->id . '
            AND prr_id = nt_notices_prr.id
            AND type = "prr"
            ) as bookmarks
        ');

        return $this;
    }

    /**
     * Show only bookmarked notices
     * @param bool $isBookmarked
     */
    public function bookmarked(bool $isBookmarked): NoticePrrModel
    {
        if (!$isBookmarked) {
            return $this;
        }
        $this->join('user_bookmarks', 'nt_notices_prr.id = user_bookmarks.prr_id');
        $this->where('user_id', auth()->user()->id);

        return $this;
    }

    /**
     * Set filter that need extra logic
     * @param  array  $filters The array of filters
     * @return this
     */
    public function filter(array $filters): object
    {
        $filters = array_filter($filters);
        if (empty($filters)) {
            return $this;
        }
        if (isset($filters['search'])) {
            $this->groupStart()
                ->orLike('name', $filters['search'])
                ->orLike('program', $filters['search'])
                ->orLike('texts', $filters['search']);
            $this->groupEnd();
        }
        if (isset($filters['dateEnd'])) {
            $this->where('date_end <=', $filters['dateEnd']);
        }
        if (isset($filters['sectors'])) {
            $this->join('nt_notices_prr_sectors', 'nt_notices_prr.id = nt_notices_prr_sectors.notice_id');
            foreach ($filters['sectors'] as $sectorId) {
                $this->where('nt_notices_prr_sectors.sector_id', $sectorId);
            }
        }
        if (isset($filters['thematics'])) {
            $this->join('nt_notices_prr_thematics', 'nt_notices_prr.id = nt_notices_prr_thematics.notice_id');
            foreach ($filters['thematics'] as $thematicId) {
                $this->where('nt_notices_prr_thematics.thematic_id', $thematicId);
            }
        }
        if (isset($filters['types'])) {
            $this->join('nt_notices_prr_types', 'nt_notices_prr.id = nt_notices_prr_types.notice_id');
            foreach ($filters['types'] as $thematicId) {
                $this->where('nt_notices_prr_types.type_id', $thematicId);
            }
        }
        if (isset($filters['cims'])) {
            $this->join('nt_notices_prr_cims', 'nt_notices_prr.id = nt_notices_prr_cims.notice_id');
            foreach ($filters['cims'] as $thematicId) {
                $this->where('nt_notices_prr_cims.cim_id', $thematicId);
            }
        }
        unset(
            $filters['clearUp'], $filters['search'], $filters['dateEnd'], $filters['sectors'], $filters['thematics'],
            $filters['types'], $filters['cims']
        );
        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }

}
