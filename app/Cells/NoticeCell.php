<?php

namespace App\Cells;

class NoticeCell
{

    /**
     * Set of available icons
     * @var array
     */
    protected array $icons = [
        'pdf' => '<i class="fa-light fa-file-pdf"></i>',
        'doc' => '<i class="fa-light fa-file-word"></i>',
        'docx' => '<i class="fa-light fa-file-word"></i>',
        'xls' => '<i class="fa-light fa-file-spreadsheet"></i>',
        'xlsx' => '<i class="fa-light fa-file-spreadsheet"></i>',
        'file' => '<i class="fa-light fa-file"></i>',
    ];

    /**
     * Force download a local document or just show the name
     * @param  array    $params The document object
     * @return string
     */
    public function download(array $params): string
    {
        return '<div class="col-2">
            <a href="' . site_url('notices/documents/download/' . $params['document']->id) . '" class="btn btn-primary">
                ' . $this->setIcon($params['document']->documentoDesignacao) . $params['document']->tipoDocumentoDesignacao .
            '</a>
        </div>';
    }

    /**
     * Define the correct icon for a file
     * @access Public function in case we need this just for the icon later
     * @param string $document
     */
    public function setIcon(string $document): string
    {
        $file = explode('.', $document);
        $extension = end($file);

        if (isset($this->icons[$extension])) {
            return $this->icons[$extension];
        }

        return $this->icons['file'];
    }

    /**
     * Encode image to show in PDF
     * @param string $document
     */
    public function encodeImage(string $url): string
    {
        $logoPath = base_url($url);
        $type = pathinfo($logoPath, PATHINFO_EXTENSION);
        $data = file_get_contents($logoPath);

        return 'data:image/' . $type . ';base64,' . base64_encode($data);
    }
}
