<?php

namespace App\Cells;

class FiltersCell
{

    protected $params;
    protected $multipleSelects = ['districts', 'cities', 'cims', 'sectors', 'thematics', 'types'];

    /**
     * Show filters so we can remove them with just one click
     * @param  array  $params The paramsn
     * @return string The view with the bullets
     */
    public function show(array $params): string
    {
        $this->params = $params;
        $data['items'] = [];
        $currentUrl = $_SERVER['QUERY_STRING'];

        $setBaseUrl = (isset($params['type']) && $params['type'] === 'prr') ? 'notices/prr' : 'notices';

        $filters = service('request')->getGet();
        foreach ($filters as $field => $value) {
            if (in_array($field, $this->multipleSelects)) {
                continue;
            }
            $string = $this->setUrl($currentUrl, $field);
            if (in_array($field, ['status', 'clearUp', 'page']) || empty($value)) {
                continue;
            }
            $data['items'][$value] = site_url($setBaseUrl . '?' . $string);
        }
        if (isset($filters['districts']) && !empty($filters['districts'])) {
            $districts = model('\App\Models\DistrictModel')->whereIn('id', $filters['districts'])->findAll();
            foreach ($districts as $d => $district) {
                $string = $this->setUrl($currentUrl, 'districts', $d);
                $data['items'][$district->name] = site_url($setBaseUrl . '?' . $string);
            }
        }
        if (isset($filters['cities']) && !empty($filters['cities'])) {
            $cities = model('\App\Models\CityModel')->whereIn('id', $filters['cities'])->findAll();
            foreach ($cities as $c => $city) {
                $string = $this->setUrl($currentUrl, 'cities', $c);
                $data['items'][$city->name] = site_url($setBaseUrl . '?' . $string);
            }
        }
        if (isset($filters['cims']) && !empty($filters['cims'])) {
            $cims = model('\App\Models\CimModel')->whereIn('id', $filters['cims'])->findAll();
            foreach ($cims as $cm => $cim) {
                $string = $this->setUrl($currentUrl, 'cims', $cm);
                $data['items'][$cim->name] = site_url($setBaseUrl . '?' . $string);
            }
        }
        if (isset($filters['sectors']) && !empty($filters['sectors'])) {
            $sectors = model('\App\Models\SectorModel')->whereIn('id', $filters['sectors'])->findAll();
            foreach ($sectors as $s => $sector) {
                $string = $this->setUrl($currentUrl, 'sectors', $s);
                $data['items'][$sector->name] = site_url($setBaseUrl . '?' . $string);
            }
        }
        if (isset($filters['thematics']) && !empty($filters['thematics'])) {
            $thematics = model('\App\Models\ThematicModel')->whereIn('id', $filters['thematics'])->findAll();
            foreach ($thematics as $t => $thematic) {
                $string = $this->setUrl($currentUrl, 'thematics', $t);
                $data['items'][$thematic->name] = site_url($setBaseUrl . '?' . $string);
            }
        }
        if (isset($filters['types']) && !empty($filters['types'])) {
            $types = model('\App\Models\TypeModel')->whereIn('id', $filters['types'])->findAll();
            foreach ($types as $ty => $type) {
                $string = $this->setUrl($currentUrl, 'types', $ty);
                $data['items'][$type->name] = site_url($setBaseUrl . '?' . $string);
            }
        }

        return view('app/cells/filters', $data);
    }

    /**
     * Set url without a selected param
     * @param string   $currentUrl Current url
     * @param string   $field      Current filter
     * @param int|null $key        (used only on arrays)
     */
    protected function setUrl(string $currentUrl, string $field, int $key = null): string
    {
        $parsed = parse_url($currentUrl);
        $query = $parsed['path'];
        parse_str($query, $this->params);
        if (!in_array($field, $this->multipleSelects)) {
            unset($this->params[$field]);
        } else {
            unset($this->params[$field][$key]);
        }
        return http_build_query($this->params);
    }

}
