<?php

namespace App\Database\Seeds;

use CodeIgniter\Database\Seeder;

class CityHalls extends Seeder
{
    public function run()
    {
        $this->db->query("INSERT INTO geo_city_halls (vat, name) VALUES
            ('500051054','MUNICÍPIO DE ALMADA'),
            ('500051062','MUNICÍPIO DE SINTRA'),
            ('500051070','MUNICIPIO DE LISBOA'),
            ('500745773','MUNICIPIO DE ALCANENA'),
            ('500745943','MUNICIP<PERSON> DE OEIRAS'),
            ('500832935','MUNICIPIO DE CUBA'),
            ('500843139','MUNICÍPIO DE CAMINHA'),
            ('501067191','MUNICIPIO DE TAVIRA'),
            ('501073655','MUNICIPIO DE MARCO DE CANAVESES'),
            ('501073663','MUNICÍPIO DE PENAFIEL'),
            ('501081216','MUN<PERSON>IP<PERSON> DE BARRANCOS'),
            ('501090436','<PERSON>UNICIPIO DE AGUEDA'),
            ('501091823','<PERSON><PERSON><PERSON>IP<PERSON> DE FELGUEIRAS'),
            ('501102752','MUNICIPIO DE AMARANTE'),
            ('501112049','MUNICIPIO DE SERPA'),
            ('501120149','MUNICÍPIO DO ENTRONCAMENTO'),
            ('501121030','MUNICIPIO DE IDANHA-A-NOVA'),
            ('501121528','MUNICIPIO DA LOUSÃ'),
            ('501121536','MUNICIPIO DE TORRE DE MONCORVO'),
            ('501122486','MUNICIPIO DE ALJUSTREL'),
            ('501128840','MUNICIPIO DE OLIVEIRA DO BAIRRO'),
            ('501129103','MUNICIPIO DE MORA'),
            ('501131140','MUNICIPIO DA GUARDA'),
            ('501132872','MUNICÍPIO DE ALTER DO CHÃO'),
            ('501133097','MUNICIPIO DE ALPIARCA'),
            ('501135960','MUNICIPIO DE CASTRO VERDE'),
            ('501138960','MUNICIPIO DE VALONGO'),
            ('501143530','MUNICIPIO DE CASTELO BRANCO'),
            ('501143718','MUNICIPIO DE PORTALEGRE'),
            ('501143726','MUNICIPIO DE TRANCOSO'),
            ('501143734','MUNICIPIO DE VIDIGUEIRA'),
            ('501144218','MUNICIPIO DE SESIMBRA'),
            ('501155996','MUNICIPIO DE ARRONCHES'),
            ('501156003','MUNICÍPIO DE VINHAIS'),
            ('501157280','MUNICIPIO DE SANTA MARIA DA FEIRA'),
            ('501158740','MUNICIPIO DE ESPINHO'),
            ('501162941','MUNICÍPIO DE FRONTEIRA'),
            ('501170162','MUNICÍPIO DE MARVÃO'),
            ('501175229','MUNICÍPIO DE CAMPO MAIOR'),
            ('501177256','MUNICÍPIO DE VENDAS NOVAS'),
            ('501181857','MUNICIPIO DE SARDOAL'),
            ('501190082','MUNICIPIO DE ESTARREJA'),
            ('501205551','MUNICÍPIO DE CHAVES'),
            ('501206639','MUNICIPIO DE MOURAO'),
            ('501216839','MUNICIPIO DE FERREIRA DO ZEZERE'),
            ('501222634','MUNICIPIO DE CALDAS DA RAINHA'),
            ('501227490','MUNICIPIO DE FERREIRA DO ALENTEJO'),
            ('501258027','MUNICÍPIO DE ARRAIOLOS'),
            ('501262997','MUNICIPIO DE MANGUALDE'),
            ('501272968','MUNICIPIO DE ELVAS'),
            ('501272976','MUNICIPIO DE MONTEMOR-O-VELHO'),
            ('501273433','MUNICIPIO DE ALMEIRIM'),
            ('501275380','MUNICIPIO DE CONDEIXA-A-NOVA'),
            ('501280740','MUNICIPIO DE OUREM'),
            ('501288120','MUNICIPIO DE ALVITO'),
            ('501290206','MUNICÍPIO DA BATALHA'),
            ('501294104','MUNICIPIO DE SETUBAL'),
            ('501294163','MUNICIPIO DE ANADIA'),
            ('501294996','MUNICIPIO DE LOURES'),
            ('501305564','MUNICIPIO DA CHAMUSCA'),
            ('501305580','MUNICIPIO DA FIGUEIRA DA FOZ'),
            ('501305734','MUNICIPIO DE ALENQUER'),
            ('501305912','MUNICIPIO DE MATOSINHOS'),
            ('501306099','CÂMARA MUNICIPAL DO PORTO'),
            ('501306234','MUNICIPIO DE OLIVEIRA DE FRADES'),
            ('501306269','MUNICÍPIO DE OVAR'),
            ('501306870','MUNICIPIO DE SANTO TIRSO'),
            ('501834117','MUNICIPIO DO REDONDO'),
            ('501937471','MUNICIPIO DE MONÇÃO'),
            ('502098139','MUNICIPIO DE LOULÉ'),
            ('502130040','MUNICIPIO DE SANTIAGO DO CACEM'),
            ('502150319','MUNICÍPIO DE ALCÁCER DO SAL'),
            ('502173297','MUNICIPIO DE PAÇOS DE FERREIRA'),
            ('502173653','MUNICIPIO DE TORRES VEDRAS'),
            ('502174153','MUNICIPIO DE MOURA'),
            ('502177080','MUNICÍPIO DE MAFRA'),
            ('502177101','MUNICÍPIO DA LOURINHÃ'),
            ('502563010','MUNICIPIO DE SINES'),
            ('502661038','MUNICIPIO DE ABRANTES'),
            ('502678917','MUNICIPIO DE CASTELO DE PAIVA'),
            ('502704977','MUNICIPIO DE SEVER DO VOUGA'),
            ('502789824','MUNICIPIO DE AVIS'),
            ('502834846','MUNICIPIO DE MONTIJO'),
            ('503219924','MUNICIPIO DE SÃO BRÁS DE ALPORTEL'),
            ('503279765','MUNICÍPIO DE MÉRTOLA'),
            ('503539473','MUNICÍPIO DE ALBUFEIRA'),
            ('503956546','MUNICIPIO DE BORBA'),
            ('504293125','MUNICÍPIO DE ODIVELAS'),
            ('504296434','MUNICÍPIO DA TROFA'),
            ('504828576','MUNICÍPIO DE ÉVORA'),
            ('504884620','MUNICIPIO DE BEJA'),
            ('505161974','MUNICÍPIO DE MÊDA'),
            ('505170876','MUNICÍPIO DE LAGOS'),
            ('505181266','MUNICÍPIO DE LEIRIA'),
            ('505187531','MUNICÍPIO DE CASCAIS'),
            ('505211696','MUNICÍPIO DE ARCOS DE VALDEVEZ'),
            ('505279460','MUNICÍPIO DE LOUSADA'),
            ('505307685','MUNICIPIO DE ARRUDA DOS VINHOS'),
            ('505309939','MUNICÍPIO DE PORTIMÃO'),
            ('505311313','MUNICÍPIO DE ODEMIRA'),
            ('505330334','MUNICIPIO DE CABECEIRAS DE BASTO'),
            ('505330768','MUNICIPIO DA COVILHÃ'),
            ('505335018','MUNICÍPIO DE VILA NOVA DE GAIA'),
            ('505371600','MUNICÍPIO DE VILA NOVA DE POIARES'),
            ('505377802','MUNICÍPIO DE PROENÇA À NOVA'),
            ('505387131','MUNICIPIO DA MAIA'),
            ('505410850','MUNICÍPIO DE SOBRAL DE MONTE AGRAÇO'),
            ('505456010','MUNICÍPIO DA AMADORA'),
            ('505584760','MUNICÍPIO DE BARCELOS'),
            ('505586401','MUNICÍPIO DE PORTO DE MÓS'),
            ('505592940','MUNICÍPIO DE MELGAÇO'),
            ('505592959','MUNICÍPIO DE FORNOS DE ALGODRES'),
            ('505656000','MUNICÍPIO DE RIO MAIOR'),
            ('505676770','MUNICÍPIO DE PONTE DA BARCA'),
            ('505763621','MUNICIPIO DO CADAVAL'),
            ('505776758','MUNICIPIO DA MARINHA GRANDE'),
            ('505804786','MUNICÍPIO DE VILA DO CONDE'),
            ('505931192','MUNICÍPIO DE AVEIRO'),
            ('505932512','MUNICÍPIO DE ALJEZUR'),
            ('505941350','MUNICÍPIO DE SANTARÉM'),
            ('505948605','MUNICIPIO DE GUIMARÃES'),
            ('505985217','MUNICIPIO DE VIZELA'),
            ('505987449','MUNÍCIPIO DE FIGUEIRA DE CASTELO RODRIGO'),
            ('506037258','MUNICÍPIO DE VIANA DO CASTELO'),
            ('506087000','MUNICIPIO DE CANTANHEDE'),
            ('506149811','MUNICÍPIO DE MONTALEGRE'),
            ('506151174','MUNICÍPIO DE VIANA DO ALENTEJO'),
            ('506173968','MUNICÍPIO DO SEIXAL'),
            ('506187543','MUNICÍPIO DE PALMELA'),
            ('506192164','MUNICIPIO DE PENAMACOR'),
            ('506196445','MUNICIPIO DE PORTEL'),
            ('506215547','MUNICIPIO DE BRAGANÇA'),
            ('506215695','MUNICÍPIO DO FUNDÃO'),
            ('506302970','MUNICÍPIO DE OLIVEIRA DE AZEMÉIS'),
            ('506321894','MUNICÍPIO DE OLHÃO'),
            ('506334562','MUNICÍPIO DE POMBAL'),
            ('506349381','MUNICÍPIO DE RESENDE'),
            ('506359670','MUNICÍPIO DE VILA REAL'),
            ('506415082','MUNICÍPIO DE COIMBRA'),
            ('506510476','MUNICÍPIO DE GOUVEIA'),
            ('506538575','MUNICÍPIO DE SÃO JOÃO DA MADEIRA'),
            ('506546381','MUNICÍPIO DE FIGUEIRÓ DOS VINHOS'),
            ('506556590','MUNICÍPIO DE ESTREMOZ'),
            ('506563774','MUNICÍPIO DA GOLEGÃ'),
            ('506572218','MUNICIPIO DE LAMEGO'),
            ('506579425','MUNICÍPIO DE FARO'),
            ('506601455','MUNICÍPIO DE TABUAÇO'),
            ('506605930','MUNICÍPIO DE ANSIÃO'),
            ('506605949','MUNICÍPIO DE ALVAIÁZERE'),
            ('506608972','MUNICÍPIO DE TORRES NOVAS'),
            ('506609553','MUNICÍPIO DE MONTEMOR-O-NOVO'),
            ('506612287','MUNICÍPIO DE NISA'),
            ('506613399','MUNICÍPIO DE GÓIS'),
            ('506613461','MUNICÍPIO DE VILA VIÇOSA'),
            ('506614913','MUNICÍPIO DE VILA FRANCA DE XIRA'),
            ('506617599','MUNICÍPIO DE ESPOSENDE'),
            ('506624200','MUNICÍPIO DE MIRANDA DO CORVO'),
            ('506625419','MUNICÍPIO DE ALMEIDA'),
            ('506627888','MUNICÍPIO DE VIMIOSO'),
            ('506632920','MUNICÍPIO DA PÓVOA DE LANHOSO'),
            ('506632938','MUNICÍPIO DE PAREDES DE COURA'),
            ('506632946','MUNICÍPIO DE MANTEIGAS'),
            ('506637441','MUNICÍPIO DE SANTA COMBA DÃO'),
            ('506641376','MUNICÍPIO DE VILA VERDE'),
            ('506642798','MUNICÍPIO DE VILA VELHA DE RÓDÃO'),
            ('506647498','MUNICÍPIO DE ALFÂNDEGA DA FÉ'),
            ('506651541','MUNICÍPIO DE PENEDONO'),
            ('506656128','MUNICÍPIO DE PAREDES'),
            ('506657957','MUNICÍPIO DE PENACOVA'),
            ('506659682','MUNICÍPIO DE VIEIRA DO MINHO'),
            ('506659968','MUNICÍPIO DO CRATO'),
            ('506663264','MUNICÍPIO DE VILA NOVA DE FAMALICÃO'),
            ('506664686','MUNICÍPIO DE MOIMENTA DA BEIRA'),
            ('506666018','MUNICÍPIO DE CARRAZEDA DE ANSIÃES'),
            ('506673626','MUNICÍPIO DO BARREIRO'),
            ('506676056','MUNICÍPIO DE BENAVENTE'),
            ('506676170','MUNICÍPIO DE SEIA'),
            ('506684920','MUNICÍPIO DE CARREGAL DO SAL'),
            ('506693651','MUNICÍPIO DE CINFÃES'),
            ('506695956','MUNICÍPIO DE BELMONTE'),
            ('506696464','MUNICÍPIO DE VILA FLOR'),
            ('506697320','MUNICÍPIO DE VISEU'),
            ('506697339','MUNICÍPIO DE MACEDO DE CAVALEIROS'),
            ('506716210','MUNICÍPIO DE CASTRO DAIRE'),
            ('506722422','MUNICÍPIO DE CORUCHE'),
            ('506724530','MUNICÍPIO DE MIRA'),
            ('506728897','MUNICÍPIO DE VALENÇA'),
            ('506730573','MUNICÍPIO DE VILA DO BISPO'),
            ('506731324','MUNICÍPIO DE CASTANHEIRA DE PÊRA'),
            ('506735524','MUNICÍPIO DE VALE DE CAMBRA'),
            ('*********','MUNICÍPIO DE TOMAR'),
            ('*********','MUNICÍPIO DA PÓVOA DE VARZIM'),
            ('*********','MUNICÍPIO DE TAROUCA'),
            ('*********','MUNICIPIO DE SALVATERRA DE MAGOS'),
            ('*********','MUNICÍPIO DE VOUZELA'),
            ('*********','MUNICÍPIO DE ALCOUTIM'),
            ('*********','MUNICÍPIO DE ALANDROAL'),
            ('*********','MUNICÍPIO DE PENELA'),
            ('*********','MUNICÍPIO DO CARTAXO'),
            ('*********','MUNICÍPIO DE ALBERGARIA-A-VELHA'),
            ('*********','MUNICÍPIO DE SÃO PEDRO DO SUL'),
            ('*********','MUNICÍPIO DE PINHEL'),
            ('*********','MUNICÍPIO DE ALCOCHETE'),
            ('*********','MUNICÍPIO DA MOITA'),
            ('*********','MUNICÍPIO DA MURTOSA'),
            ('*********','MUNICÍPIO DE MEALHADA'),
            ('*********','MUNICÍPIO DE PENALVA DO CASTELO'),
            ('*********','MUNICÍPIO DE CASTELO DE VIDE'),
            ('*********','MUNICÍPIO DE AMARES'),
            ('*********','MUNICÍPIO DE BOMBARRAL'),
            ('*********','MUNICÍPIO DE CASTRO MARIM'),
            ('*********','MUNICÍPIO DE ÓBIDOS'),
            ('*********','MUNICÍPIO DE LAGOA'),
            ('*********','MUNICÍPIO DE PONTE DE SOR'),
            ('*********','MUNICÍPIO DE MIRANDA DO DOURO'),
            ('*********','MUNICÍPIO DE TÁBUA'),
            ('*********','MUNICÍPIO DE AROUCA'),
            ('506809307','MUNICÍPIO DE AGUIAR DA BEIRA'),
            ('506809323','MUNICÍPIO DE VILA NOVA DE PAIVA'),
            ('506809560','MUNICÍPIO DE SOUSEL'),
            ('506810267','MUNICÍPIO DE VILA POUCA DE AGUIAR'),
            ('506811662','MUNICÍPIO DE SABUGAL'),
            ('506811883','MUNICÍPIO DE PAMPILHOSA DA SERRA'),
            ('506811913','MUNICÍPIO DE PONTE DE LIMA'),
            ('506812820','MUNICÍPIO DE PENICHE'),
            ('506814343','MUNICÍPIO DE MAÇÃO'),
            ('506816184','MUNICÍPIO DE ALMODÔVAR'),
            ('506818098','MUNICÍPIO DE RIBEIRA DE PENA'),
            ('506818829','MUNICÍPIO DE OLIVEIRA DO HOSPITAL'),
            ('506818837','MUNICÍPIO DE SILVES'),
            ('506821480','MUNICÍPIO DE AZAMBUJA'),
            ('506822680','MUNICÍPIO DE TONDELA'),
            ('506823318','MUNICÍPIO DE GRÂNDOLA'),
            ('506824152','MUNICIPIO DE OLEIROS'),
            ('506824942','MUNICÍPIO DE SABROSA'),
            ('506826546','MUNICÍPIO DE CONSTÂNCIA'),
            ('506826961','MUNICÍPIO DE MONCHIQUE'),
            ('506829138','MUNICÍPIO DE SANTA MARTA DE PENAGUIÃO'),
            ('506829197','MUNICÍPIO DE VILA NOVA DE FOZ CÔA'),
            ('506829260','MUNICÍPIO DE PESO DA RÉGUA'),
            ('506833224','MUNICÍPIO DE VILA REAL DE SANTO ANTÓNIO'),
            ('506833232','MUNICÍPIO DE ARGANIL'),
            ('506834166','MUNICÍPIO DE NELAS'),
            ('506840328','MUNICÍPIO DE MESÃO FRIO'),
            ('506841561','MUNICÍPIO DE FAFE'),
            ('506843190','MUNICÍPIO DE ARMAMAR'),
            ('506848957','MUNICÍPIO DE GONDOMAR'),
            ('506849635','MUNICÍPIO DE CELORICO DA BEIRA'),
            ('506851168','MUNICÍPIO DE MOGADOURO'),
            ('506852032','MUNICÍPIO DE SERNANCELHE'),
            ('506854299','MUNICÍPIO DE BAIÃO'),
            ('506855368','MUNICÍPIO DE MORTÁGUA'),
            ('506859487','MUNICÍPIO DE ALIJÓ'),
            ('506862763','MUNICÍPIO DE MURÇA'),
            ('506865517','MUNICÍPIO DE GAVIÃO'),
            ('506873412','MUNICÍPIO DE MONFORTE'),
            ('506874249','MUNICÍPIO DE ALCOBAÇA'),
            ('506874320','MUNICÍPIO DE VALPAÇOS'),
            ('506876330','MUNICÍPIO DE OURIQUE'),
            ('506881784','MUNICÍPIO DE MIRANDELA'),
            ('506882713','MUNICÍPIO DE SÁTÃO'),
            ('506884929','MUNICÍPIO DE CELORICO DE BASTO'),
            ('506884937','MUNICÍPIO DE FREIXO DE ESPADA À CINTA'),
            ('506886964','MUNICÍPIO DE BOTICAS'),
            ('506892646','MUNICÍPIO DE SÃO JOÃO DA PESQUEIRA'),
            ('506896625','MUNICÍPIO DE VILA NOVA DE CERVEIRA'),
            ('506899250','MUNICÍPIO DE VILA NOVA DA BARQUINHA'),
            ('506901173','MUNICÍPIO DE BRAGA'),
            ('506907619','MUNICÍPIO DE TERRAS DE BOURO'),
            ('506912833','MUNICÍPIO DE VAGOS'),
            ('506920887','MUNICÍPIO DE ÍLHAVO'),
            ('506932273','MUNICÍPIO DE VILA DE REI'),
            ('506963837','MUNICÍPIO DA SERTÃ'),
            ('506967107','MUNICÍPIO DE MONDIM DE BASTO'),
            ('506984770','Junta de Freguesia de Santa Luzia – Tavira'),
            ('507011937','MUNICÍPIO DE PEDRÓGÃO GRANDE'),
            ('507012100','MUNICÍPIO DA NAZARÉ'),
            ('507040589','MUNICÍPIO DE REGUENGOS DE MONSARAZ'),
            ('507103742','MUNICÍPIO DE SOURE'),
            ('510834671','FREGUESIA DE AROUCA E BURGO'),
            ('510841260','FREGUESIA DE VILELA SÃO COSME E SÃO DAMIÃO E SÁ'),
            ('511233639','MUNICÍPIO DA CALHETA (Madeira)'),
            ('512070946','MUNICIPIO DA MADALENA')");
    }
}
