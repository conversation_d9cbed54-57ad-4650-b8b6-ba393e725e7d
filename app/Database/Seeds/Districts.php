<?php

namespace App\Database\Seeds;

use App\Models\DistrictModel;
use CodeIgniter\CLI\CLI;
use CodeIgniter\Database\Seeder;

class Districts extends Seeder
{
    public function run()
    {
        $data = [
            [
                'id' => 1,
                'name' => 'Aveiro',
            ],
            [
                'id' => 2,
                'name' => 'Beja',
            ],
            [
                'id' => 3,
                'name' => 'Braga',
            ],
            [
                'id' => 4,
                'name' => 'Bragança',
            ],
            [
                'id' => 5,
                'name' => 'Castelo Branco',
            ],
            [
                'id' => 6,
                'name' => 'Coimbra',
            ],
            [
                'id' => 7,
                'name' => 'Évora',
            ],
            [
                'id' => 8,
                'name' => 'Faro',
            ],
            [
                'id' => 9,
                'name' => 'Guarda',
            ],
            [
                'id' => 10,
                'name' => 'Leiria',
            ],
            [
                'id' => 11,
                'name' => 'Lisboa',
            ],
            [
                'id' => 12,
                'name' => 'Portalegre',
            ],
            [
                'id' => 13,
                'name' => 'Porto',
            ],
            [
                'id' => 14,
                'name' => '<PERSON>ré<PERSON>',
            ],
            [
                'id' => 15,
                'name' => '<PERSON><PERSON><PERSON>',
            ],
            [
                'id' => 16,
                'name' => 'Viana do Castelo',
            ],
            [
                'id' => 17,
                'name' => 'Vila Real',
            ],
            [
                'id' => 18,
                'name' => 'Viseu',
            ],
            [
                'id' => 31,
                'name' => 'Ilha da Madeira',
            ],
            [
                'id' => 32,
                'name' => 'Ilha de Porto Santo',
            ],
            [
                'id' => 41,
                'name' => 'Ilha de Santa Maria',
            ],
            [
                'id' => 42,
                'name' => 'Ilha de São Miguel',
            ],
            [
                'id' => 43,
                'name' => 'Ilha Terceira',
            ],
            [
                'id' => 44,
                'name' => 'Ilha da Graciosa',
            ],
            [
                'id' => 45,
                'name' => 'Ilha de São Jorge',
            ],
            [
                'id' => 46,
                'name' => 'Ilha do Pico',
            ],
            [
                'id' => 47,
                'name' => 'Ilha do Faial',
            ],
            [
                'id' => 48,
                'name' => 'Ilha das Flores',
            ],
            [
                'id' => 49,
                'name' => 'Ilha do Corvo',
            ],
        ];
        $districtModel = new DistrictModel();
        foreach ($data as $district) {
            if ($districtModel->insert($district)) {
                CLI::write($district['name'] . ' was inserted!');
            }
        }
    }
}
