<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FixTableNames extends Migration
{
    public function up()
    {
        $this->forge->renameTable('basegov_alerts', 'base_gov_alerts');
        $this->forge->renameTable('basegov_alerts_cpvs', 'base_gov_alerts_cpvs');
        $this->forge->renameTable('basegov_announcements', 'base_gov_announcements');
        $this->forge->renameTable('basegov_announcements_contracting_entities', 'base_gov_announcements_contracting_entities');
    }

    public function down()
    {
        $this->forge->renameTable('base_gov_alerts', 'basegov_alerts');
        $this->forge->renameTable('base_gov_alerts_cpvs', 'basegov_alerts_cpvs');
        $this->forge->renameTable('base_gov_announcements', 'basegov_announcements');
        $this->forge->renameTable('base_gov_announcements_contracting_entities', 'basegov_announcements_contracting_entities');
    }
}
