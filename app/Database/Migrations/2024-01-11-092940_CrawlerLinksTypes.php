<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class CrawlerLinksTypes extends Migration
{
    public function up()
    {
        $fields = [
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['national', 'regional', 'pre-notice', 'tenders', 'prr'],
                'null' => false,
                'default' => 'national',
            ],
        ];
        $this->forge->modifyColumn('crawler_links', $fields);
    }

    public function down()
    {
        $fields = [
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['national', 'regional', 'pre-notice', 'tenders', 'prr'],
                'null' => false,
                'default' => 'national',
            ],
        ];
        $this->forge->modifyColumn('crawler_links', $fields);
    }
}
