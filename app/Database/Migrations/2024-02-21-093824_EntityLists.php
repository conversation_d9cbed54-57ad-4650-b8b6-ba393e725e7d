<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EntityLists extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'name' => [
                'type' => 'text',
                'null' => false,
            ],
            'direction' => [
                'type' => 'ENUM',
                'constraint' => ['normal', 'inverted'],
                'null' => false,
                'default' => 'inverted',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'null' => false,
                'default' => 'active',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('base_gov_entity_lists');
    }

    public function down()
    {
        $this->forge->dropTable('base_gov_entity_lists');
    }
}
