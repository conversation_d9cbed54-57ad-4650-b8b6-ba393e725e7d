<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class <PERSON>rawler extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'link' => [
                'type' => 'text',
                'null' => false,
            ],
            'type' => [
                'type' => 'ENUM',
                'constraint' => ['national', 'regional', 'europe', 'tenders'],
                'null' => false,
                'default' => 'national',
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['todo', 'done'],
                'null' => false,
                'default' => 'todo',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('crawler_links');
    }

    public function down()
    {
        $this->forge->dropTable('crawler_links');
    }
}
