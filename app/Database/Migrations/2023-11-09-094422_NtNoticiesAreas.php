<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticiesAreas extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'europe_notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'area_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'europe_notice_id', 'nt_notices_europe', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_areas_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'area_id', 'nt_areas', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_areas_area_id__fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notices_europe_areas');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_europe_areas');
    }
}
