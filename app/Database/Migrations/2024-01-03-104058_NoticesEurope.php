<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NoticesEurope extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'thematic_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'program_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'link' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'start_date' => [
                'type' => 'date',
                'null' => false,
            ],
            'end_date' => [
                'type' => 'date',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'thematic_id', 'nt_thematics', 'id', 'CASCADE', 'CASCADE', 'nt_ntcs_europe_thematics_thematic_id_fk'
        );
        $this->forge->addForeignKey(
            'program_id', 'nt_notices_europe_programs', 'id', 'CASCADE', 'CASCADE', 'nt_ntcs_europe_programs_program_id_fk'
        );
        $this->forge->createTable('nt_notices_europe');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_europe');
    }
}
