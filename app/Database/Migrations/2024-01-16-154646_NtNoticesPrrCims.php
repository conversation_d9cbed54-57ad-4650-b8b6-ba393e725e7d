<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticesPrrCims extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'cim_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'cim_id', 'geo_cims', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_prr_cims_cim_id_fk'
        );
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_prr', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_prr_cims_notice_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notices_prr_cims');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_prr_cims');
    }
}
