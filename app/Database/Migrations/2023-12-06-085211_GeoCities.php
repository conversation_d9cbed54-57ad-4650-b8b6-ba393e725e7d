<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class GeoCities extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'code' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'district_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'name' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => false,
            ],
            'nut_1' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => false,
            ],
            'nut_2' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => false,
            ],
            'nut_3' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addForeignKey(
            'district_id', 'geo_districts', 'id', 'CASCADE', 'CASCADE',
            'geo_cities_districts_district_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('geo_cities');
    }

    public function down()
    {
        $this->forge->dropTable('geo_cities');
    }
}
