<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BaseGovStatus extends Migration
{
    public function up()
    {
        $fields = [
            'status' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'default' => 'new',
                'after' => 'observations',
            ],
        ];

        $this->forge->addColumn('base_gov', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov', ['status']);
    }
}
