<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use DreApp\Libraries\SettingsCrud;

class DreEmails extends Migration
{
    public function up()
    {
        service('settings')->set('Dre.emails', []);
        SettingsCrud::add('Dre.emails', 'marga<PERSON><PERSON>ve<PERSON>@chconsulting.pt');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
        SettingsCrud::add('Dre.emails', '<EMAIL>');
    }

    public function down()
    {
        service('settings')->forget('Dre.emails');
    }
}
