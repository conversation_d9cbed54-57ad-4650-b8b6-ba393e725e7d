<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlertsNewFields extends Migration
{
    public function up()
    {
        $this->forge->dropColumn('base_gov_alerts', 'status');
        $fields = [
            'last_check' => [
                'type' => 'TIMESTAMP',
                'null' => true,
                'after' => 'emails',
            ],
        ];
        $this->forge->addColumn('base_gov_alerts', $fields);

    }

    public function down()
    {
        $fields = [
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'null' => false,
                'default' => 'inactive',
                'after' => 'emails',
            ],
        ];
        $this->forge->addColumn('base_gov_alerts', $fields);
    }
}
