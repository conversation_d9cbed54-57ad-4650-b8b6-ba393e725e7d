<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticePrrLinks extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'name' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'link' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_prr', 'id', 'CASCADE', 'CASCADE', 'nt_notice_prr_links_notice_id_fk'
        );
        $this->forge->createTable('nt_notices_prr_links');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_prr_links');
    }
}
