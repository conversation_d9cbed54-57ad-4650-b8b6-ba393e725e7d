<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class StNoticeSectors extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'extra_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'sector_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'sector_id', 'nt_sectors', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_sectors_sector_id_fk'
        );
        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_sectors_extra_id_fk'
        );

        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notice_sectors');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notice_sectors');
    }
}
