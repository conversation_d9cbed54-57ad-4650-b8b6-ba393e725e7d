<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class PrrBookmark extends Migration
{
    public function up()
    {
        $fields = [
            'prr_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true,
                'after' => 'extra_id',
            ],
        ];

        $this->forge->addColumn('user_bookmarks', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('user_bookmarks', 'prr_id');
    }
}
