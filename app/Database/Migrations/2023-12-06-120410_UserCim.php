<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UserCim extends Migration
{
    public function up()
    {
        $fields = [
            'cim_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'max_employees',
            ],
        ];
        $this->forge->addForeignKey('cim_id', 'geo_cims', 'id', 'CASCADE', 'CASCADE', 'user_details_cim_id_fk');
        $this->forge->addColumn('user_details', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('user_details', ['cim_id']);
    }
}
