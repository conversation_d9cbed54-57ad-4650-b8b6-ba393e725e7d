<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticeEuropeTypes extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'type_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'type_id', 'nt_types', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_types_type_id_fk'
        );
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_europe', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_types_notice_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notice_europe_types');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notice_europe_types');
    }
}
