<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EntitiesSyncSystem extends Migration
{
    public function up()
    {
        $fields = [
            'remote_total' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'name',
            ],
            'local_total' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'remote_total',
            ],
            'last_connection' => [
                'type' => 'TIMESTAMP',
                'after' => 'local_total',
                'null' => true,
            ],
            'direction' => [
                'type' => 'varchar',
                'constraint' => '100',
                'default' => 'normal',
                'null' => false,
                'after' => 'last_connection',
            ],
        ];

        $this->forge->addColumn('base_gov_entities', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov_entities', ['remote_total', 'local_total', 'last_connection', 'direction']);
    }
}
