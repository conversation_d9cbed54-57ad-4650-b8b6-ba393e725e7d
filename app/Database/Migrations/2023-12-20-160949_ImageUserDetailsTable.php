<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ImageUserDetailsTable extends Migration
{
    public function up()
    {
        $fields = [
            'image' => [
                'type' => 'varchar',
                'constraint' => '100',
                'null' => true,
                'after' => 'city_id',
            ],
        ];
        $this->forge->addColumn('user_details', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('user_details', ['image']);
    }
}
