<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovAlerts extends Migration
{
    public function up()
    {

        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'tags' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'emails' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'null' => false,
                'default' => 'inactive',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('basegov_alerts');
    }

    public function down()
    {
        $this->forge->dropTable('basegov_alerts');
    }
}
