<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemsGrouping extends Migration
{
    public function up()
    {
        $fields = [
            'grouping' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => true,
                'after' => 'candidate'
            ]
        ];
        $this->forge->addColumn('dre_items', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('dre_items', [
            'grouping'
        ]);
    }
}
