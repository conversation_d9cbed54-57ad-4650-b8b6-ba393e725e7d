<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class Rename extends Migration
{
    public function up()
    {
        $this->forge->renameTable('nt_notices_europe', 'nt_prenotices');
        $this->forge->renameTable('nt_notices_europe_areas', 'nt_prenotices_areas');
        $this->forge->renameTable('nt_europe_external_links', 'nt_prenotices_external_links');
    }

    public function down()
    {
        $this->forge->renameTable('nt_prenotices', 'nt_notices_europe');
        $this->forge->renameTable('nt_prenotices_areas', 'nt_notices_europe_areas');
        $this->forge->renameTable('nt_prenotices_external_links', 'nt_europe_external_links');
    }
}
