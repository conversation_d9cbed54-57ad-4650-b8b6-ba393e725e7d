<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemsRevokedStatus extends Migration
{
    public function up()
    {
        $fields = [
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['no_status', 'open', 'closed', 'revoked'],
                'default' => 'no_status',
                'null' => false
            ]
        ];
        $this->forge->modifyColumn('dre_items', $fields);
    }

    public function down()
    {
        $fields = [
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['no_status', 'open', 'closed'],
                'default' => 'no_status',
                'null' => false
            ]
        ];
        $this->forge->modifyColumn('dre_items', $fields);
    }
}
