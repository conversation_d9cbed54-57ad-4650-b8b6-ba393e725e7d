<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovAwardCriteria extends Migration
{
    public function up()
    {
        $fields = [
            'award_criteria' => [
                'type' => 'text',
                'null' => true,
                'after' => 'drPublicationDate',
            ],
            'doc' => [
                'type' => 'TINYINT',
                'default' => 0,
                'null' => false,
                'after' => 'drPublicationDate',
            ],
        ];
        $this->forge->addColumn('base_gov_announcements', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov_announcements', ['award_criteria', 'doc']);
    }
}
