<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class ArticlesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'title' => [
                'type' => 'text',
                'null' => false,
            ],
            'slug' => [
                'type' => 'text',
                'null' => false,
            ],
            'intro' => [
                'type' => 'text',
                'null' => false,
            ],
            'description' => [
                'type' => 'text',
                'null' => false,
            ],
            'image' => [
                'type' => 'varchar',
                'constraint' => '100',
                'null' => true,
            ],
            'tags' => [
                'type' => 'varchar',
                'null' => true,
                'constraint' => 255,
            ],
            'source' => [
                'type' => 'varchar',
                'null' => true,
                'constraint' => 255,
            ],
            'schedule' => [
                'type' => 'datetime',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('articles');
    }

    public function down()
    {
        $this->forge->dropTable('articles');
    }
}
