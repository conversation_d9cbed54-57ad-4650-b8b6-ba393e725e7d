<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovAnnouncements extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'type' => [
                'type' => 'text',
                'null' => true,
            ],
            'proposalDeadline' => [
                'type' => 'date',
            ],
            'contractingEntity' => [
                'type' => 'text',
                'null' => true,
            ],
            'basePrice' => [
                'type' => 'text',
                'null' => true,
            ],
            'contractDesignation' => [
                'type' => 'text',
                'null' => true,
            ],
            'drPublicationDate' => [
                'type' => 'date',
                'null' => true,
            ],
            'contractingProcedureType' => [
                'type' => 'text',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('basegov_announcements');

    }

    public function down()
    {
        $this->forge->dropTable('basegov_announcements');
    }
}
