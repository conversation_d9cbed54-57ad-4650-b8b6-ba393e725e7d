<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticeEuropeThematics extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'thematic_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'thematic_id', 'nt_thematics', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_sectors_thematic_id_fk'
        );
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_europe', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_thematics_notice_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notice_europe_thematics');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notice_europe_thematics');
    }
}
