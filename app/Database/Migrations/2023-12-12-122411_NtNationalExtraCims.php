<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNationalExtraCims extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'extra_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'cim_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_national_extra_cims_nt_notices_national_extra_fk'
        );
        $this->forge->addForeignKey(
            'cim_id', 'geo_cims', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_national_extra_cims_geo_cims_fk'
        );
        $this->forge->createTable('nt_notices_national_extra_cims');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_national_extra_cims');
    }
}
