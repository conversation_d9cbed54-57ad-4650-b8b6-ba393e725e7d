<?php

namespace App\Database\Migrations;

use Basegov\Models\BaseGovAlertModel;
use CodeIgniter\Database\Migration;

class AlertsGlobal extends Migration
{
    public function up()
    {
        $fields = [
            'main' => [
                'type' => 'INT',
                'constraint' => 2,
                'null' => false,
                'default' => '0',
                'after' => 'emails',
            ],
        ];
        $this->forge->addColumn('base_gov_alerts', $fields);

        $alertModel = new BaseGovAlertModel();
        $alertModel->skipValidation(true)->allowCallbacks(false)->save([
            'id' => 1, 'main' => 1,
        ]);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov_alerts', ['main']);
    }
}
