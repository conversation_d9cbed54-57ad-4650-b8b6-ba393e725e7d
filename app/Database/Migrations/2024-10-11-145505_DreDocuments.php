<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class DreDocuments extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'dre_item_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true
            ],
            'local_document' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP')
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP')
            ]
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'dre_item_id', 'dre_items', 'id', 'CASCADE', 'CASCADE',
            'dre_items_documents_dre_item_id_fk'
        );
        $this->forge->createTable('dre_items_documents');
    }

    public function down()
    {
        $this->forge->dropTable('dre_items_documents');
    }
}
