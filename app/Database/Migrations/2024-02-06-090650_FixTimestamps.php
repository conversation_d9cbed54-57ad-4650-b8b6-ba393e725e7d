<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FixTimestamps extends Migration
{
    /**
     * Not a normal migration, we just need to fix de default values globally.
     * No need for a down statemente and use of forge.
     * This could also be just a command, but it doesn't make sense to create a command that would only ran once
     * Other option could be to make this a seed, but this wouldn't be seeding anything so this seems like the best option
     */
    public function up()
    {
        $db = db_connect();
        $db->query('alter table articles modify created_at timestamp null');
        $db->query('alter table articles modify updated_at timestamp null');
        $db->query('alter table base_gov modify created_at timestamp null');
        $db->query('alter table base_gov modify updated_at timestamp null');
        $db->query('alter table base_gov_alerts modify created_at timestamp null');
        $db->query('alter table base_gov_alerts modify updated_at timestamp null');
        $db->query('alter table base_gov_announcements modify created_at timestamp null');
        $db->query('alter table base_gov_announcements modify updated_at timestamp null');
        $db->query('alter table base_gov_announcements_contracting_entities modify created_at timestamp null');
        $db->query('alter table base_gov_announcements_contracting_entities modify updated_at timestamp null');
        $db->query('alter table base_gov_contestants modify created_at timestamp null');
        $db->query('alter table base_gov_documents modify created_at timestamp null');
        $db->query('alter table base_gov_invitees modify created_at timestamp null');
        $db->query('alter table cpvs modify created_at timestamp null');
        $db->query('alter table cpvs modify updated_at timestamp null');
        $db->query('alter table crawler_links modify created_at timestamp null');
        $db->query('alter table crawler_links modify updated_at timestamp null');
        $db->query('alter table geo_cims modify created_at timestamp null');
        $db->query('alter table geo_cims modify updated_at timestamp null');
        $db->query('alter table geo_cities modify created_at timestamp null');
        $db->query('alter table geo_cities modify updated_at timestamp null');
        $db->query('alter table geo_city_halls modify created_at timestamp null');
        $db->query('alter table geo_city_halls modify updated_at timestamp null');
        $db->query('alter table geo_districts modify created_at timestamp null');
        $db->query('alter table geo_districts modify updated_at timestamp null');
        $db->query('alter table geo_parish_councel modify created_at timestamp null');
        $db->query('alter table geo_parish_councel modify updated_at timestamp null');
        $db->query('alter table nt_areas modify created_at timestamp null');
        $db->query('alter table nt_areas modify updated_at timestamp null');
        $db->query('alter table nt_notices_europe modify created_at timestamp null');
        $db->query('alter table nt_notices_europe modify updated_at timestamp null');
        $db->query('alter table nt_notices_europe_cims modify created_at timestamp null');
        $db->query('alter table nt_notices_europe_documents modify created_at timestamp null');
        $db->query('alter table nt_notices_europe_documents modify updated_at timestamp null');
        $db->query('alter table nt_notices_europe_programs modify created_at timestamp null');
        $db->query('alter table nt_notices_europe_programs modify updated_at timestamp null');
        $db->query('alter table nt_notices_national_documents modify created_at timestamp null');
        $db->query('alter table nt_notices_national_documents modify updated_at timestamp null');
        $db->query('alter table nt_notices_national_extra modify created_at timestamp null');
        $db->query('alter table nt_notices_national_extra modify updated_at timestamp null');
        $db->query('alter table nt_notices_national_extra_cims modify created_at timestamp null');
        $db->query('alter table nt_notices_national_extra_cities modify created_at timestamp null');
        $db->query('alter table nt_notices_national_texts modify created_at timestamp null');
        $db->query('alter table nt_notices_national_texts modify updated_at timestamp null');
        $db->query('alter table nt_notices_prr modify created_at timestamp null');
        $db->query('alter table nt_notices_prr modify updated_at timestamp null');
        $db->query('alter table nt_organisms modify created_at timestamp null');
        $db->query('alter table nt_organisms modify updated_at timestamp null');
        $db->query('alter table nt_prenotices modify created_at timestamp null');
        $db->query('alter table nt_prenotices modify updated_at timestamp null');
        $db->query('alter table nt_prenotices_external_links modify created_at timestamp null');
        $db->query('alter table nt_prenotices_external_links modify updated_at timestamp null');
        $db->query('alter table nt_sectors modify created_at timestamp null');
        $db->query('alter table nt_sectors modify updated_at timestamp null');
        $db->query('alter table nt_thematics modify created_at timestamp null');
        $db->query('alter table nt_thematics modify updated_at timestamp null');
        $db->query('alter table nt_types modify created_at timestamp null');
        $db->query('alter table nt_types modify updated_at timestamp null');
        $db->query('alter table user_bookmarks modify created_at timestamp null');
        $db->query('alter table user_bookmarks modify updated_at timestamp null');
        $db->query('alter table user_details modify created_at timestamp null');
        $db->query('alter table user_details modify updated_at timestamp null');
    }

    public function down()
    {
        //
    }
}
