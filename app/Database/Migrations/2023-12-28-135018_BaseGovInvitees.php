<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class BaseGovInvitees extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'remote_base_gov_id' => [
                'type' => 'INT',
                'constraint' => 11,
            ],
            'base_gov_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'nif' => [
                'type' => 'varchar',
                'constraint' => 20,
            ],
            'description' => [
                'type' => 'TEXT',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'base_gov_id', 'base_gov', 'id', 'CASCADE', 'CASCADE',
            'base_gov_invitees_base_gov_id_fk'
        );
        $this->forge->createTable('base_gov_invitees');
    }

    public function down()
    {
        $this->forge->dropTable('base_gov_invitees');
    }
}
