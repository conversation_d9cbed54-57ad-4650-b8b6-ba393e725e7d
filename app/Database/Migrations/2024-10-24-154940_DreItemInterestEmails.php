<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemInterestEmails extends Migration
{
    public function up()
    {
        $fields = [
            'interest_emails' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'interest'
            ]
        ];
        $this->forge->addColumn('dre_items', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('dre_items', [
            'interest_emails'
        ]);
    }
}
