<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNoticesEuropeCims extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'cim_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_europe', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_cims_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'cim_id', 'geo_cims', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_cims_cim_fk'
        );
        $this->forge->createTable('nt_notices_europe_cims');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_europe_cims');
    }
}
