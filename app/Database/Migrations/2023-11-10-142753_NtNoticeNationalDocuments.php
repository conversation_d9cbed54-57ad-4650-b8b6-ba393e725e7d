<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNoticiesNationalDocuments extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'documentoId' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'documentoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
            'tipoDocumentoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
            'documentoData' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'path' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'local_document' => [
                'type' => 'TINYINT',
                'null' => false,
                'default' => 0,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'null' => false,
                'default' => 'inactive',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_national', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_national_documents_notice_id_fk'
        );
        $this->forge->createTable('nt_notices_national_documents');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_national_documents');
    }
}
