<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemName extends Migration
{
    public function up()
    {
        $fields = [
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'title'
            ]
        ];
        $this->forge->addColumn('dre_items', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('dre_items', [
            'name'
        ]);
    }
}
