<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class UserBookmarks extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'extra_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'type' => [
                'type' => 'VARCHAR',
                'constraint' => 100,
                'null' => false,
            ],
            'url' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addForeignKey(
            'user_id', 'users', 'id', 'CASCADE', 'CASCADE',
            'user_bookmarks_auth_identities_user_id_fk'
        );
        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'user_bookmarks_notices_national_extra_extra_id'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('user_bookmarks');
    }

    public function down()
    {
        $this->forge->dropTable('user_bookmarks');
    }
}
