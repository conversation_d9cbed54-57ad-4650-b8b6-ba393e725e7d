<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class FailsafeSystem extends Migration
{
    public function up()
    {
        $fields = [
            'count_fails' => [
                'type' => 'INT',
                'constraint' => 5,
                'null' => true,
                'after' => 'direction',
                'default' => 0,
            ],
        ];
        $this->forge->addColumn('geo_city_halls', $fields);
        $this->forge->addColumn('geo_parish_councel', $fields);
        $this->forge->addColumn('geo_cims', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('geo_city_halls', 'count_fails');
        $this->forge->dropColumn('geo_parish_councel', 'count_fails');
        $this->forge->dropColumn('geo_cims', 'count_fails');

    }
}
