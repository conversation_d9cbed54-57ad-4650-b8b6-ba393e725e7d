<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AnnouncementsDetails extends Migration
{
    public function up()
    {
        $fields = [
            'dreSeries' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'default' => null,
                'null' => true,
                'after' => 'id',
            ],
            'modelType' => [
                'type' => 'TEXT',
                'default' => null,
                'null' => true,
                'after' => 'type',
            ],
            'cnccs' => [
                'type' => 'INT',
                'constraint' => 2,
                'default' => 0,
                'null' => true,
                'after' => 'modelType',
            ],
            'contractingProcedureId' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => true,
                'after' => 'modelType',
            ],
            'contractsCount' => [
                'type' => 'INT',
                'constraint' => 11,
                'default' => 0,
                'null' => true,
                'after' => 'contractingProcedureId',
            ],
            'announcementNumber' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'default' => null,
                'null' => true,
                'after' => 'contractsCount',
            ],
            'ambientCriteria' => [
                'type' => 'INT',
                'constraint' => 2,
                'default' => 0,
                'null' => true,
                'after' => 'announcementNumber',
            ],
            'contractingProcedureUrl' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'contractDesignation',
            ],
            'cpvs' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'contractingProcedureUrl',
            ],
            'contractType' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'cpvs',
            ],
            'contractingProcedureAliasID' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'contractType',
            ],
            'impugnations' => [
                'type' => 'INT',
                'constraint' => 1,
                'null' => true,
                'after' => 'contractingProcedureAliasID',
            ],
            'dreNumber' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'impugnations',
            ],
            'maximumEstimatedValueUnderFrameworkAgreement' => [
                'type' => 'INT',
                'constraint' => 2,
                'null' => true,
                'after' => 'dreNumber',
            ],
            'reference' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'maximumEstimatedValueUnderFrameworkAgreement',
            ],
            'reference' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'maximumEstimatedValueUnderFrameworkAgreement',
            ],
            'status' => [
                'type' => 'VARCHAR',
                'constraint' => '100',
                'default' => 'new',
                'after' => 'contractingProcedureType',
            ],
        ];
        $this->forge->addColumn('basegov_announcements', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('basegov_announcements', [
            'dreSeries',
            'modelType',
            'cnccs',
            'contractingProcedureId',
            'contractsCount',
            'announcementNumber',
            'ambientCriteria',
            'contractingProcedureUrl',
            'cpvs',
            'contractType',
            'contractingProcedureAliasID',
            'impugnations',
            'dreNumber',
            'maximumEstimatedValueUnderFrameworkAgreement',
            'reference',
            'reference',
            'status',
        ]);
    }
}
