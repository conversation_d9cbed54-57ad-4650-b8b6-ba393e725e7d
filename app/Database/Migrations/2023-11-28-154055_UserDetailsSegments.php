<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UserDetailsSegments extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_details_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'segment_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'segment_id', 'nt_segments', 'id', 'CASCADE', 'CASCADE',
            'user_details_segments_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'user_details_id', 'user_details', 'id', 'CASCADE', 'CASCADE',
            'user_details_segments_user_details_id_fk'
        );

        $this->forge->addKey('id', true);
        $this->forge->createTable('user_details_segments');
    }

    public function down()
    {
        $this->forge->dropTable('user_details_segments');
    }
}
