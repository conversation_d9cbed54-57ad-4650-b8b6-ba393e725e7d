<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class CreateProgramCitiesTable extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'program_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'city_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'program_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'program_cities_nt_notices_national_extra_fk'
        );
        $this->forge->addForeignKey(
            'city_id', 'geo_cities', 'id', 'CASCADE', 'CASCADE',
            'program_cities_geo_cities_fk'
        );
        $this->forge->createTable('program_cities');
    }

    public function down()
    {
        $this->forge->dropTable('program_cities');
    }
}
