<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNationalExtraCities extends Migration
{
    public function up()
    {
        $this->forge->renameTable('program_cities', 'nt_notices_national_extra_cities');

        $fields = [
            'program_id' => [
                'name' => 'extra_id',
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ];
        $this->forge->modifyColumn('nt_notices_national_extra_cities', $fields);

        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_national_extra_cities_nt_notices_national_extra_fk'
        );
        $this->forge->addForeignKey(
            'city_id', 'geo_cities', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_national_extra_cities_geo_cities_fk'
        );
    }

    public function down()
    {
        $this->forge->dropForeignKey('nt_notices_national_extra_cities', 'program_id');
        $this->forge->dropForeignKey('nt_notices_national_extra_cities', 'city_id');
        $this->forge->dropTable('nt_notices_national_extra_cities');
    }
}
