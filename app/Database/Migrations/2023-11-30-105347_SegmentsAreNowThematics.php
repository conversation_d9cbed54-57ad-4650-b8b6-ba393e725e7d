<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class SegmentsAreNowThematics extends Migration
{

    protected $thematics = [
        'id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'auto_increment' => true,
        ],
        'name' => [
            'type' => 'varchar',
            'constraint' => '255',
            'null' => false,
        ],
        'created_at' => [
            'type' => 'TIMESTAMP',
        ],
        'updated_at' => [
            'type' => 'TIMESTAMP',
        ],
    ];

    protected $noticeThematics = [
        'id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'auto_increment' => true,
        ],
        'extra_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
        'thematic_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
    ];

    protected $noticeSegments = [
        'id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'auto_increment' => true,
        ],
        'extra_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
        'segment_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
    ];

    protected $userSegments = [
        'id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'auto_increment' => true,
        ],
        'user_details_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
        'segment_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
    ];

    protected $userThematics = [
        'id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'auto_increment' => true,
        ],
        'user_details_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
        'thematic_id' => [
            'type' => 'INT',
            'constraint' => 11,
            'unsigned' => true,
            'null' => false,
        ],
    ];

    public function up()
    {
        $this->forge->dropTable('nt_notice_segments');
        $this->forge->dropTable('user_details_segments');
        $this->forge->dropTable('nt_segments');

        // Thematics
        $this->forge->addField($this->thematics);
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_thematics');

        // Notice Thematics
        $this->forge->addField($this->noticeThematics);
        $this->forge->addForeignKey(
            'thematic_id', 'nt_thematics', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_thematics_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_thematics_extra_id_fk'
        );

        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notice_thematics');

        $this->forge->addField($this->userThematics);
        $this->forge->addForeignKey(
            'thematic_id', 'nt_thematics', 'id', 'CASCADE', 'CASCADE',
            'user_details_thematics_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'user_details_id', 'user_details', 'id', 'CASCADE', 'CASCADE',
            'user_details_thematics_user_details_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('user_details_thematics');
    }

    public function down()
    {
        $this->forge->addField($this->thematics);
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_segments');

        $this->forge->addField($this->noticeSegments);
        $this->forge->addForeignKey(
            'segment_id', 'nt_segments', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_segments_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'extra_id', 'nt_notices_national_extra', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_segments_extra_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notice_segments');

        $this->forge->addField($this->userSegments);
        $this->forge->addForeignKey(
            'segment_id', 'nt_segments', 'id', 'CASCADE', 'CASCADE',
            'user_details_segments_notice_id_fk'
        );
        $this->forge->addForeignKey(
            'user_details_id', 'user_details', 'id', 'CASCADE', 'CASCADE',
            'user_details_segments_user_details_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('user_details_segments');
    }
}
