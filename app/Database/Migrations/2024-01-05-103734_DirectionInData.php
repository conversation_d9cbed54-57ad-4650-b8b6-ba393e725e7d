<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DirectionInData extends Migration
{
    public function up()
    {
        $fields = [
            'direction' => [
                'type' => 'varchar',
                'constraint' => '100',
                'default' => 'normal',
                'null' => false,
                'after' => 'last_connection',
            ],
        ];
        $this->forge->addColumn('geo_cims', $fields);
        $this->forge->addColumn('geo_city_halls', $fields);
        $this->forge->addColumn('geo_parish_councel', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('geo_cims', ['direction']);
        $this->forge->dropColumn('geo_city_halls', ['direction']);
        $this->forge->dropColumn('geo_parish_councel', ['direction']);
    }
}
