<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemDetails extends Migration
{
    public function up()
    {
        $fields = [
            'promoter' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'id'
            ],
            'ref' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'description'
            ],
            'procedure' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'ref'
            ],
            'base_price' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => true,
                'after' => 'procedure'
            ],
            'platform' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'base_price'
            ],
            'mono_price' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => true,
                'after' => 'platform'
            ],
            'date_clarify' => [
                'type' => 'DATE',
                'null' => true,
                'after' => 'mono_price'
            ],
            'hour_clarify' => [
                'type' => 'TIME',
                'null' => true,
                'after' => 'date_clarify'
            ],
            'clarifications' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'hour_clarify'
            ],
            'date_candidacy' => [
                'type' => 'DATE',
                'null' => true,
                'after' => 'clarifications'
            ],
            'hour_candidacy' => [
                'type' => 'TIME',
                'null' => true,
                'after' => 'date_candidacy'
            ],
            'date_proposal' => [
                'type' => 'DATE',
                'null' => true,
                'after' => 'hour_candidacy'
            ],
            'hour_proposal' => [
                'type' => 'TIME',
                'null' => true,
                'after' => 'date_proposal'
            ],
            'decision' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'null' => true,
                'after' => 'hour_proposal'
            ],
            'candidate' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
                'after' => 'decision'
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['no_status', 'open', 'closed'],
                'default' => 'no_status',
                'null' => false,
                'after' => 'candidate'
            ],
            'interest' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'null' => false,
                'after' => 'status'
            ],
            'obs' => [
                'type' => 'TEXT',
                'null' => true,
                'after' => 'interest'
            ]
        ];
        $this->forge->addColumn('dre_items', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('dre_items', [
            'promoter', 'ref', 'procedure', 'base_price', 'platform', 'mono_price',
            'date_clarify', 'hour_clarify', 'clarifications', 'date_candidacy',
            'hour_candidacy', 'date_proposal', 'hour_proposal', 'decision',
            'candidate', 'status', 'interest', 'obs'
        ]);
    }
}
