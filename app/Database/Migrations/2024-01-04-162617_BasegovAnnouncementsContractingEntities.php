<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovAnnouncementsContractingEntities extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'base_gov_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'announcement_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'nif' => [
                'type' => 'VARCHAR',
                'constraint' => 50,
                'null' => false,
            ],
            'description' => [
                'type' => 'text',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'announcement_id', 'basegov_announcements', 'id', 'CASCADE', 'CASCADE',
            'basegov_announcements_contracting_entities_announcement_id_fk'
        );
        $this->forge->createTable('basegov_announcements_contracting_entities');
    }

    public function down()
    {
        $this->forge->dropTable('basegov_announcements_contracting_entities');
    }
}
