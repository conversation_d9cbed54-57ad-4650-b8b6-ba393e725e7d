<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BaseGovData extends Migration
{
    public function up()
    {
        $fields = [
            'contractFundamentationType' => [
                'type' => 'TEXT',
                'after' => 'signingDate',
            ],
            'increments' => [
                'type' => 'INT',
                'after' => 'contractFundamentationType',
            ],
            'closeDate' => [
                'type' => 'date',
                'after' => 'increments',
            ],
            'causesDeadlineChange' => [
                'type' => 'TEXT',
                'after' => 'closeDate',
            ],
            'causesPriceChange' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'after' => 'causesDeadlineChange',
            ],
            'frameworkAgreementProcedureId' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'after' => 'causesPriceChange',
            ],
            'frameworkAgreementProcedureDescription' => [
                'type' => 'TEXT',
                'after' => 'frameworkAgreementProcedureId',
            ],
            'directAwardFundamentationType' => [
                'type' => 'TEXT',
                'after' => 'frameworkAgreementProcedureDescription',
            ],
            'ambientCriteria' => [
                'type' => 'TEXT',
                'after' => 'directAwardFundamentationType',
            ],
            'observations' => [
                'type' => 'TEXT',
                'after' => 'ambientCriteria',
            ],
            'contractingProcedureUrl' => [
                'type' => 'TEXT',
                'after' => 'publicationDate',
            ],
            'endOfContractType' => [
                'type' => 'TEXT',
                'after' => 'contractingProcedureUrl',
            ],
            'totalEffectivePrice' => [
                'type' => 'TEXT',
                'after' => 'endOfContractType',
            ],
            'announcementId' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'totalEffectivePrice',
            ],
            'centralizedProcedure' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'contractingProcedureType',
            ],
            'executionDeadline' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'after' => 'centralizedProcedure',
            ],
            'contractTypeCS' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'executionDeadline',
            ],
            'executionPlace' => [
                'type' => 'TEXT',
                'after' => 'contractTypeCS',
            ],
            'cpvs' => [
                'type' => 'TEXT',
                'after' => 'executionPlace',
            ],
            'nonWrittenContractJustificationTypes' => [
                'type' => 'TEXT',
                'after' => 'cpvs',
            ],
            'contractStatus' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'after' => 'initialContractualPrice',
            ],
            'materialCriteria' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'contractStatus',
            ],
            'contractTypes' => [
                'type' => 'TEXT',
                'after' => 'materialCriteria',
            ],
            'income' => [
                'type' => 'TEXT',
                'after' => 'objectBriefDescription',
            ],
            'aquisitionStateMemberUE' => [
                'type' => 'INT',
                'after' => 'signingDate',
            ],
            'infoAquisitionStateMemberUE' => [
                'type' => 'INT',
                'after' => 'aquisitionStateMemberUE',
            ],
            'groupMembers' => [
                'type' => 'TEXT',
                'after' => 'infoAquisitionStateMemberUE',
            ],
            'specialMeasures' => [
                'type' => 'TEXT',
                'after' => 'groupMembers',
            ],
            'regime' => [
                'type' => 'TEXT',
                'after' => 'specialMeasures',
            ],
            'cpvsType' => [
                'type' => 'TEXT',
                'after' => 'regime',
            ],
            'cpvsDesignation' => [
                'type' => 'TEXT',
                'after' => 'cpvsType',
            ],
            'cocontratantes' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'cpvsDesignation',
            ],
            'cpvsValue' => [
                'type' => 'TEXT',
                'after' => 'cocontratantes',
            ],
            'normal' => [
                'type' => 'INT',
                'constraint' => 11,
                'after' => 'cpvsValue',
            ],
            'description' => [
                'type' => 'TEXT',
                'after' => 'normal',
            ],
        ];

        $this->forge->addColumn('base_gov', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov', [
            'contractFundamentationType', 'increments', 'closeDate', 'causesDeadlineChange', 'causesPriceChange',
            'frameworkAgreementProcedureId', 'frameworkAgreementProcedureDescription',
            'directAwardFundamentationType', 'ambientCriteria', 'observations', 'contractingProcedureUrl',
            'endOfContractType', 'totalEffectivePrice', 'announcementId', 'centralizedProcedure', 'executionDeadline',
            'contractTypeCS', 'executionPlace', 'cpvs', 'nonWrittenContractJustificationTypes', 'contractStatus',
            'materialCriteria', 'contractTypes', 'income', 'aquisitionStateMemberUE', 'infoAquisitionStateMemberUE',
            'groupMembers', 'specialMeasures', 'regime', 'cpvsType', 'cpvsDesignation', 'cocontratantes', 'cpvsValue',
            'normal', 'description',
        ]);
    }
}
