<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNoticiesNational extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'avisoGlobalId' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'codigoAviso' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'designacaoPT' => [
                'type' => 'text',
                'null' => false,
            ],
            'classificacaoAvisoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'instrumentoTerritorialDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'contextoAvisoInstrumentoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => true,
            ],
            'dataUltimaAlteracao' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'dataPublicacao' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'dataInicio' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'dataFim' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'dataFimAtual' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'tempoMedioDecisaoFinal' => [
                'type' => 'INT',
                'constraint' => 9,
                'null' => true,
            ],
            'documentoId' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'documentoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
            'tipoDocumentoDesignacao' => [
                'type' => 'VARCHAR',
                'constraint' => 200,
                'null' => true,
            ],
            'documentoData' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'path' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'local_document' => [
                'type' => 'TINYINT',
                'null' => false,
                'default' => 0,
            ],
            'status' => [
                'type' => 'ENUM',
                'constraint' => ['active', 'inactive'],
                'null' => false,
                'default' => 'inactive',
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notices_national');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_national');
    }
}
