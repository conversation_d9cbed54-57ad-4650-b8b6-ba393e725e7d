<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class EuropeBookmark extends Migration
{
    public function up()
    {
        $fields = [
            'europe_id' => [
                'type' => 'INT',
                'constraint' => '11',
                'null' => true,
                'after' => 'prr_id',
            ],
        ];

        $this->forge->addColumn('user_bookmarks', $fields);
    }

    public function down()
    {
        $this->forge->addColumn('user_bookmarks', ['europe_id']);
    }
}
