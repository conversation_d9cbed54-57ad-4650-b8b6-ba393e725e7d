<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticePrr extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'link' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'name' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'program' => [
                'type' => 'TEXT',
                'null' => true,
            ],
            'date' => [
                'type' => 'date',
                'null' => true,
            ],
            'date_start' => [
                'type' => 'date',
                'null' => true,
            ],
            'date_end' => [
                'type' => 'date',
                'null' => true,
            ],
            'tags' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'texts' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'document' => [
                'type' => 'TEXT',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notices_prr');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_prr');
    }
}
