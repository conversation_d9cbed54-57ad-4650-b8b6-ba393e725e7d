<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class SectorIcon extends Migration
{
    public function up()
    {
        $fields = [
            'icon' => [
                'type' => 'VARCHAR',
                'constraint' => '255',
                'null' => true,
                'default' => '<i class="fa-light fa-vector-circle"></i>',
                'after' => 'name',
            ],
        ];

        $this->forge->addColumn('nt_sectors', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('nt_sectors', ['icon']);
    }
}
