<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovAlertsCpvs extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'alert_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'cpv' => [
                'type' => 'VARCHAR',
                'constraint' => 150,
                'null' => false,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'alert_id', 'basegov_alerts', 'id', 'CASCADE', 'CASCADE',
            'basegov_alerts_cpvs_alert_id_fk'
        );
        $this->forge->createTable('basegov_alerts_cpvs');
    }

    public function down()
    {
        $this->forge->dropTable('basegov_alerts_cpvs');
    }
}
