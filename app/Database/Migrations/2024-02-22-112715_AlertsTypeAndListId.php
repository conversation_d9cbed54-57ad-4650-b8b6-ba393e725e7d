<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class AlertsTypeAndListId extends Migration
{
    public function up()
    {
        $fields = [
            'type' => [
                'type' => 'varchar',
                'constraint' => 100,
                'default' => 'announcements',
                'null' => false,
                'after' => 'main',
            ],
            'list_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'id',
            ],
        ];
        $this->forge->addForeignKey(
            'list_id', 'base_gov_entity_lists', 'id', 'CASCADE', 'CASCADE', 'base_gov_alerts_lists_list_id'
        );
        $this->forge->addColumn('base_gov_alerts', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('base_gov_alerts', ['type', 'list_id']);
    }
}
