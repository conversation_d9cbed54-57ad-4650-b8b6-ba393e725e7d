<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class BasegovFails extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'vat' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'total_remote' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'remote_pages' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'last_page' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('base_gov_failed');
    }

    public function down()
    {
        $this->forge->dropTable('base_gov_failed');
    }
}
