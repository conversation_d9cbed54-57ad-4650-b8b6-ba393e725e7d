<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class NtNoticesPrrCities extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'notice_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'city_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'city_id', 'geo_cities', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_prr_cities_city_id_fk'
        );
        $this->forge->addForeignKey(
            'notice_id', 'nt_notices_prr', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_prr_cities_notice_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_notices_prr_cities');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_prr_cities');
    }
}
