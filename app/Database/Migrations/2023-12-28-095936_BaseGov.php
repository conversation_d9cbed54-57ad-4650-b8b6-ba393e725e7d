<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class BaseGov extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'vat' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'contractingProcedureType' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => true,
            ],
            'publicationDate' => [
                'type' => 'date',
                'null' => true,
            ],
            'ccp' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => true,
            ],
            'contracting' => [
                'type' => 'text',
                'null' => true,
            ],
            'contracted' => [
                'type' => 'text',
                'null' => true,
            ],
            'initialContractualPrice' => [
                'type' => 'varchar',
                'constraint' => '255',
                'null' => true,
            ],
            'objectBriefDescription' => [
                'type' => 'text',
                'null' => true,
            ],
            'signingDate' => [
                'type' => 'date',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('base_gov');

    }

    public function down()
    {
        $this->forge->dropTable('base_gov');
    }
}
