<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UserDetailsTypes extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'user_details_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'type_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
        ]);
        $this->forge->addForeignKey(
            'type_id', 'nt_types', 'id', 'CASCADE', 'CASCADE',
            'user_details_types_type_id_fk'
        );
        $this->forge->addForeignKey(
            'user_details_id', 'user_details', 'id', 'CASCADE', 'CASCADE',
            'user_details_types_user_details_id_fk'
        );

        $this->forge->addKey('id', true);
        $this->forge->createTable('user_details_types');
    }

    public function down()
    {
        $this->forge->dropTable('user_details_types');
    }
}
