<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ProgramsSegmentationAndCim extends Migration
{
    public function up()
    {
        $fields = [
            'segmentation_type' => [
                'type' => 'ENUM',
                'constraint' => ['districts', 'cities', 'nut_1', 'nut_2', 'nut_3'],
                'null' => true,
                'after' => 'estrategiaDesignacao',
            ],
            'cim' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'null' => 'false',
                'after' => 'segmentation_type',
            ],
        ];

        $this->forge->addColumn('nt_notices_national_extra', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('nt_notices_national_extra', ['segmentation_type', 'cim']);
    }
}
