<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class Entities extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'list_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'vat' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => false,
            ],
            'name' => [
                'type' => 'text',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => null,
            ],
        ]);
        $this->forge->addForeignKey(
            'list_id', 'base_gov_entity_lists', 'id', 'CASCADE', 'CASCADE',
            'base_gov_entity_lists_list_id'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('base_gov_entities');
    }

    public function down()
    {
        $this->forge->dropTable('base_gov_entities');
    }
}
