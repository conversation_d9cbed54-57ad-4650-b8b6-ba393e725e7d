<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class UserDetailsDistrictAndCity extends Migration
{
    public function up()
    {
        $fields = [
            'district_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'max_employees',
            ],
            'city_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => true,
                'after' => 'district_id',
            ],
        ];
        $this->forge->addForeignKey('district_id', 'geo_districts', 'id', 'CASCADE', 'CASCADE', 'user_details_district_id_fk');
        $this->forge->addForeignKey('city_id', 'geo_cities', 'id', 'CASCADE', 'CASCADE', 'user_details_city_id_fk');
        $this->forge->addColumn('user_details', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('user_details', ['district_id', 'city_id']);
    }
}
