<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreItemsCompanies extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'item_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false
            ],
            'company_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false
            ]
        ]);
        $this->forge->addForeignKey(
            'item_id', 'dre_items', 'id', 'CASCADE', 'CASCADE',
            'dre_items_companies_item_id_fk'
        );
        $this->forge->addForeignKey(
            'company_id', 'dre_companies', 'id', 'CASCADE', 'CASCADE',
            'dre_items_companies_company_id_fk'
        );
        $this->forge->addKey('id', true);
        $this->forge->createTable('dre_items_companies');
    }

    public function down()
    {
        $this->forge->dropTable('dre_items_companies');
    }
}
