<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtOrganisms extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'entity' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'person' => [
                'type' => 'text',
                'null' => true,
            ],
            'email' => [
                'type' => 'text',
                'null' => true,
            ],
            'phone' => [
                'type' => 'varchar',
                'null' => true,
                'constraint' => 12,
            ],
            'cellphone' => [
                'type' => 'varchar',
                'null' => true,
                'constraint' => 12,
            ],
            'website' => [
                'type' => 'text',
                'null' => false,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('nt_organisms');
    }

    public function down()
    {
        $this->forge->dropTable('nt_organisms');
    }
}
