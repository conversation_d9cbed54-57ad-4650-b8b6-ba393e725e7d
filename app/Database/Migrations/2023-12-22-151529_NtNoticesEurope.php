<?php
namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;
use CodeIgniter\Database\RawSql;

class NtNoticesEurope extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true,
            ],
            'organism_id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'null' => false,
            ],
            'program' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false,
            ],
            'name' => [
                'type' => 'text',
                'null' => true,
            ],
            'description' => [
                'type' => 'text',
                'null' => true,
            ],
            'priority' => [
                'type' => 'text',
                'null' => true,
            ],
            'objetives' => [
                'type' => 'text',
                'null' => true,
            ],
            'to_whom' => [
                'type' => 'text',
                'null' => true,
            ],
            'expenses' => [
                'type' => 'text',
                'null' => true,
            ],
            'budget' => [
                'type' => 'text',
                'null' => true,
            ],
            'tax' => [
                'type' => 'text',
                'null' => true,
            ],
            'starts_at' => [
                'type' => 'date',
                'null' => true,
            ],
            'ends_at' => [
                'type' => 'date',
                'null' => true,
            ],
            'website' => [
                'type' => 'text',
                'null' => true,
            ],
            'image' => [
                'type' => 'varchar',
                'constraint' => '100',
                'null' => true,
            ],
            'created_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
            'updated_at' => [
                'type' => 'TIMESTAMP',
                'default' => new RawSql('CURRENT_TIMESTAMP'),
            ],
        ]);
        $this->forge->addKey('id', true);
        $this->forge->addForeignKey(
            'organism_id', 'nt_organisms', 'id', 'CASCADE', 'CASCADE',
            'nt_notices_europe_organism_id_fk'
        );
        $this->forge->createTable('nt_notices_europe');
    }

    public function down()
    {
        $this->forge->dropTable('nt_notices_europe');
    }
}
