<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class ParsedDocuments extends Migration
{
    public function up()
    {
        $fields = [
            'parsed' => [
                'type' => 'TINYINT',
                'constraint' => 1,
                'default' => 0,
                'null' => 'false',
                'after' => 'local_document',
            ],
        ];
        $this->forge->addColumn('nt_notices_national_documents', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('nt_notices_national_documents', 'parsed');
    }
}
