<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DatesAndSyncSystem extends Migration
{
    public function up()
    {
        $fields = [
            'remote_total' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'name',
            ],
            'local_total' => [
                'type' => 'INT',
                'constraint' => 11,
                'null' => true,
                'after' => 'remote_total',
            ],
            'last_connection' => [
                'type' => 'TIMESTAMP',
                'after' => 'local_total',
                'null' => true,
            ],
        ];

        $this->forge->addColumn('geo_city_halls', $fields);
        $this->forge->addColumn('geo_parish_councel', $fields);
        $this->forge->addColumn('geo_cims', $fields);
    }

    public function down()
    {
        $this->forge->dropColumn('geo_city_halls', ['remote_total', 'local_total', 'last_connection']);
        $this->forge->dropColumn('geo_parish_councel', ['remote_total', 'local_total', 'last_connection']);
        $this->forge->dropColumn('geo_cims', ['remote_total', 'local_total', 'last_connection']);
    }
}
