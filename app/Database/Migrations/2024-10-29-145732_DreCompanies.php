<?php

namespace App\Database\Migrations;

use CodeIgniter\Database\Migration;

class DreCompanies extends Migration
{
    public function up()
    {
        $this->forge->addField([
            'id' => [
                'type' => 'INT',
                'constraint' => 11,
                'unsigned' => true,
                'auto_increment' => true
            ],
            'name' => [
                'type' => 'VARCHAR',
                'constraint' => 255,
                'null' => false
            ]
        ]);
        $this->forge->addKey('id', true);
        $this->forge->createTable('dre_companies');

        $seeder = \Config\Database::seeder();
        $seeder->call('\App\Database\Seeds\Companies');
    }

    public function down()
    {
        $this->forge->dropTable('dre_companies');
    }
}
