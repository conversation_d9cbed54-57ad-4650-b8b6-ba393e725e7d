<?php

use CodeIgniter\Pager\PagerRenderer;

/**
 * @var PagerRenderer $pager
 */
$pager->setSurroundCount(2);
?>

<nav class="Page navigation" aria-label="<?php echo lang('Pager.pageNavigation') ?>">
	<ul class="pagination">
		<?php if ($pager->hasPrevious()): ?>
			<li class="page-item">
				<a class="page-link" href="<?php echo $pager->getFirst() ?>" aria-label="<?php echo lang('Pager.first') ?>">
					<span aria-hidden="true"><i class="fa-duotone fa-chevrons-left"></i></span>
				</a>
			</li>
			<li class="page-item">
				<a class="page-link" href="<?php echo $pager->getPrevious() ?>" aria-label="<?php echo lang('Pager.previous') ?>">
					<span aria-hidden="true"><i class="fa-duotone fa-chevron-left"></i></span>
				</a>
			</li>
		<?php endif?>

		<?php foreach ($pager->links() as $link): ?>
			<li <?php echo $link['active'] ? 'class="page-item active"' : '' ?>>
				<a class="page-link" href="<?php echo $link['uri'] ?>">
					<?php echo $link['title'] ?>
				</a>
			</li>
		<?php endforeach?>

		<?php if ($pager->hasNext()): ?>
			<li class="page-item">
				<a class="page-link" href="<?php echo $pager->getNext() ?>" aria-label="<?php echo lang('Pager.next') ?>">
					<span aria-hidden="true"><i class="fa-duotone fa-chevron-right"></i></span>
				</a>
			</li>
			<li class="page-item">
				<a class="page-link" href="<?php echo $pager->getLast() ?>" aria-label="<?php echo lang('Pager.last') ?>">
					<span aria-hidden="true"><i class="fa-duotone fa-chevrons-right"></i></span>
				</a>
			</li>
		<?php endif?>
	</ul>
</nav>
