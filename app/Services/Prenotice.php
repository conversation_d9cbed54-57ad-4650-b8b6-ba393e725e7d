<?php

namespace App\Services;

class Prenotice
{

    /**
     * Notice Model (nt_notices_europe)
     * @var object
     */
    public $notice;
    /**
     * Area Model (nt_areas)
     * @var object
     */
    public $area;
    /**
     * NoticeAreaModel  (nt_notices_europe_areas)
     * @var object
     */
    public $noticeArea;
    /**
     * EuropeLinkModel (nt_europe_external_links)
     * @var
     */
    public $link;

    /**
     * Define all the service dependencies
     * @param \App\Models\PrenoticeModel        $noticeEuropeModel
     * @param \App\Models\AreaModel             $areaModel
     * @param \App\Models\NoticeEuropeAreaModel $preNoticeAreaModel
     * @param \App\Models\PreNoticeLinkModel    $preNoticeLinkModel
     */
    public function __construct(
        \App\Models\PrenoticeModel $preNoticeModel,
        \App\Models\AreaModel $areaModel,
        \App\Models\PreNoticeAreaModel $preNoticeAreaModel,
        \App\Models\PreNoticeLinkModel $preNoticeLinkModel) {

        $this->notice = $preNoticeModel;
        $this->area = $areaModel;
        $this->noticeArea = $preNoticeAreaModel;
        $this->link = $preNoticeLinkModel;
    }

    /**
     * Create a new european Notice
     * @param  array  $notice Notice data
     * @return bool
     */
    public function create(array $notice): bool
    {
        $notice['status'] = 'inactive';
        $notice = $this->prepareDates($notice);
        $noticeId = $this->notice->insert($notice);

        if (!$noticeId) {
            return false;
        }
        $this->addLinks($noticeId, $notice['more_info']);
        $this->addAreas($noticeId, $notice['area']);

        return true;
    }

    /**
     * Prepare dates (can be transform in a public function in the future)
     * @param  array $notice The selected notice data
     * @return array Notice with the dates transformed
     */
    protected function prepareDates(array $notice): array
    {
        if (!isset($notice['dates'])) {
            $notice['start_date'] = $notice['end_date'] = null;

            return $notice;
        }

        $dates = explode('–', $notice['dates']);
        $notice['start_date'] = (isset($dates[0])) ? $dates[0] . '-01-01' : null;
        $notice['end_date'] = (isset($dates[1])) ? $dates[1] . '-12-31' : null;

        return $notice;
    }

    /**
     * Associate links to a given notice
     * @param int    $noticeId The selected notice
     * @param string $links    The selected links
     */
    protected function addLinks(int $noticeId, array $links): bool
    {
        $check = true;
        foreach ($links as $link) {
            if (!$this->link->insert(['notice_id' => $noticeId, 'url' => $link])) {
                $check = false;
            }
        }

        return $check;
    }

    /**
     * Associate areas to a given notice
     * @param int    $noticeId The selected notice
     * @param string $area     The selected area
     */
    protected function addAreas(int $noticeId, string $area)
    {
        $checkArea = $this->area->like('name', $area, 'after')->where('type', 'europe')->first();
        $areaId = $checkArea->id ?? null;
        if (empty($checkArea)) {
            $areaId = $this->area->insert(['name' => $area, 'type' => 'europe']);
        }
        if ($this->noticeArea->insert(['europe_notice_id' => $noticeId, 'area_id' => $areaId])) {
            return true;
        }

        return false;
    }

}
