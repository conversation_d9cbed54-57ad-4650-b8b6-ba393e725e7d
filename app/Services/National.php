<?php

namespace App\Services;

use \DateTime;

class National
{

    /**
     * Notice Model (nt_notices_national)
     * @var object
     */
    protected object $notice;
    /**
     * Program Model (nt_notices_national_extra)
     * @var object
     */
    protected object $program;
    /**
     * Text Model (nt_notices_national_extra)
     * @var object
     */
    protected object $text;
    /**
     * Document Model (nt_notices_national_documents)
     * @var object
     */
    protected object $document;

    /**
     * City Model (geo_cities)
     * @var object
     */
    protected object $city;
    /**
     * The default filters
     * @var array
     */
    protected array $defaultFilters = [
        'nt_notices_national.status' => 'active',
        'nt_notices_national_extra.status' => 'active',
    ];
    /**
     * The default select for our queries
     * @var string
     */
    protected string $defaultSelect = '
        nt_notices_national_extra.id, nt_notices_national_extra.notice_id,
        nt_notices_national_extra.programaOperacionalDesignacao, nt_notices_national_extra.prioridadeDesignacao,
        nt_notices_national_extra.objetivoEspecificoDesignacao, nt_notices_national_extra.tipologiaAcaoDesignacao,
        nt_notices_national_extra.tipologiaIntervencaoDesignacao,
        nt_notices_national_extra.tipologiaOperacaoDesignacao, nt_notices_national_extra.tipoFinanciamentoDesignacao,
        nt_notices_national_extra.fundoDesignacao, nt_notices_national_extra.fonteFinanciamentoNacionalDesignacao,
        nt_notices_national_extra.dotacao, nt_notices_national_extra.estrategiaDesignacao,
        nt_notices_national.avisoGlobalId, nt_notices_national.codigoAviso,
        nt_notices_national.designacaoPT, nt_notices_national.classificacaoAvisoDesignacao,
        nt_notices_national.instrumentoTerritorialDesignacao, nt_notices_national.contextoAvisoInstrumentoDesignacao,
        nt_notices_national.dataUltimaAlteracao, nt_notices_national.dataPublicacao, nt_notices_national.dataInicio,
        nt_notices_national.dataFim, nt_notices_national.dataFimAtual, nt_notices_national.tempoMedioDecisaoFinal,
        nt_notices_national.documentoId, nt_notices_national.documentoDesignacao,
        nt_notices_national.tipoDocumentoDesignacao, nt_notices_national.documentoData, nt_notices_national.path,
        nt_notices_national.local_document, nt_notices_national.status';
    /**
     * Types of notices
     * @var array
     */
    public array $types = [
        'europe' => 'União Europeia',
        'national' => 'Nacional',
    ];
    /**
     * Filters set by the user
     * @var array
     */
    public array $filters = [];

    /**
     * Construct the notice service
     * @param \App\Models\NoticeNationalModel           $notice
     * @param \App\Models\NoticiesNationalExtraModel    $programModel
     * @param \App\Models\NoticeNationalTextModel       $text
     * @param \App\Models\NoticiesNationalDocumentModel $document
     */
    public function __construct(
        \App\Models\NoticeNationalModel $notice,
        \App\Models\NoticiesNationalExtraModel $programModel,
        \App\Models\NoticeNationalTextModel $text,
        \App\Models\NoticiesNationalDocumentModel $document,
        \App\Models\CityModel $city
    ) {
        $this->notice = $notice;
        $this->program = $programModel;
        $this->text = $text;
        $this->document = $document;
        $this->city = $city;
    }

    /**
     * Find notices
     * @param  array  $filters The filters
     * @param  string $select  The select only if needed
     * @return array  of notices
     */
    public function findAll(array $filters = [], string $select = null): array
    {
        $this->filters = $filters;

        $this->select($select)->orderBy($this->filters['orderBy'] ?? 'id')
            ->search($this->filters['search'] ?? '')
            ->where($this->filters);

        return [
            'data' => $this->program->join(
                'nt_notices_national', 'nt_notices_national_extra.notice_id = nt_notices_national.id'
            )->paginate(15),
            'pagination' => $this->program->pager,
        ];
    }

    /**
     * Get only bookmarked items
     * @return National
     */
    public function bookmarks(): National
    {
        $this->program->join('user_bookmarks', 'nt_notices_national_extra.id = user_bookmarks.extra_id')
            ->where('user_bookmarks.user_id', auth()->user()->id);

        return $this;
    }

    /**
     * What type of notices are we getting?
     * National
     * @param  string $type The selected type
     * @return this
     */
    public function type(string $type): National
    {
        $this->defaultFilters = array_merge(
            $this->defaultFilters,
            ['nt_notices_national_extra.tipoFinanciamentoDesignacao' => $this->types[$type]]
        );

        return $this;
    }

    /**
     * Use default select or use your own
     * @param  string|null $select
     * @return this
     */
    protected function select(string $select = null): National
    {
        $select = (!is_null($select)) ? $select : $this->defaultSelect;
        $select .= ', (SELECT COUNT(id)
            FROM user_bookmarks
            WHERE extra_id = nt_notices_national_extra.id
            AND user_id = ' . auth()->user()->id . ') AS bookmarks,
            (SELECT icon
                FROM nt_sectors
                JOIN nt_notice_sectors ON nt_sectors.id = nt_notice_sectors.sector_id
                WHERE nt_notice_sectors.extra_id = nt_notices_national_extra.id
                ORDER BY RAND()
                LIMIT 1
            ) AS icon';

        $this->program->select($select);

        return $this;
    }

    /**
     * Use default where or set your own
     * @param  array  $filters The filters we're using
     * @return this
     */
    protected function where(array $filters = []): National
    {
        $filters = (!empty($this->filters)) ? $this->filters : $this->defaultFilters;
        // Data de fim ainda não ultrapassada
        $this->program->where('dataFim >', date('Y-m-d'));
        // Data de Fim
        if (isset($filters['dateEnd']) && !empty($filters['dateEnd'])) {
            $date = DateTime::createFromFormat('m/d/Y', $filters['dateEnd']);
            $this->program->where('dataFim <=', $date->format('Y-m-d'));
        }
        // Nome do programa
        if (isset($filters['programName']) && !empty($filters['programName'])) {
            $this->program->where('nt_notices_national_extra.programaOperacionalDesignacao', $filters['programName']);
        }
        // Status do programa
        if (isset($filters['status']) && !empty($filters['status'])) {
            $this->program->where('nt_notices_national_extra.status', $filters['status']);
            $this->program->where('nt_notices_national.status', $filters['status']);
            unset($filters['status']);
        }
        // GEO (districts + cities)
        if (isset($filters['districts'])) {
            $cityIds = $filters['cities'] ?? [];
            if (!isset($filters['cities'])) {
                $cityIds = array_column(
                    $this->city->asArray()->whereIn('district_id', $filters['districts'])->findAll(), 'id'
                );
            }
            $this->program->join('nt_notices_national_extra_cities', 'nt_notices_national_extra.id = nt_notices_national_extra_cities.extra_id');
            $this->program->whereIn('nt_notices_national_extra_cities.city_id', $cityIds);
        } else if (isset($filters['cims'])) {
            $this->program->join('nt_notices_national_extra_cims', 'nt_notices_national_extra.id = nt_notices_national_extra_cims.extra_id');
            $this->program->whereIn('nt_notices_national_extra_cims.cim_id', $filters['cims']);
        }
        // SETORES
        if (isset($filters['sectors'])) {
            $this->program->join('nt_notice_sectors', 'nt_notices_national_extra.id = nt_notice_sectors.extra_id');
            $this->program->whereIn('nt_notice_sectors.sector_id', $filters['sectors']);
        }
        // TEMATICAS
        if (isset($filters['thematics'])) {
            $this->program->join('nt_notice_thematics', 'nt_notices_national_extra.id = nt_notice_thematics.extra_id');
            $this->program->whereIn('nt_notice_thematics.thematic_id', $filters['thematics']);
        }
        // Tipos de Entidade
        if (isset($filters['types'])) {
            $this->program->join('nt_notice_types', 'nt_notices_national_extra.id = nt_notice_types.extra_id');
            $this->program->whereIn('nt_notice_types.type_id', $filters['types']);
        }
        unset($filters['types'], $filters['thematics'], $filters['sectors'], $filters['cims'], $filters['districts'],
            $filters['cities'], $filters['dateEnd'], $filters['programName'], $filters['clearUp']);

        $filters = array_merge($filters, $this->defaultFilters);
        // Pesquisa generica
        foreach ($filters as $field => $value) {
            if ($field === 'page') {
                continue;
            }
            $this->program->where($field, $value);
        }

        return $this;
    }

    /**
     * Order results by a default field or a given param
     * @param  string     $field The param
     * @return National
     */
    protected function orderBy(string $field = 'nt_notices_national.id'): National
    {
        unset($this->filters['orderBy']);
        if ($field === 'date') {
            $field = 'nt_notices_national.dataFim';
        }

        $this->program->orderBy($field, 'DESC');

        return $this;
    }

    /**
     * Search in two fields, if more fields are set they should all be set here
     * @param  string     $search The search string
     * @return National
     */
    protected function search(string $search = ''): National
    {
        unset($this->filters['search']);
        if (empty($search)) {
            return $this;
        }

        $this->program->groupStart()
            ->orLike('nt_notices_national.designacaoPT', $search)
            ->orLike('nt_notices_national_extra.objetivoEspecificoDesignacao', $search)
            ->groupEnd();

        return $this;
    }
}
