<?php

namespace App\Services;

class Notification
{
    /**
     * Store the Codeignite email object
     */
    protected object $mail;
    /**
     * Set default from can be overriten via $this->initialize
     * @var array
     */
    protected array $from = [
        'email' => '<EMAIL>',
        'name' => 'Radar'
    ];
    /**
     * Set to
     */
    protected string $to;
    /**
     * Set cc
     */
    protected array $cc;
    /**
     * Set bcc
     */
    protected array $bcc = ['<EMAIL>'];
    /**
     * Set subject
     */
    protected string $subject;
    /**
     * Set message
     */
    protected string $message;
    /**
     * Set config
     */
    protected array $config;

    /**
     * Set dependencies
     * @param object $mail The email class
     */
    public function __construct($mail)
    {
        $this->mail = $mail;
    }

    /**
     * Use a different set of settings other than App/Config/Email.php
     * To use this service is as follows
     * service('notification')->initialize(['to' => '<EMAIL>'])
     *      ->subject('My subject')
     *      ->message('my message')
     *      ->send();
     * @param  array        $config array of settings
     * @return Notification $this
     */
    public function config(array $config): Notification
    {
        $this->config = $config;
        $this->mail->initialize($this->config);

        return $this;
    }

    /**
     * Initialize the email preferences this must be used before seding at least with $data['to'] = <EMAIL>
     * @param  array  $data  All the people that are receiving the email
     * @return object This
     */
    public function initialize(array $data = []): Notification
    {
        $this->from = $data['from'] ?? $this->from;
        $this->to = $data['to'];
        $this->cc = $data['cc'] ?? [];
        $this->bcc = array_merge($data['bcc'] ?? [], $this->bcc);

        return $this;
    }

    /**
     * Set a subject
     * @param  string $subject The subject for your message
     * @return object this
     */
    public function subject(string $subject): Notification
    {
        if (empty($subject)) {
            throw new \Exception('Deve definir um subject para enviar o seu email');
        }
        $this->subject = $subject;

        return $this;
    }

    /**
     * Set the message
     * @param  string $message The subjet for the message
     * @return object This
     */
    public function message(string $message): Notification
    {
        if (empty($message)) {
            throw new \Exception('Deve definir uma mensagem para enviar o seu email');
        }
        $this->message = $message;

        return $this;
    }

    /**
     * Send the actual email
     * @return bool
     */
    public function send(): bool
    {
        if (!$this->validateData()) {
            return false;
        }
        // In development use send mail and see your emails on devilbox
        $to = trim($this->to);
        if (ENVIRONMENT === 'development') {
            $this->config([
                'protocol' => 'sendmail',
                'mailPath' => '/usr/sbin/sendmail',
                'charset' => 'iso-8859-1',
                'wordWrap' => true
            ]);
            
        }
        $this->mail->setFrom($this->from['email'], $this->from['name']);
        $this->mail->setTo($to);
        $this->mail->setCC($this->cc);
        $this->mail->setBCC($this->bcc);
        $this->mail->setSubject($this->subject);
        $this->mail->setMessage($this->message);

        return $this->mail->send();
    }

    /**
     * Validate required data before sending
     * @return bool
     */
    protected function validateData(): bool
    {
        if (empty($this->to) || empty($this->subject) || empty($this->message)) {
            return false;
        }

        return true;
    }

}
