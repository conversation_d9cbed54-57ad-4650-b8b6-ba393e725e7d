<?php namespace App\Services;

class User
{
    /**
     * UserDetail Model
     * @var object
     */
    protected $userDetail;
    /**
     * UserIdentity Model
     * @var object
     */
    protected $userIdentity;
    /**
     * User Provider
     * @var object
     */
    protected $userProvider;

    /**
     * Set default dependencies
     * @param object $userDetailModel
     */
    public function __construct(object $userDetailModel, object $userIdentityModel)
    {
        $this->userDetail = $userDetailModel;
        $this->userIdentity = $userIdentityModel;
        $this->userProvider = auth()->getProvider();
    }

    /**
     * Redirects to user creation or update
     * @param  array  $data
     * @return bool
     */
    public function save(array $data): bool
    {
        if (!$this->validateData($data)) {
            return false;
        }
        if (!empty($data['id'])) {
            return $this->update($data);
        }

        return $this->create($data);
    }

    /**
     * Validate account
     * @param  int    $userId
     * @param  bool   $active
     * @return bool
     */
    public function validateAccount(int $userId, bool $active): bool
    {
        $user = auth()->getProvider()->findById($userId);
        if (!$user) {
            return false;
        }

        $user->fill([
            'active' => $active,
        ]);

        return $this->userProvider->save($user);
    }

    /**
     * Handle forgot password proccess
     * @param  object        $user
     * @return string|bool
     */
    public function generateForgotPasswordToken(object $user): string | bool
    {
        $identityData = $user->getEmailIdentity();
        if ($identityData->force_reset && (strtotime($identityData->expires) > strtotime(date('Y/m/d H:i:s')))) {
            session()->setFlashdata('error', 'O pedido para recuperar a password foi feito recentemente. Por favor, verifique o seu email.');
            return false;
        }

        // Force password reset
        $user->forcePasswordReset();

        return $this->generateToken($identityData->id, date('Y/m/d H:i:s', strtotime('+30 minutes')));
    }

    /**
     * Generate random token
     * @param  int           $identityId
     * @param  string        $expireDate
     * @return string|bool
     */
    public function generateToken(int $identityId, string $expireDate = null): string | bool
    {
        // Generate token to save
        $token = uniqid();
        $dataToUpdate = [
            'expires' => $expireDate,
            'extra' => $token,
        ];

        if (!$this->userIdentity->update($identityId, $dataToUpdate)) {
            session()->setFlashdata('error', 'Algo correu mal ao recuperar a sua password. Entre em contacto com um técnico');
            return false;
        }

        return $token;
    }

    /**
     * update user password
     * @param  array  $data (user_id, password, password_confirm)
     * @return bool
     */
    public function updatePassword(array $data): bool
    {
        if (!$this->validatePasswords($data)) {
            return false;
        }

        $user = $this->userProvider->findById($data['user_id']);
        if (!$user) {
            session()->setFlashdata('error', 'Algo correu mal ao atualizar a sua password. Entre em contacto com um técnico');
            return false;
        }
        $user->fill([
            'password' => $data['password'],
        ]);
        if (!$this->userProvider->save($user)) {
            session()->setFlashdata('error', 'Algo correu mal ao atualizar a sua password. Entre em contacto com um técnico');
            return false;
        }

        // remove force password flag on a user
        $user->undoForcePasswordReset();

        return true;
    }

    /**
     * Validate user subscriptions before login [filter CheckUserActivated]
     * @param  int    $userId
     * @return bool
     */
    public function validateUserSubscription(int $userId): bool
    {
        $userDetails = $this->userDetail->where('user_id', $userId)->first();
        $userType = $userDetails->user_type;
        // if user_type is employee, get company
        if ($userType === 'employee') {
            $userDetails = $this->userDetail->where('user_id', $userDetails->company_id)->first();
        }

        if (!$userDetails->start_date) {
            session()->setFlashdata('error', 'A subscrição associada a esta conta não foi definida. Por favor, contact um técnico');
            return false;
        }
        if ((date('Y-m-d') < $userDetails->start_date)) {
            $diffDays = date_diff(date_create(date('Y-m-d')), date_create($userDetails->start_date))->format("%r%a");
            session()->setFlashdata('error', 'A subscrição associada a esta conta terá inicio dentro de ' . $diffDays . ' dias [' . $userDetails->start_date . ']');
            return false;
        }
        if ((date('Y-m-d') > $userDetails->due_date)) {
            session()->setFlashdata('error', 'A subscrição associada a esta conta encontra-se expirada. Por favor, contacte um técnico');
            return false;
        }

        // days left to end the subscription
        $diffDays = date_diff(date_create(date('Y-m-d')), date_create($userDetails->due_date))->format("%r%a");
        if ($diffDays < 10 && $userType === 'company') {
            session()->setFlashdata('confirm', 'Faltam ' . $diffDays . ' dias para terminar a sua subscrição. Para prorrogar, entre em contacto com o técnico.');
        }

        return true;
    }

    /**
     * Delete user
     * @param  int    $id Selected user id
     * @return bool
     */
    public function delete(int $id): bool
    {
        $user = $this->userProvider->findById($this->userProvider->getInsertID());

        return $this->userProvider->delete($user->id);
    }

    /**
     * create user
     * @param  array  $data
     * @return bool
     */
    private function create(array $data): bool
    {
        if ($data['group'] === 'employee' && !$this->validateEmployeesNumber($data['company_id'])) {
            return false;
        }

        if ($this->userProvider->findByCredentials(['email' => $data['email']])) {
            session()->setFlashdata('error', 'Já existe um utilizador com o e-mail inserido');
            return false;
        }

        try {
            $dataToCreate = [
                'email' => $data['email'],
                'username' => $data['username'] ?? null,
                'active' => (isset($data['active'])) ? $data['active'] : 1,
                'password' => $data['password'],
            ];

            $user = new \CodeIgniter\Shield\Entities\User($dataToCreate);
            $this->userProvider->save($user);
        } catch (\CodeIgniter\Shield\Exceptions\ValidationException $e) {
            session()->setFlashdata('error', $e->getMessage());
            return false;
        } catch (\CodeIgniter\Database\Exceptions\DatabaseException $e) {
            session()->setFlashdata('error', 'Não foi possível criar o utilizador com os dados inseridos. Por favor, tente de novo
                ou contacte um técnico.');
            return false;
        }

        // get inserted record
        $user = $this->userProvider->findById($this->userProvider->getInsertID());
        // set group
        $user->addGroup($data['group']);

        $data['user_id'] = $user->id;
        $data['user_type'] = $data['group'];
        // unset unnecessary fields to update user detail
        unset(
            $data['csrf_test_name'], $data['id'], $data['username'], $data['password'], $data['group'],
            $data['old_subscription_start_date'], $data['password_confirm'], $data['email'], $data['active']
        );

        if (!model('\App\Models\UserDetailModel')->save($data)) {
            session()->setFlashdata('errors', model('\App\Models\UserDetailModel')->errors());
            $this->delete($user->id);
            return false;
        }

        session()->setFlashdata('confirm', 'O utilizador foi criado com sucesso');
        return true;
    }

    /**
     * update user
     * @param  array  $data
     * @return bool
     */
    private function update(array $data): bool
    {
        $dataToUpdate = [
            'username' => $data['username'] ?? null,
            'email' => $data['email'],
            'active' => (isset($data['status']) && $data['status'] === '0') ? false : true,
        ];

        if (!empty($data['password'])) {
            $dataToUpdate['password'] = $data['password'];
        }

        try {
            $user = $this->userProvider->findById($data['id']);
            $user->fill($dataToUpdate);
            $this->userProvider->save($user);
        } catch (\CodeIgniter\Shield\Exceptions\ValidationException $e) {
            session()->setFlashdata('error', $e->getMessage());
            return false;
        } catch (\CodeIgniter\Database\Exceptions\DatabaseException $e) {
            session()->setFlashdata('error', 'Não foi possível atualizar o utilizador com os dados inseridos. Por favor, tente de novo
                ou contacte um técnico.');
            return false;
        }

        $userDetailId = $data['user_detail_id'];
        // unset unnecessary fields to update user detail
        unset(
            $data['csrf_test_name'], $data['id'], $data['company_id'], $data['user_detail_id'], $data['group'], $data['email'],
            $data['username'], $data['password'], $data['password_confirm'], $data['old_subscription_start_date'],
        );

        if (!empty($data) && !model('\App\Models\UserDetailModel')->update($userDetailId, $data)) {
            session()->setFlashdata('errors', model('\App\Models\UserDetailModel')->errors());
            return false;
        }

        session()->setFlashdata('confirm', 'Os dados foram atualizados com sucesso');
        return true;
    }

    /**
     * Validate all data before save
     * @param  array  $data
     * @return bool
     */
    private function validateData(array $data): bool
    {
        if (!$this->validateSubscriptionDates($data)) {
            return false;
        }
        if (!$this->validatePasswords($data)) {
            return false;
        }

        return true;
    }

    /**
     * Validate subscription date
     * @param  array  $data
     * @return bool
     */
    private function validateSubscriptionDates(array $data): bool
    {
        if (!isset($data['due_date'])) {
            return true;
        }
        if (date('Y-m-d') > $data['due_date']) {
            session()->setFlashdata('errors', 'A data de vencimento da subscrição não pode ser inferior ao dia atual');
            return false;
        }
        if ($data['start_date'] > $data['due_date']) {
            session()->setFlashdata('error', 'A data de inicio não pode ser superior à data de vencimento');
            return false;
        }

        return true;
    }

    /**
     * Validate employees number
     * @param  int    $companyId
     * @return bool
     */
    private function validateEmployeesNumber(int $companyId): bool
    {
        $companyDetails = $this->userProvider->getUserDetails('company', $companyId)[0];
        if ($companyDetails->max_employees <= $companyDetails->count_employees) {
            session()->setFlashdata('error', 'Não é possível adicionar mais Utilizadores à entidade');
            return false;
        }

        return true;
    }

    /**
     * Validate passwords
     * @param  array  $data
     * @return bool
     */
    private function validatePasswords(array $data): bool
    {
        if (!isset($data['password']) || (empty($data['password']) && empty($data['password_confirm']))) {
            return true;
        }
        if ($data['password'] !== $data['password_confirm']) {
            session()->setFlashdata('error', 'As passwords não coincidem');
            return false;
        }

        return true;
    }
}
