{"name": "codeigniter4/appstarter", "description": "CodeIgniter4 starter app", "license": "MIT", "type": "project", "homepage": "https://codeigniter.com", "support": {"forum": "https://forum.codeigniter.com/", "source": "https://github.com/codeigniter4/CodeIgniter4", "slack": "https://codeigniterchat.slack.com"}, "require": {"php": "^7.4 || ^8.0", "almasaeed2010/adminlte": "~3.2", "codeigniter4/framework": "4.4.3", "codeigniter4/shield": "^1.0@beta", "dompdf/dompdf": "^2.0", "elephpant/breadcrumb": "^2.0", "guzzlehttp/guzzle": "^7.0", "phpoffice/phpspreadsheet": "^2.0", "seunmatt/codeigniter-log-viewer": "^2.0", "smalot/pdfparser": "^2.7", "spekulatius/phpscraper": "*", "zrashwani/arachnid": "^2.2"}, "require-dev": {"fakerphp/faker": "^1.9", "mikey179/vfsstream": "^1.6"}, "autoload": {"exclude-from-classmap": ["**/Database/Migrations/**"]}, "autoload-dev": {"psr-4": {"Tests\\Support\\": "tests/_support"}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "scripts": {"post-update-cmd": ["cp -R vendor/almasaeed2010/adminlte/dist/ public/admin", "cp -R vendor/almasaeed2010/adminlte/plugins/ public/admin"]}, "minimum-stability": "dev", "prefer-stable": true}