<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Notícias</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('articles') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($article->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> notícia
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open_multipart('articles/save', ['id' => 'articles-form']) ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $article->id ?? null ?>">
                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="title" class="form-label">Título</label>
                                    <input type="text" name="title" class="form-control" aria-describedby="title" value="<?php echo set_value('title', $article->title ?? null) ?>" placeholder="Título" required>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="intro" class="form-label">Introdução</label>
                                    <input type="text" name="intro" class="form-control" aria-describedby="intro" value="<?php echo set_value('intro', $article->intro ?? null) ?>" placeholder="Introdução" required>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">Conteúdo</label>
                                    <textarea id="summernote" name="description" rows="20" required><?php echo set_value('description', $article->description ?? null) ?></textarea>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group tag-area">
                                    <label for="tags" class="form-label">Tags</label>
                                    <ul class="tags-ul"><input type="text" class="tag-input" id="input-tags"/></ul>
                                    <input type="hidden" name="tags" value="<?php echo set_value('tags', $article->tags ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="image" class="form-label">Imagem</label>
                                    <?php if (!empty($article->image)): ?>
                                        <div>
                                            <a class="mr-2" href="<?php echo base_url('uploads/images/' . ($article->image)) ?>" target="_blank"><i class="fa-duotone fa-image mr-1"></i><?php echo $article->image ?></a>
                                            <a onclick="return confirmDelete();" href="<?php echo site_url('articles/delete-img/' . $article->id) ?>"><i class="fa-duotone fa-trash"></i></a>
                                        </div>
                                    <?php else: ?>
                                        <input type="file" class="form-control" name="image"/>
                                    <?php endif?>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="source" class="form-label">Origem</label>
                                    <input type="text" name="source" class="form-control" aria-describedby="source" value="<?php echo set_value('source', $article->source ?? null) ?>" placeholder="Link da origem"required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Data de publicação</label>
                                    <input type="datetime-local" class="form-control" name="schedule" value="<?php echo set_value('schedule', $article->schedule ?? '') ?>" placeholder="Data de publicação" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>


<?php echo $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.js"></script>
<script src="<?php echo base_url('dist/js/tags.js') ?>"></script>
<script>
    $(document).ready(function() {
        $('#summernote').summernote();
    });
</script>
<?php echo $this->endSection() ?>


<?php echo $this->section('styles') ?>
<link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote.min.css" rel="stylesheet">
<?php echo $this->endSection() ?>
