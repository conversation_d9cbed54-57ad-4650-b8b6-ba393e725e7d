<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Entidades</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('companies') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <?php echo form_open_multipart('companies/save') ?>
        <?php echo csrf_field() ?>
        <input type="hidden" name="id" value="<?php echo $company->id ?? null ?>">
        <input type="hidden" name="user_detail_id" value="<?php echo $userDetailId ?? null ?>">
        <input type="hidden" name="old_subscription_start_date" value="<?php echo $company->start_date ?? null ?>">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h3 class="card-title">
                            <?php if (empty($company->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> entidade
                        </h3>
                    </div>
                    <div class="card-body">
                        <h5 class="mb-3"><b>Dados de acesso</b></h5>
                        <div class="row mb-3">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="username" class="form-label">NIF</label>
                                    <input type="text" name="username" minlength="9" maxlength="9" class="form-control" aria-describedby="username" value="<?php echo set_value('username', $company->username ?? null) ?>" placeholder="Introduza o número de contribuinte" required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">E-mail</label>
                                    <input type="email" name="email" class="form-control" aria-describedby="email" value="<?php echo set_value('email', $company->email ?? null) ?>" placeholder="Introduza o e-mail"required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" name="password" class="form-control" minlength="8" placeholder="Introduza a password" <?php if (empty($company->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Confirmar Password</label>
                                    <input type="password" name="password_confirm" class="form-control" minlength="8" placeholder="Confirmar a password" <?php if (empty($company->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                        </div>
                        <h5 class="mb-3"><b>Dados de subscrição</b></h5>
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="start_date" class="form-label">Data de Início</label>
                                    <input type="date" placeholder="dd-mm-yyyy" name="start_date" class="form-control" value="<?php echo set_value('start_date', $company->start_date ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">Data de Vencimento</label>
                                    <input type="date" name="due_date" class="form-control" value="<?php echo set_value('due_date', $company->due_date ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="max_employees" class="form-label">Nº Máx. de Utilizadores</label>
                                    <input type="number" name="max_employees" class="form-control" placeholder="Introduza o número máximo de Utilizadores" min="0" value="<?php echo set_value('max_employees', $company->max_employees ?? null) ?>"required>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="status" class="form-label">Estado</label>
                                    <select required name="status" class="form-control">
                                        <option></option>
                                        <option <?php if ($company->active): ?>selected<?php endif?> value="1">Ativo</option>
                                        <option <?php if (!$company->active): ?>selected<?php endif?> value="0">Inativo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-6 col-xs-12">
                                <h5><b>Geografia</b></h5>
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="city_id" class="form-label">Geografia</label>
                                            <select required name="city_id" class="form-control">
                                                <option></option>
                                                <?php foreach ($districts as $district): ?>
                                                    <optgroup label="<?php echo $district->name ?>">
                                                        <?php foreach ($district->cities as $city): ?>
                                                            <option <?php if (isset($company->city_id) && $company->city_id === $city->id): ?>selected<?php endif?> value="<?php echo $city->id ?>"><?php echo $city->name ?></option>
                                                        <?php endforeach?>
                                                    </optgroup>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="cim_id" class="form-label">Comunidade Inter Municipal</label>
                                            <select required name="cim_id" class="form-control">
                                                <option></option>
                                                <?php foreach ($cims as $cim): ?>
                                                    <option <?php if (isset($cim->id) && isset($company) && $company->cim_id === $cim->id): ?>selected<?php endif?> value="<?php echo $cim->id ?>"><?php echo $cim->name ?></option>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-xs-12">
                                <h5><b>Entidade</b></h5>
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="image" class="form-label">Logotipo</label>
                                            <?php if (!empty($company->image)): ?>
                                                <div class="img-company">
                                                    <a class="mr-2" href="<?php echo base_url('uploads/users/images/' . ($company->image)) ?>" target="_blank"><img src="<?php echo base_url('uploads/users/images/' . ($company->image)) ?>" class=""/></a>
                                                    <a class="delete-img-company" onclick="return confirmDelete();" href="<?php echo site_url('companies/delete-img/' . $company->userDetailId) ?>"><i class="fa-duotone fa-trash"></i></a>
                                                </div>
                                            <?php else: ?>
                                                <input type="file" class="form-control" name="image"/>
                                            <?php endif?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h3 class="card-title">
                            Editar Temáticas
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($thematics)): ?>
                            <div class="row">
                                <h5>Deve <a href="<?php echo site_url('settings/thematics/insert') ?>">criar temáticas</a> para os poder atribuir a entidades</h5>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-12">
                                    <?php foreach ($thematics as $thematic): ?>
                                        <div class="form-check">
                                            <input <?php if (in_array($thematic->id, $userDetails->thematics)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="thematics[]" value="<?php echo $thematic->id ?>" id="thematic<?php echo $thematic->id ?>">
                                            <label class="form-check-label" for="thematic<?php echo $thematic->id ?>"><?php echo $thematic->name ?></label>
                                        </div>
                                    <?php endforeach?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>
                        <?php endif?>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h3 class="card-title">
                            Editar Tipo de Entidade
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($types)): ?>
                            <div class="row">
                                <h5>Deve <a href="<?php echo site_url('settings/types/insert') ?>">criar Tipo de Entidade</a> para os poder atribuir a entidades</h5>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-12">
                                    <?php foreach ($types as $type): ?>
                                        <div class="form-check">
                                            <input <?php if (in_array($type->id, $userDetails->types)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="types[]" value="<?php echo $type->id ?>" id="types<?php echo $type->id ?>">
                                            <label class="form-check-label" for="types<?php echo $type->id ?>"><?php echo $type->name ?></label>
                                        </div>
                                    <?php endforeach?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>
                        <?php endif?>
                    </div>
                </div>
            </div>
            <div class="col-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between">
                        <h3 class="card-title">
                            Editar Setores
                        </h3>
                    </div>
                    <div class="card-body">
                        <?php if (empty($sectors)): ?>
                            <div class="row">
                                <h5>Deve <a href="<?php echo site_url('settings/types/insert') ?>">criar Tipo de Entidade</a> para os poder atribuir a entidades</h5>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-12">
                                    <?php foreach ($sectors as $sector): ?>
                                        <div class="form-check">
                                            <input <?php if (in_array($sector->id, $userDetails->sectors)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="sectors[]" value="<?php echo $sector->id ?>" id="sectors<?php echo $sector->id ?>">
                                            <label class="form-check-label" for="sectors<?php echo $sector->id ?>"><?php echo $sector->name ?></label>
                                        </div>
                                    <?php endforeach?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>
                        <?php endif?>
                    </div>
                </div>
            </div>
        </div>
    <?php echo form_close() ?>
</div>
<?php echo $this->endSection() ?>
