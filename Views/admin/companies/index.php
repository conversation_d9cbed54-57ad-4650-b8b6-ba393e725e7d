<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Entidades ativas</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('companies/insert') ?>" class="btn btn-primary"><i class="fa-duotone fa-plus"></i> Inserir entidade</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">Lista de entidades ativas</h3>
                </div>
                <div class="card-body">
                    <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>NIF</th>
                                <th>Utilizadores</th>
                                <th>Subscrição</th>
                                <th>Estado</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($companies as $company): ?>
                            <tr>
                                <td><?php echo $company->id ?></td>
                                <td><?php echo $company->secret ?? ''; ?></td>
                                <td><?php echo $company->username ?></td>
                                <td>
                                    <?php if ((int) $company->max_employees !== 0): ?>
                                    <div class="btn-group" role="group">
                                        <a class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Gerir Utilizadores" href="<?php echo site_url('companies/employees/' . $company->id) ?>"><i class="fa-duotone fa-users"></i> <?php echo $company->count_employees ?> / <?php echo $company->max_employees ?></a>
                                    </div>
                                    <?php endif;?>
                                </td>
                                <td>
                                   <?php if (date("Y-m-d") < $company->due_date): ?>
                                        <span class="badge badge-success">Válida</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Expirada</span>
                                    <?php endif;?>
                                </td>
                                <td>
                                   <?php if ($company->active): ?>
                                        <span class="badge badge-success">Ativo</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">Inativo</span>
                                    <?php endif;?>
                                </td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo site_url('companies/update/' . $company->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Editar"><i class="fa-duotone fa-pen-to-square"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Apagar" onclick="return confirmDelete();" href="<?php echo site_url('companies/delete/' . $company->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable();
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>