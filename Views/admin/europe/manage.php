<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Avisos Europeus</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('notices/europe') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($notice->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Avisos Europeus
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('notices/europe/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $notice->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="code">Código</label>
                                <input type="text" class="form-control" name="code" aria-describedby="code" value="<?php echo set_value('code', $notice->code ?? null) ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label for="name">Designação</label>
                                <input type="text" class="form-control" name="name" aria-describedby="name" value="<?php echo set_value('name', $notice->name ?? null) ?>" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="program_id" class="form-label">Programa</label>
                                    <select required name="program_id" class="form-control">
                                        <option value=""></option>
                                        <?php foreach ($programs as $program): ?>
                                            <option <?php if (isset($notice->program_id) && $notice->program_id === $program->id): ?>selected<?php endif?> value="<?php echo $program->id ?>"><?php echo $program->program ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="thematic_id" class="form-label">Temática</label>
                                    <select required name="thematic_id" class="form-control">
                                        <option value=""></option>
                                        <?php foreach ($thematics as $thematic): ?>
                                            <option <?php if (isset($notice->thematic_id) && $notice->thematic_id === $thematic->id): ?>selected<?php endif?> value="<?php echo $thematic->id ?>"><?php echo $thematic->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="start_date" class="form-label">Início</label>
                                    <input type="date" name="start_date" class="form-control" aria-describedby="start_date" value="<?php echo set_value('start_date', $notice->start_date ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="end_date" class="form-label">Fim</label>
                                    <input type="date" name="end_date" class="form-control" aria-describedby="end_date" value="<?php echo set_value('end_date', $notice->end_date ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="link" class="form-label">Website</label>
                                    <input type="text" name="link" class="form-control" aria-describedby="link" value="<?php echo set_value('link', $notice->link ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>