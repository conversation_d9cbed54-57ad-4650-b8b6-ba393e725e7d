<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12">
                <h1 class="m-0"><strong>Avisos no Portal:</strong> <small><a target="_blank" href="https://portugal2030.pt">https://portugal2030.pt</a></small></h1>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Programas indexados</h3>
                    </div>
                    <div class="card-body">
                        <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                            <thead>
                                <tr>
                                    <th>Programa Operacional</th>
                                    <th>Objetivo</th>
                                    <th>Financiamento</th>
                                    <th>Status</th>
                                    <th class="text-right">Dotação</th>
                                    <th class="text-right"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($programs as $program): ?>
                                    <tr>
                                        <td><?php echo $program->programaOperacionalDesignacao ?></td>
                                        <td><?php echo $program->objetivoEspecificoDesignacao ?></td>
                                        <td><?php echo $program->tipoFinanciamentoDesignacao ?></td>
                                        <td>
                                            <?php echo view_cell('\Admin\Cells\NoticeCell::status', ['status' => $program->status]) ?>
                                        </td>
                                        <td class="text-right"><?php echo $formatter->formatCurrency($program->dotacao, 'EUR'), PHP_EOL; ?></td>
                                        <td class="text-right">
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo site_url('notices/national/programs/sectors/' . $program->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Setores"><i class="fa-duotone fa-vector-circle"></i></a>
                                                <a href="<?php echo site_url('notices/national/programs/types/' . $program->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Tipo de Entidade"> <i class="fa-duotone fa-tags"></i></a>
                                                <a href="<?php echo site_url('notices/national/programs/thematics/' . $program->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Temáticas"><i class="fa-duotone fa-sitemap"></i></a>
                                                <a href="<?php echo site_url('notices/national/programs/update/' . $program->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Editar">
                                                    <i class="fa-duotone fa-pen-to-square"></i>
                                                </a>
                                                <a onclick="return confirmDelete();" href="<?php echo site_url('notices/national/programs/delete/' . $program->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Apagar">
                                                    <i class="fa-duotone fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable();
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>