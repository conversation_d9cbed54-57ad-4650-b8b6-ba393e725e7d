<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12">
                <h1 class="m-0"><strong>Avisos no Portal:</strong> <small><a target="_blank" href="https://portugal2030.pt">https://portugal2030.pt</a></small></h1>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><strong><?php echo $notice->designacaoPT ?></strong></h3>
                    </div>
                    <div class="card-body">
                        <?php echo form_open('notices/national/save') ?>
                            <input type="hidden" name="id" value="<?php echo $notice->id ?>">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="avisoGlobalId">Aviso Global ID</label>
                                        <input type="text" class="form-control" readonly name="avisoGlobalId" value="<?php echo $notice->avisoGlobalId ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="codigoAviso">Código Aviso</label>
                                        <input type="text" class="form-control" readonly name="codigoAviso" value="<?php echo $notice->codigoAviso ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="designacaoPT">Titulo</label>
                                        <input class="form-control" type="text" required name="designacaoPT" value="<?php echo $notice->designacaoPT ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="classificacaoAvisoDesignacao">Classificacao Aviso Designacao</label>
                                        <input class="form-control" type="text" name="classificacaoAvisoDesignacao" value="<?php echo $notice->classificacaoAvisoDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <label for="instrumentoTerritorialDesignacao">Instrumento Territorial Designacao</label>
                                    <input type="text" class="form-control" name="instrumentoTerritorialDesignacao" value="<?php echo $notice->instrumentoTerritorialDesignacao ?>">
                                </div>
                                <div class="col-6">
                                    <label for="contextoAvisoInstrumentoDesignacao">Contexto Aviso Instrumento Designação</label>
                                    <input type="text" class="form-control" name="contextoAvisoInstrumentoDesignacao" value="<?php echo $notice->contextoAvisoInstrumentoDesignacao ?>">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="dataPublicacao">Data Publicação</label>
                                        <input class="form-control" type="datetime-local" name="dataPublicacao" value="<?php echo $notice->dataPublicacao ?>">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="dataInicio">Data Início</label>
                                        <input class="form-control" type="datetime-local" name="dataInicio" value="<?php echo $notice->dataInicio ?>">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="dataFim">Data Fim</label>
                                        <input class="form-control" type="datetime-local" name="dataFim" value="<?php echo $notice->dataFim ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4">
                                    <label for="tempoMedioDecisaoFinal">Tempo Médio Decisão Final</label>
                                    <input type="text" class="form-control" value="<?php echo $notice->tempoMedioDecisaoFinal ?>">
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select name="status" required class="form-control">
                                            <option></option>
                                            <option <?php if ($notice->status === 'active'): ?>selected<?php endif?> value="active">Ativo</option>
                                            <option <?php if ($notice->status === 'inactive'): ?>selected<?php endif?> value="inactive">Inativo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>

                        <?php echo form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php echo $this->endSection() ?>
