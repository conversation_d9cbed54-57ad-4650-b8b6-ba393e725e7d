<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><strong>Avisos no Portal:</strong> <small><a target="_blank" href="https://portugal2030.pt">https://portugal2030.pt</a></small></h1>
            </div>
            <div class="col-sm-6 text-right">
                <a href="<?php echo site_url('notices/national/programs/' . $program->notice_id) ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title"><strong><?php echo $program->programaOperacionalDesignacao ?></strong></h3>
                    </div>
                    <div class="card-body">
                        <?php echo form_open_multipart('notices/national/programs/save') ?>
                            <input type="hidden" name="id" value="<?php echo $program->id ?>">
                            <input type="hidden" name="notice_id" value="<?php echo $program->notice_id ?>">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="programaOperacionalDesignacao">Designação</label>
                                        <input class="form-control" type="text" required name="programaOperacionalDesignacao" value="<?php echo $program->programaOperacionalDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="prioridadeDesignacao">Prioridade</label>
                                        <input class="form-control" type="text" name="prioridadeDesignacao" value="<?php echo $program->prioridadeDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="objetivoEspecificoDesignacao">Objetivo Especifico</label>
                                        <input class="form-control" type="text" name="objetivoEspecificoDesignacao" value="<?php echo $program->objetivoEspecificoDesignacao ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="tipologiaAcaoDesignacao">Tipologia Ação</label>
                                        <input class="form-control" type="text" name="tipologiaAcaoDesignacao" value="<?php echo $program->tipologiaAcaoDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="tipologiaIntervencaoDesignacao">Tipologia Intervencao</label>
                                        <input class="form-control" type="text" name="tipologiaIntervencaoDesignacao" value="<?php echo $program->tipologiaIntervencaoDesignacao ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="tipologiaOperacaoDesignacao">Tipologia Operacao</label>
                                        <input class="form-control" type="text" name="tipologiaOperacaoDesignacao" value="<?php echo $program->tipologiaOperacaoDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="tipoFinanciamentoDesignacao">Tipo Financiamento</label>
                                        <input class="form-control" type="text" name="tipoFinanciamentoDesignacao" value="<?php echo $program->tipoFinanciamentoDesignacao ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="fundoDesignacao">Fundo</label>
                                        <input class="form-control" type="text" name="fundoDesignacao" value="<?php echo $program->fundoDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="fonteFinanciamentoNacionalDesignacao">Fonte Financiamento Nacional</label>
                                        <input class="form-control" type="text" name="fonteFinanciamentoNacionalDesignacao" value="<?php echo $program->fonteFinanciamentoNacionalDesignacao ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="estrategiaDesignacao">Estratégia</label>
                                        <input class="form-control" type="text" name="estrategiaDesignacao" value="<?php echo $program->estrategiaDesignacao ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="dotacao">Dotacao</label>
                                        <input class="form-control" type="text" name="dotacao" value="<?php echo $program->dotacao ?>">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select name="status" required class="form-control">
                                            <option></option>
                                            <option <?php if ($program->status === 'active'): ?>selected<?php endif?> value="active">Ativo</option>
                                            <option <?php if ($program->status === 'inactive'): ?>selected<?php endif?> value="inactive">Inativo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label for="image" class="form-label">Imagem do tipo de aviso</label>
                                        <?php if (!empty($program->image)): ?>
                                            <div>
                                                <a class="mr-2" href="<?php echo base_url('uploads/programs/images/' . ($program->image)) ?>" target="_blank"><i class="fa-duotone fa-image mr-1"></i><?php echo $program->image ?></a>
                                                <a onclick="return confirmDelete();" href="<?php echo site_url('notices/national/programs/delete-img/' . $program->id) ?>"><i class="fa-duotone fa-trash"></i></a>
                                            </div>
                                        <?php else: ?>
                                            <input type="file" class="form-control" name="image"/>
                                        <?php endif?>
                                    </div>
                                </div>
                            </div>
                            <p><hr></p>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="thematics">Temáticas</label>
                                        <select name="thematics[]" class="form-control" multiple>
                                            <?php foreach ($thematics as $thematic): ?>
                                                <option <?php if (in_array($thematic->id, $program->thematics)): ?>selected<?php endif?> value="<?php echo $thematic->id ?>"><?php echo $thematic->name ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="types">Tipo de Entidade</label>
                                        <select name="types[]" class="form-control" multiple>
                                            <?php foreach ($types as $type): ?>
                                                <option <?php if (in_array($type->id, $program->types)): ?>selected<?php endif?> value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="sectors">Setores</label>
                                        <select name="sectors[]" class="form-control" multiple>
                                            <?php foreach ($sectors as $sector): ?>
                                                <option <?php if (in_array($sector->id, $program->sectors)): ?>selected<?php endif?> value="<?php echo $sector->id ?>"><?php echo $sector->name ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-12">
                                    <p><strong>Segmentação</strong></p>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'districts')" type="radio" name="segmentation[filterType]" id="districts" value="districts" checked <?php if ($program->segmentation_type === 'districts'): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="district">Distrito</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'cities')" type="radio" name="segmentation[filterType]" id="cities" value="cities" <?php if ($program->segmentation_type === 'cities'): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="concelho">Concelho</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_1')" type="radio" name="segmentation[filterType]" id="nut_1" value="nut_1" <?php if ($program->segmentation_type === 'nut_1'): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="nut_1">NUT 1</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_2')" type="radio" name="segmentation[filterType]" id="nut_2" value="nut_2" <?php if ($program->segmentation_type === 'nut_2'): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="nut_2">NUT 2</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_3')" type="radio" name="segmentation[filterType]" id="nut_3" value="nut_3" <?php if ($program->segmentation_type === 'nut_3'): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="nut_3">NUT 3</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'cims')" type="radio" name="segmentation[filterType]" id="cims" value="cims" <?php if ($program->cim != 0): ?>checked<?php endif?>>
                                        <label class="form-check-label" for="cims">CIM</label>
                                    </div>
                                    <p><hr></p>
                                    <div class="radio-segmentation" data-programid="<?php echo $program->id ?>" data-type="<?php echo $program->segmentation_type ?>" data-cim="<?php echo (int) $program->cim ?>">
                                        <select name="segmentation[districts][]" multiple class="sel-districts d-none form-control districts<?php echo $program->id ?>" style="height: 300px;">
                                            <?php foreach ($districts as $district): ?>
                                                <option <?php if ($program->segmentation_type === 'districts' && in_array($district->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $district->id ?>"><?php echo $district->name ?></option>
                                            <?php endforeach?>
                                        </select>
                                        <select name="segmentation[cities][]" multiple class="sel-cities d-none form-control cities<?php echo $program->id ?>" style="height: 300px;">
                                            <?php foreach ($districts as $district): ?>
                                                <optgroup label="<?php echo $district->name ?>">
                                                    <?php foreach ($district->cities as $city): ?>
                                                        <option <?php if ($program->segmentation_type === 'cities' && in_array($city->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $city->id ?>"><?php echo $city->name ?></option>
                                                    <?php endforeach?>
                                                </optgroup>
                                            <?php endforeach?>
                                        </select>
                                        <select name="segmentation[nut_1][]" multiple class="sel-nut_1 d-none form-control nut_1<?php echo $program->id ?>" style="height: 300px;">
                                            <?php foreach ($nuts1 as $nut1): ?>
                                                <option <?php if ($program->segmentation_type === 'nut_1' && in_array($nut1->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut1->nut ?>"><?php echo $nut1->nut ?></option>
                                            <?php endforeach?>
                                        </select>
                                        <select name="segmentation[nut_2][]" multiple class="sel-nut_2 d-none form-control nut_2<?php echo $program->id ?>" style="height: 200px;">
                                            <?php foreach ($nuts2 as $nut2): ?>
                                                <option <?php if ($program->segmentation_type === 'nut_2' && in_array($nut2->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut2->nut ?>"><?php echo $nut2->nut ?></option>
                                            <?php endforeach?>
                                        </select>
                                        <select name="segmentation[nut_3][]" multiple class="sel-nut_3 d-none form-control nut_3<?php echo $program->id ?>" style="height: 100px;">
                                            <?php foreach ($nuts3 as $nut3): ?>
                                                <option <?php if ($program->segmentation_type === 'nut_3' && in_array($nut3->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut3->nut ?>"><?php echo $nut3->nut ?></option>
                                            <?php endforeach?>
                                        </select>
                                        <select name="segmentation[cims][]" multiple class="sel-cims d-none form-control cims<?php echo $program->id ?>" style="height: 300px;">
                                            <?php foreach ($cims as $cim): ?>
                                                <option <?php if ($program->cim && in_array($cim->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $cim->id ?>"><?php echo $cim->name ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>
                        <?php echo form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
<script src="<?php echo base_url('dist/js/segmentation.js'); ?>" ></script>
<?php echo $this->endSection() ?>
