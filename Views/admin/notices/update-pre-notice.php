<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12">
                <h1 class="m-0">Avisos Europeus <small><a target="_blank" href="https://portaldosfundoseuropeus.pt">https://portaldosfundoseuropeus.pt</a></small></h1>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Editar <strong><?php echo $notice->title ?></strong></h3>
                    </div>
                    <div class="card-body">
                        <?php echo form_open('notices/pre-notice/save') ?>
                            <input type="hidden" name="id" value="<?php echo $notice->id ?>">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="origin">URL</label>
                                        <input class="form-control" type="text" required name="origin" value="<?php echo $notice->origin ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="title">Titulo</label>
                                        <input class="form-control" type="text" required name="title" value="<?php echo $notice->title ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="description">Descrição</label>
                                        <textarea rows="12" class="form-control" required name="description"><?php echo $notice->description ?></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label for="tags">tags</label>
                                        <input class="form-control" type="text" required name="tags" value="<?php echo $notice->tags ?>">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="start_date">Data de ínicio</label>
                                        <input class="form-control" type="date" required name="start_date" value="<?php echo $notice->start_date ?>">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="end_date">Data de Fim</label>
                                        <input class="form-control" type="date" required name="end_date" value="<?php echo $notice->end_date ?>">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label for="status">Status</label>
                                        <select name="status" required class="form-control">
                                            <option></option>
                                            <option <?php if ($notice->status === 'active'): ?>selected<?php endif?> value="active">Ativo</option>
                                            <option <?php if ($notice->status === 'inactive'): ?>selected<?php endif?> value="inactive">Inativo</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>

                        <?php echo form_close() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php echo $this->endSection() ?>
