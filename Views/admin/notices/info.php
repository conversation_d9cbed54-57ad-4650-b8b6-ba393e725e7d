<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
<?php echo form_open('notices/national/global-save') ?>
    <input type="hidden" name="id" value="<?php echo $notice->id ?>">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Avisos</h1>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="callout callout-info">
                    <h5><i class="fa-duotone fa-info"></i> <?php echo $notice->designacaoPT ?></h5>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="invoice p-3 mb-3">
                    <div class="row invoice-info">
                        <div class="col-sm-4 invoice-col">
                            <p class="lead">Informação base</p>
                            <strong>Aviso Global ID: </strong><?php echo $notice->avisoGlobalId ?><br>
                            <strong>Código Aviso: </strong><?php echo $notice->codigoAviso ?><br>
                            <strong>Titulo: </strong><?php echo $notice->designacaoPT ?><br>
                            <strong>Classificacao Aviso Designacao: </strong><?php echo $notice->classificacaoAvisoDesignacao ?><br>
                        </div>
                        <div class="col-sm-4 invoice-col">
                            <p class="lead">Datas</p>
                            <strong>Última alteração: </strong><?php echo $notice->dataUltimaAlteracao ?> <br>
                            <strong>Data Publicação: </strong><?php echo $notice->dataPublicacao ?><br>
                            <strong>Data Início: </strong><?php echo $notice->dataInicio ?><br>
                            <strong>Data Fim: </strong><?php echo $notice->dataFim ?><br>
                            <strong>Data de Finalização atual: </strong><?php echo $notice->dataFimAtual ?>
                        </div>

                        <div class="col-sm-4 invoice-col">
                            <p class="lead">Outras informações</p>
                            <strong>Tempo Médio Decisão Final: </strong><?php echo $notice->tempoMedioDecisaoFinal ?><br>
                            <strong>Instrumento Territorial Designacao:</strong><br>
                            <?php echo $notice->instrumentoTerritorialDesignacao ?><br>
                            <strong>Contexto Aviso Instrumento Designação:</strong><br>
                            <?php echo $notice->contextoAvisoInstrumentoDesignacao ?><br>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12"><hr></div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <p class="lead"><strong>Outros Textos</strong></p>
                            <div class="accordion" id="texts">
                                <?php foreach ($texts as $text): ?>
                                    <div class="card">
                                        <div class="card-header" id="headingTexts<?php echo $text->id ?>">
                                            <h2 class="mb-0">
                                                <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapseTexts<?php echo $text->id ?>" aria-expanded="true" aria-controls="collapse<?php echo $text->id ?>">
                                                    <span class="text-left">
                                                        <a target="_blank" href="<?php echo site_url('notices/national/documents/texts/update/' . $text->id) ?>" data-toggle="tooltip" data-placement="top" title="Editar">
                                                            <i class="fa-duotone fa-pen-to-square"></i>
                                                        </a>
                                                        <?php echo $text->title ?>
                                                    </span>
                                                    <span class="text-right">
                                                        <?php echo view_cell('\Admin\Cells\NoticeCell::status', ['status' => $text->status]) ?>
                                                    </span>
                                                </button>
                                            </h2>
                                        </div>
                                        <div id="collapseTexts<?php echo $text->id ?>" class="collapse" aria-labelledby="headingTexts<?php echo $text->id ?>" data-parent="#texts">
                                            <div class="card-body">
                                                <p><?php echo nl2br($text->content) ?></p>
                                                <p><hr></p>
                                                <p>
                                                    <input data-onstyle="success" data-offstyle="danger" data-style="quick" type="checkbox" name="texts[<?php echo $text->id ?>]" <?php if ($text->status === 'active'): ?>checked<?php endif?> data-toggle="toggle">
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12"><hr></div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="accordion" id="programs">
                                <p class="lead">Programas indexados</p>
                                <?php foreach ($programs as $program): ?>
                                    <div class="card">
                                        <div class="card-header" id="heading<?php echo $program->id ?>">
                                            <h2 class="mb-0">
                                                <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" data-target="#collapse<?php echo $program->id ?>" aria-expanded="true" aria-controls="collapse<?php echo $program->id ?>">
                                                    <span class="text-left">
                                                        <a target="_blank" href="<?php echo site_url('notices/national/programs/update/' . $program->id) ?>" data-toggle="tooltip" data-placement="top" title="Editar">
                                                        <i class="fa-duotone fa-pen-to-square"></i>
                                                        </a>
                                                        <?php echo $program->programaOperacionalDesignacao ?> / <?php echo $program->tipoFinanciamentoDesignacao ?> / <?php echo $formatter->formatCurrency($program->dotacao, 'EUR'), PHP_EOL; ?>
                                                    </span>
                                                    <span class="text-right">
                                                        <?php echo view_cell('\Admin\Cells\NoticeCell::status', ['status' => $program->status]) ?>
                                                    </span>

                                                </button>
                                            </h2>
                                        </div>
                                        <div id="collapse<?php echo $program->id ?>" class="collapse" aria-labelledby="heading<?php echo $program->id ?>" data-parent="#programs">
                                            <div class="card-body">
                                                <p><strong>Prioridade:</strong> <?php echo $program->prioridadeDesignacao ?></p>
                                                <p><strong>Objetivo Especifico:</strong> <?php echo $program->objetivoEspecificoDesignacao ?></p>
                                                <p><strong>Tipologia Ação:</strong> <?php echo $program->tipologiaAcaoDesignacao ?></p>
                                                <p><strong>Tipologia Intervenção:</strong> <?php echo $program->tipologiaIntervencaoDesignacao ?></p>
                                                <p><strong>Tipologia Operação:</strong> <?php echo $program->tipologiaOperacaoDesignacao ?></p>
                                                <p><strong>Tipo Financiamento:</strong> <?php echo $program->tipoFinanciamentoDesignacao ?></p>
                                                <p><strong>Fundo:</strong> <?php echo $program->fundoDesignacao ?></p>
                                                <p><strong>Fonte Financiamento Nacional:</strong> <?php echo $program->fonteFinanciamentoNacionalDesignacao ?></p>
                                                <p><strong>Estratégia:</strong> <?php echo $program->estrategiaDesignacao ?></p>
                                                <p><strong>Dotação:</strong> <?php echo $formatter->formatCurrency($program->dotacao, 'EUR'), PHP_EOL; ?></p>
                                                <p><hr></p>
                                                <p>
                                                    <input data-onstyle="success" data-offstyle="danger" data-style="quick" type="checkbox" name="programs[<?php echo $program->id ?>][status]" <?php if ($program->status === 'active'): ?>checked<?php endif?> data-toggle="toggle">
                                                </p>
                                                <p><hr></p>
                                                <p><strong>Segmentação</strong></p>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'districts')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="districts" value="districts" checked <?php if ($program->segmentation_type === 'districts'): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="district">Distrito</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'cities')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="cities" value="cities" <?php if ($program->segmentation_type === 'cities'): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="concelho">Concelho</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_1')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="nut_1" value="nut_1" <?php if ($program->segmentation_type === 'nut_1'): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="nut_1">NUT 1</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_2')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="nut_2" value="nut_2" <?php if ($program->segmentation_type === 'nut_2'): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="nut_2">NUT 2</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'nut_3')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="nut_3" value="nut_3" <?php if ($program->segmentation_type === 'nut_3'): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="nut_3">NUT 3</label>
                                                </div>
                                                <div class="form-check form-check-inline">
                                                    <input class="form-check-input" onclick="showSegments(<?php echo $program->id ?>, 'cims')" type="radio" name="programs[<?php echo $program->id ?>][filterType]" id="cims" value="cims" <?php if ($program->cim != 0): ?>checked<?php endif?>>
                                                    <label class="form-check-label" for="cims">CIM</label>
                                                </div>
                                                <p><hr></p>
                                                <div class="radio-segmentation" data-programid="<?php echo $program->id ?>" data-type="<?php echo $program->segmentation_type ?>" data-cim="<?php echo (int) $program->cim ?>">
                                                    <select name="districts[<?php echo $program->id ?>][]" multiple class="sel-districts d-none form-control districts<?php echo $program->id ?>" style="height: 300px;">
                                                        <?php foreach ($districts as $district): ?>
                                                            <option <?php if ($program->segmentation_type === 'districts' && in_array($district->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $district->id ?>"><?php echo $district->name ?></option>
                                                        <?php endforeach?>
                                                    </select>
                                                    <select name="cities[<?php echo $program->id ?>][]" multiple class="sel-cities d-none form-control cities<?php echo $program->id ?>" style="height: 300px;">
                                                        <?php foreach ($districts as $district): ?>
                                                            <optgroup label="<?php echo $district->name ?>">
                                                            <?php foreach ($district->cities as $city): ?>
                                                                <option <?php if ($program->segmentation_type === 'cities' && in_array($city->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $city->id ?>"><?php echo $city->name ?></option>
                                                            <?php endforeach?>
                                                        </optgroup>
                                                        <?php endforeach?>
                                                    </select>
                                                    <select name="nut_1[<?php echo $program->id ?>][]" multiple class="sel-nut_1 d-none form-control nut_1<?php echo $program->id ?>" style="height: 300px;">
                                                        <?php foreach ($nuts1 as $nut1): ?>
                                                            <option <?php if ($program->segmentation_type === 'nut_1' && in_array($nut1->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut1->nut ?>"><?php echo $nut1->nut ?></option>
                                                        <?php endforeach?>
                                                    </select>
                                                    <select name="nut_2[<?php echo $program->id ?>][]" multiple class="sel-nut_2 d-none form-control nut_2<?php echo $program->id ?>" style="height: 200px;">
                                                        <?php foreach ($nuts2 as $nut2): ?>
                                                            <option <?php if ($program->segmentation_type === 'nut_2' && in_array($nut2->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut2->nut ?>"><?php echo $nut2->nut ?></option>
                                                        <?php endforeach?>
                                                    </select>
                                                    <select name="nut_3[<?php echo $program->id ?>][]" multiple class="sel-nut_3 d-none form-control nut_3<?php echo $program->id ?>" style="height: 100px;">
                                                        <?php foreach ($nuts3 as $nut3): ?>
                                                            <option <?php if ($program->segmentation_type === 'nut_3' && in_array($nut3->nut, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut3->nut ?>"><?php echo $nut3->nut ?></option>
                                                        <?php endforeach?>
                                                    </select>
                                                    <select name="cims[<?php echo $program->id ?>][]" multiple class="sel-cims d-none form-control cims<?php echo $program->id ?>" style="height: 300px;">
                                                        <?php foreach ($cims as $cim): ?>
                                                            <option <?php if ($program->cim && in_array($cim->id, $program->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $cim->id ?>"><?php echo $cim->name ?></option>
                                                        <?php endforeach?>
                                                    </select>
                                                </div>
                                                <p><hr></p>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="thematics">Temáticas</label>
                                                            <select name="programs[<?php echo $program->id ?>][thematics][]" class="form-control" multiple>
                                                                <?php foreach ($thematics as $thematic): ?>
                                                                    <option <?php if (in_array($thematic->id, $program->thematics)): ?>selected<?php endif?> value="<?php echo $thematic->id ?>"><?php echo $thematic->name ?></option>
                                                                <?php endforeach?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="types">Tipo de Entidade</label>
                                                            <select name="programs[<?php echo $program->id ?>][types][]" class="form-control" multiple>
                                                                <?php foreach ($types as $type): ?>
                                                                    <option <?php if (in_array($type->id, $program->types)): ?>selected<?php endif?> value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
                                                                <?php endforeach?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label for="sectors">Setores</label>
                                                            <select name="programs[<?php echo $program->id ?>][sectors][]" class="form-control" multiple>
                                                                <?php foreach ($sectors as $sector): ?>
                                                                    <option <?php if (in_array($sector->id, $program->sectors)): ?>selected<?php endif?> value="<?php echo $sector->id ?>"><?php echo $sector->name ?></option>
                                                                <?php endforeach?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach?>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 table-responsive">
                            <p class="lead">Documentos Anexos</p>
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Designação</th>
                                        <th>Nome</th>
                                        <th>Disponibilidade <br/>Indexado</th>
                                        <th>Status</th>
                                        <th class="text-right"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($documents as $document): ?>
                                        <tr>
                                            <td>
                                                <?php echo view_cell('\Admin\Cells\NoticeCell::download', ['document' => $document]) ?>
                                            </td>
                                            <td><?php echo $document->tipoDocumentoDesignacao ?></td>
                                            <td>
                                                <?php echo view_cell('\Admin\Cells\NoticeCell::local', ['local' => $document->local_document]) ?> <br/>
                                                <?php echo view_cell('\Admin\Cells\NoticeCell::parsed', ['parsed' => $document->parsed]) ?>
                                            </td>
                                            <td>
                                                <input data-onstyle="success" data-offstyle="danger" data-style="quick" type="checkbox" name="documents[<?php echo $document->id ?>]" <?php if ($document->status === 'active'): ?>checked<?php endif?> data-toggle="toggle">
                                            </td>
                                            <td class="text-right">
                                                <div class="btn-group" role="group">
                                                    <a target="_blank" href="<?php echo site_url('notices/national/documents/update/' . $document->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Editar">
                                                        <i class="fa-duotone fa-pen-to-square"></i>
                                                    </a>
                                                    <a href="<?php echo site_url('notices/national/documents/texts/' . $document->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Textos indexados">
                                                        <i class="fa-duotone fa-file-lines"></i>
                                                    </a>
                                                    <a onclick="return confirmDelete();" href="<?php echo site_url('notices/national/documents/delete/' . $document->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Apagar">
                                                        <i class="fa-duotone fa-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <p><hr></p>
                        </div>
                        <div class="col-10">
                            <p>
                                <input data-onstyle="success" data-offstyle="danger" data-style="quick" type="checkbox" name="notice[<?php echo $notice->id ?>]" <?php if ($notice->status === 'active'): ?>checked<?php endif?> data-toggle="toggle">
                            </p>
                        </div>
                        <div class="col-2 text-right">
                            <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php echo form_close() ?>
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
<link href="https://gitcdn.github.io/bootstrap-toggle/2.2.2/css/bootstrap-toggle.min.css" rel="stylesheet">
<script src="https://gitcdn.github.io/bootstrap-toggle/2.2.2/js/bootstrap-toggle.min.js"></script>
<script src="<?php echo base_url('dist/js/segmentation.js'); ?>" ></script>
<?php echo $this->endSection() ?>