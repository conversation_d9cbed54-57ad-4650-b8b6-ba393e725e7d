<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Setores</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('notices/national/programs/' . $program->notice_id) ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        Editar Setores
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('notices/national/programs/sectors/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="extra_id" value="<?php echo $program->id ?? null ?>">
                        <?php if (empty($sectors)): ?>
                            <div class="row">
                                <h5>Deve <a href="<?php echo site_url('settings/sectors/insert') ?>">criar Setores</a> para os poder atribuir a entidades</h5>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <div class="col-12">
                                    <?php foreach ($sectors as $sector): ?>
                                        <div class="form-check">
                                            <input <?php if (in_array($sector->id, $program->sectors)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="sectors[]" value="<?php echo $sector->id ?>" id="sector<?php echo $sector->id ?>">
                                            <label class="form-check-label" for="sector<?php echo $sector->id ?>"><?php echo $sector->name ?></label>
                                        </div>
                                    <?php endforeach?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                                </div>
                            </div>
                        <?php endif?>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>
