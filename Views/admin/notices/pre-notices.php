<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-12">
                <h1 class="m-0"><strong>Pré-avisos</strong> <small><a target="_blank" href="https://portaldosfundoseuropeus.pt">https://portaldosfundoseuropeus.pt</a></small></h1>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title text-bold">Informação indexada com informação de pré-avisos</h3>
                    </div>
                    <div class="card-body">
                        <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                            <thead>
                                <tr>
                                    <th>Titulo</th>
                                    <th>Tags</th>
                                    <th>Data de início</th>
                                    <th>Data de fim a</th>
                                    <th>Status</th>
                                    <th class="text-right"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($notices as $notice): ?>
                                    <tr>
                                        <td><?php echo word_limiter($notice->title, 5) ?></td>
                                        <td><?php echo $notice->tags ?></td>
                                        <td><?php echo $notice->start_date ?></td>
                                        <td><?php echo $notice->end_date ?></td>
                                        <td>
                                            <?php echo view_cell('\Admin\Cells\NoticeCell::status', ['status' => $notice->status]) ?>
                                        </td>
                                        <td class="text-right">
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo $notice->origin ?>" target="_blank" class="btn btn-primary"  data-toggle="tooltip" data-placement="top" title="Ver página">
                                                    <i class="fa-duotone fa-link"></i>
                                                </a>
                                                <a href="<?php echo site_url('notices/pre-notice/update/' . $notice->id) ?>" class="btn btn-primary" data-toggle="tooltip" data-placement="top" title="Editar">
                                                    <i class="fa-duotone fa-pen-to-square"></i>
                                                </a>
                                                <a onclick="return confirmDelete();" href="<?php echo site_url('notices/pre-notice/delete/' . $notice->id) ?>"  data-toggle="tooltip" data-placement="top" title="Apagar" class="btn btn-primary">
                                                    <i class="fa-duotone fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach?>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable();
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>