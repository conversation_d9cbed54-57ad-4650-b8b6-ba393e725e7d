<?php echo $this->extend('layouts/admin') ?>

<?php echo $this->section('content') ?>
    <div class="container-fluid">
        <div class="row mb-2 align-items-center">
            <div class="col-sm-6">
                <h1 class="m-0">utilizador</h1>
            </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('companies/employees/' . $companyId) ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                      <?php if (empty($employee->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> utilizador
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('companies/employees/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $employee->id ?? null ?>">
                        <input type="hidden" name="user_detail_id" value="<?php echo $employee->userDetailId ?? null ?>">
                        <input type="hidden" name="company_id" value="<?php echo $companyId ?>">
                        <h5 class="mb-3"><b>Dados de acesso</b></h5>
                        <div class="row mb-3">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">E-mail</label>
                                    <input type="email" name="email" class="form-control" aria-describedby="email" placeholder="Introduza o e-mail" value="<?php echo set_value('email', $employee->email ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" name="password" class="form-control" minlength="8" placeholder="Introduza a password" <?php if (empty($employee->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Confirmar Password</label>
                                    <input type="password" name="password_confirm" class="form-control" minlength="8" placeholder="Confirmar a password" <?php if (empty($employee->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>