<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2">
        <div class="col-sm-12">
            <h1 class="m-0">Release Log</h1>
            <?php $log = "
<h4>0.2.70</h4>
-<strong>Joao Pires      Mon Feb 17 </strong>  Ao marcar interesse na app dre, se o user tiver perdido o login redireciona para a página de login


<h4>0.2.69</h4>
-<strong>Joao Pires      Thu Feb 13 </strong>  Mudar comando de alerta de datas do dre para enviar emails individuais para os interessados
-<strong>Joao Pires      Thu Feb 13 </strong>  Mudar mensagem de marcar interesse por email se concurso estiver revogado
-<strong>Joao Pires      Wed Feb 12 </strong>  Adicionar estado revogado aos concursos e desativar notificações dos mesmos

<h4>0.2.66</h4>
-<strong>Joao Pires      Fri Jan 31 </strong>  Adicionar página de detalhe pública e botão de copiar link para cada concurso na tabela
-<strong>Joao Pires      Fri Jan 31 </strong>  Enviar email de detalhe de concurso ao marcar interesse se este já tivesse sido notificado aos interessados
-<strong>Joao Pires      Fri Jan 31 </strong>  Corrigir notificação de logs quando o ficheiro dos logs não existe
-<strong>Marco Monteiro  Thu Jan 30 </strong>  Alterar label informativa
-<strong>Marco Monteiro  Thu Jan 30 </strong>  Alterar o max nos uploads para 40MB
-<strong>Marco Monteiro  Tue Dec 31 </strong>  Fix logs - Baixar o nivel de criticidade do log
-<strong>Marco Monteiro  Tue Dec 31 </strong>  Fix logs: CRITICAL - 2024-12-11 03:17:02 --> Attempt to read property 'publicationDate' on null
-<strong>Marco Monteiro  Tue Dec 31 </strong>  Fix logs: CRITICAL - 2024-12-08 05:01:02 --> json_decode(): Argument #1 ($ json) must be of type string, array given
-<strong>Marco Monteiro  Tue Dec 31 </strong>  Fix logs: CRITICAL - 2024-12-05 16:48:18 --> Undefined array key 'dre_item_id'

<h4>0.2.57</h4>
-<strong>Joao Pires      Fri Dec 20</strong>  Passar links do dre a não obrigatórios no insert

<h4>0.2.56</h4>
-<strong>Joao Pires      Thu Dec 12</strong>  Adicionar pagina de insert para os concursos dre
-<strong>Marco Monteiro  Thu Dec 5</strong>   Passar a ser a vania a gerir o radar

<h4>0.2.54</h4>
-<strong>Marco Monteiro  Tue Dec 3 </strong>   ooops
-<strong>Marco Monteiro  Tue Dec 3 </strong>   Poder remover interesse diretamente a partir do email
-<strong>Marco Monteiro  Tue Dec 3 </strong>   Remover dependencia n usada + padding nas linhas para o botão n ficar desconfigurado
-<strong>Marco Monteiro  Tue Dec 3 </strong>   small fixes from logs
-<strong>Marco Monteiro  Mon Nov 25 </strong>  Remover migracao repetida

<h4>0.2.49</h4>
-<strong>Joao Pires      Fri Nov 15</strong>  Adicionar Bárbara e João Pires aos emails dos logs
-<strong>Joao Pires      Thu Nov 14</strong>  Melhoria no layout do email das datas dos concursos dre

<h4>0.2.47</h4>
-<strong>Joao Pires      Wed Nov 13</strong>  Validação de tamanho de anexos e função de download de anexos
-<strong>Joao Pires      Tue Nov 12</strong>  Colocar logs de emergency para info dos comandos do basegov

<h4>0.2.45</h4>
-<strong>Joao Pires      Tue Nov 5</strong>   Correção no modelo dos dre items na função company data

<h4>0.2.44</h4>
-<strong>Joao Pires      Wed Oct 30</strong>  Alterar comando de fechar consursos dre para a data de proposta para o dia anterior

<h4>0.2.43</h4>
-<strong>Joao Pires      Wed Oct 30</strong>  Adcionar campo de agrupamento
-<strong>Joao Pires      Wed Oct 30</strong>  Adicionar filtros para a data limite de esclarecimentos
-<strong>Joao Pires      Wed Oct 30</strong>  Adicionar e-mails dos interessados no envio de email dos detalhes
-<strong>Joao Pires      Wed Oct 30</strong>  Novo campo que indica quais as empresas que vão avançar num concurso dre
-<strong>Joao Pires      Wed Oct 30</strong>  Migrações e modelos para as tabela de empresa e relação empresa concurso dre
-<strong>Joao Pires      Tue Oct 29</strong>  Melhorias no layout do botões dos emails das datas e avisos dre
-<strong>Joao Pires      Tue Oct 29</strong>  Comando para colocar concurso com estado fechado se passar data de proposta
-<strong>Joao Pires      Mon Oct 28</strong>  Mudar a coluna do estado para decisão de avançar no tabelas dos concursos dre
-<strong>Joao Pires      Mon Oct 28</strong>  Acrescentar coluna de data limite de esclarecimentos à tabela dos concursos dre

<h4>0.2.34</h4>
-<strong>Joao Pires      Mon Oct 28</strong>  Colocar email de AH se Ana Torres coloca interesse num concurso

<h4>0.2.33</h4>
-<strong>Joao Pires      Mon Oct 28</strong>  Adicionar e-mail de interesse a partir do botão da tabela de concursos
-<strong>Joao Pires      Mon Oct 28</strong>  Adicionar emails de interessados no alerta das datas do dre
-<strong>Joao Pires      Fri Oct 25</strong>  Atualizar email de detalhe do concurso dre com o nome do projeto
-<strong>Joao Pires      Fri Oct 25</strong>  Adicionar nome de projeto à tabela dre_items e melhoria no layout do email da datas dre
-<strong>Joao Pires      Fri Oct 25</strong>  Adicionar emails para envio no tooltip de envio de email do concurso dre
-<strong>Joao Pires      Fri Oct 25</strong>  Adicionar campo de emails de interesse no formulário dos concursos dre
-<strong>Joao Pires      Fri Oct 25</strong>  Registar email de interesse como AH se a Ana Torres marcar interesse num concurso dre
-<strong>Joao Pires      Fri Oct 25</strong>  Adicionar campo para gravar emails de interesse e implementar o respetivo registo
-<strong>Joao Pires      Thu Oct 24</strong>  Acrecentar email da pessoa que recebe o alerta dre no botão mostrar interesse
-<strong>Joao Pires      Thu Oct 24</strong>  Alterar lógica de envio de e-mail dos alertas do dre para enviar individualmente para os destinatários
-<strong>Joao Pires      Thu Oct 24</strong>  Alterar estilos no email de alerta do dre

<h4>0.2.22</h4>
- <strong>Joao Pires      Wed Oct 23</strong>  Adicionar pasta users nos uploads
- <strong>Joao Pires      Wed Oct 23</strong>  Correção git ignore para os uploads
- <strong>Joao Pires      Wed Oct 23</strong>  Pasta uploads dre

<h4>0.2.19</h4>
- <strong>Joao Pires      Tue Oct 22</strong>  Melhorar layout do email das alertas dre
- <strong>Joao Pires      Tue Oct 22</strong>  Ordenar concursos pela data mais recente


<h4>0.2.17</h4>
- <strong>Joao Pires      Tue Oct 22</strong>  Correção tooltip para enviar email

<h4>0.2.16</h4>
- <strong>Joao Pires      Tue Oct 22</strong>  Adicionar comando para notificar de limites de datas dos concursos

<h4>0.2.15</h4>
- <strong>Joao Pires      Fri Oct 18</strong>  Confirmação no botão de envio de email
- <strong>Joao Pires      Fri Oct 18</strong>  Botão para envio de email com detalhes de um concurso dre
- <strong>Joao Pires      Thu Oct 17</strong>  Possibilidade de marcar interesse num concurso dre e partir do email de notificação
- <strong>Joao Pires      Tue Oct 15</strong>  Melhoria logica de filtragem de concursos dre no respetivo comando
- <strong>Joao Pires      Mon Oct 14</strong>  Possibilidade de adicionar anexos aos concursos dre
- <strong>Joao Pires      Fri Oct 11</strong>  Botão para mostrar interesse num concurso diretamente das tabelas
- <strong>Joao Pires      Thu Oct 10</strong>  Adicionar datatable, filtro por mês atual por defeito nos outros e excluídos, melhoria lógica do filtro excluídos
- <strong>Joao Pires      Thu Oct 10</strong>  Adicionar novas tabs para anuncios com interesse e outros, acrescentar colunas e filtros na tabela
- <strong>Joao Pires      Tue Oct 8</strong>   Adicionar campos conforme excel dos concursos dre e gestão dos mesmos


<h4>0.2.6</h4>
- <strong>Joao Pires      Wed Oct 2</strong>  Adicionar helper para filtragem de paravras excluídas
- <strong>Joao Pires      Wed Oct 2</strong>  Listagem de concursos e respetivo detalhe das mesmas, alteração na lógica de filtragem de concursos
- <strong>Joao Pires      Mon Sep 30</strong> Nova configuração para os emails do dre e respetivo crud
- <strong>Joao Pires      Mon Sep 30</strong> Renomear namaspace para DreApp, library para os cruds dos settings, crud para dicionário de exclusão
- <strong>Joao Pires      Mon Sep 23</strong> Criar novo namespace para o Dre e os repetivos ficheiros e urls para aceder ao mesmo
- <strong>Marco Monteiro  Mon Sep 9</strong>  Adicionar rita aos emails

<h4>0.1.294</h4>
- <strong>Marco Monteiro  Mon Apr 29</strong>  re ordernar os resultados do dre
- <strong>Marco Monteiro  Mon Apr 29</strong>  Correcção para dar erro de timeout na conexao ao basegov
- <strong>Marco Monteiro  Thu Apr 18</strong>  Fix envio de email para ter assuntos diferentes e para existir forma de distinguir
- <strong>Marco Monteiro  Wed Apr 17</strong>  Poder enviar ou todos os items do dia, ou excluir items com as palavras que n interessam
- <strong>Marco Monteiro  Fri Apr 12</strong>  Melhorar feedback dos comandos dre
- <strong>Marco Monteiro  Fri Apr 12</strong>  correcao commando de listagem de palavras
- <strong>Marco Monteiro  Fri Apr 12</strong>  Gerir palavras de exclusão do sistema de alerta do DRE

<h4>0.1.290</h4>
- <strong>Marco Monteiro  Fri Apr 12</strong>  Fix merge
- <strong>Marco Monteiro  Fri Apr 12</strong>  Enviar alertas de info sincronizada do diario da republica
- <strong>Marco Monteiro  Fri Apr 12</strong>  Uniformizar mensagens nos comandos
- <strong>Marco Monteiro  Thu Apr 11</strong>  Sincronizar RSS do diário da republica
- <strong>Marco Monteiro  Wed Apr 10</strong>  CRITICAL - 2024-04-09 00:27:12 --> Failed to parse JSON: Idle timeout reached
- <strong>Marco Monteiro  Wed Apr 10</strong>  CRITICAL - 2024-04-07 12:45:01 --> Undefined array key emails
- <strong>Marco Monteiro  Wed Apr 10</strong>  Colocar try catch nas tentativas de ir buscar informacao para quando falharem dar apenas um erro no cli
- <strong>Marco Monteiro  Wed Apr 10</strong>  Fix logs de falha na recuperacao, ficava alguns com falha a fazer ate ao infinito
- <strong>Natali Lucas    Thu Mar 28</strong>  adicionado o exit na funcao run basegovrecover
- <strong>Natali Lucas    Fri Mar 15</strong>  Update na funcao para apagar virgula, pontos e tracos do final do ultimo email antes de salvar db

<h4>0.1.275</h4>
- Natali  Wed Mar 13</strong>  fix-log cleanUpEmail
- Natali  Wed Mar 13</strong>  fix-log-alerts

<h4>0.1.273</h4>
- <strong>Marco Monteiro  Mon Mar 11</strong>  Corrigir para nos alertas não sairem informações já fora do prazo que tenham sido sincronizadas
- <strong>Marco Monteiro  Mon Mar 11</strong>  Resolver problema de sincronizacao
- <strong>Marco Monteiro  Wed Mar 6</strong>   Correcoes nos logs quando n ha resposta do basegov

<h4>0.1.270</h4>
- <strong>Marco Monteiro  Wed Feb 28</strong>  Recover all the ones that were failed
- <strong>Marco Monteiro  Fri Feb 23</strong>  Sistema de registo de falhados
- <strong>Marco Monteiro  Fri Feb 23</strong>  Criar sistema de tentativa até um máximo de 3 vezes com os mesmos valores
- <strong>Marco Monteiro  Fri Feb 23</strong>  Correcção nos anuncios / cpvs

<h4>0.1.265</h4>
- <strong>Marco Monteiro  Thu Feb 22</strong>  Enviar alertas criados dos contratos
- <strong>Marco Monteiro  Thu Feb 22</strong>  Poder sincronizar também NIFs das listagens que sejam adicionadas
- <strong>Marco Monteiro  Thu Feb 22</strong>  Criar mecanismos para sincronizar nifs das listas nos contratos
- <strong>Marco Monteiro  Thu Feb 22</strong>  CRUD alertas associados a contratos
- <strong>Marco Monteiro  Thu Feb 22</strong>  mostrar apenas os do tipo anuncios
- <strong>Marco Monteiro  Thu Feb 22</strong>  Adicionar tipo e list associado à criacao de um alerta
- <strong>Marco Monteiro  Thu Feb 22</strong>  retirar contagem de rows, n é precisa
- <strong>Marco Monteiro  Thu Feb 22</strong>  Importar ficheiros csv com nifs
- <strong>Marco Monteiro  Wed Feb 21</strong>  CRUD entidades associadas a listagens
- <strong>Marco Monteiro  Wed Feb 21</strong>  basegov - CRUD listagem de entidades
- <strong>Marco Monteiro  Wed Feb 21</strong>  mostrar resultados nas tabelas
- <strong>Marco Monteiro  Tue Feb 20</strong>  Enviar notificacao dos logs por email

<h4>0.1.250</h4>
- <strong>Marco Monteiro  Tue Feb 20</strong>  Enviar notificação da contratacao publica do dia anterior ou uma data em particular
- <strong>Marco Monteiro  Tue Feb 20</strong>  Passar sistema de pesquisa para contratos
- <strong>Marco Monteiro  Tue Feb 20</strong>  Select tem que ser por query, n podem ser concatenados
- <strong>Marco Monteiro  Mon Feb 19</strong>  Passar a ter um alerta geral principal que recebe todos os avisos
- <strong>Marco Monteiro  Mon Feb 19</strong>  Fix view para n estar muita informação encavalitada
- <strong>Marco Monteiro  Fri Feb 16</strong>  Resolver problema na query que devia devolver o arquivo
- <strong>Marco Monteiro  Fri Feb 16</strong>  Links publics no novo sub-dominio e namespace
- <strong>Marco Monteiro  Fri Feb 16</strong>  Pesquisa por tags (palavras com diferentes likes) + pesquisa comulativa
- <strong>Marco Monteiro  Fri Feb 16</strong>  Criar namespace proprio para basegov e segmentar login para poder ser com o mesmo user mas ver coisas diferentes com base no subdominio em que esteja a trabalhar

<h4>0.1.237</h4>
- <strong>Marco  Thu Feb 15</strong>  Erro na filtragem quando se muda de pagina
- <strong>Marco  Fri Feb 9</strong>   Sistema de tracking de logs para não ter que andar sempre a entrar no servidor para monitorizar
- <strong>Marco  Tue Feb 6</strong>   Fix timestams
- <strong>Marco  Thu Feb 1</strong>   definir return
- <strong>Marco  Thu Feb 1</strong>   Remover ficheiros que tinham ficado e indentacao
- <strong>Marco  Thu Feb 1</strong>   Exportar pesquisas do base.gov

<h4>0.1.232-ALPHA</h4>
- <strong>Marco Monteiro  Thu Feb 1</strong>   Filtrar contratos
- <strong>Marco Monteiro  Wed Jan 31</strong>  icones na paginacao
- <strong>Marco Monteiro  Wed Jan 31</strong>  Remover datatables da listagem de contratos e passar a paginacao normal
- <strong>Marco Monteiro  Tue Jan 30</strong>  Limpar filtro a mais
- <strong>Marco Monteiro  Tue Jan 30</strong>  Share nos detalhes dos avisos
- <strong>Marco Monteiro  Tue Jan 30</strong>  pre avisos n deixamos partilhar
- <strong>Marco Monteiro  Tue Jan 30</strong>  Botoes de partilhar nos detalhes dos avisos
- <strong>Marco Monteiro  Tue Jan 30</strong>  Poder ver o detalhe dos avisos sem fazer login com funcionalidades reduzidas
- <strong>Marco Monteiro  Tue Jan 30</strong>  Adicionar slugs a todos os detalhes
- <strong>Marco Monteiro  Tue Jan 30</strong>  General fixes na app
- <strong>Marco Monteiro  Tue Jan 30</strong>  Ver bookmarks das diferentes areas / tabelas
- <strong>Marco Monteiro  Tue Jan 30</strong>  Resolver problema de tags vazias + intervalos de tempo
- <strong>Marco Monteiro  Mon Jan 29</strong>  Limpeza de codigo
- <strong>Marco Monteiro  Mon Jan 29</strong>  Adicionar e remover bookmarks nos avisos europeus
- <strong>Marco Monteiro  Mon Jan 29</strong>  Adicionar e remover bookmarks no PRR
- <strong>Marco Monteiro  Mon Jan 29</strong>  Remover possibilidade de dois tipos de visualização, ficam so cards
- <strong>Marco Monteiro  Mon Jan 29</strong>  Adicionar icone em todas as listagens atraves de um dos setores associados
- <strong>Marco Monteiro  Mon Jan 29</strong>  Fix faltava fazer unset ao archive
- <strong>Marco Monteiro  Mon Jan 29</strong>  Fix na filtragem por alertas definidos
- <strong>Marco Monteiro  Mon Jan 29</strong>  Podes pesquisar pelos alertas custumizados
- <strong>Marco Monteiro  Mon Jan 29</strong>  Remover class de todas as tabelas para n passem dos limites da pagina
- <strong>Marco Monteiro  Mon Jan 29</strong>  Poder filtrar por datas de publicação
- <strong>Marco Monteiro  Mon Jan 29</strong>  Re-estruturar + filtro por procedimento
- <strong>Marco Monteiro  Mon Jan 29</strong>  Filtrar por cpv
- <strong>Marco Monteiro  Mon Jan 29</strong>  Poder filtrar por arquivo ou ativos pela sua data
- <strong>Marco Monteiro  Wed Jan 24</strong>  Fix merge request
- <strong>Marco Monteiro  Wed Jan 24</strong>  PRR detalhe + PRR exportar
- <strong>Marco Monteiro  Mon Jan 22</strong>  Pesquisa de avisos no PRR
- <strong>Marco Monteiro  Mon Jan 22</strong>  Fix merge request
- <strong>Marco Monteiro  Mon Jan 22</strong>  problema timezones + criar pagina publica para poder ver os criterios de adjucacao sem ter que fazer login
- <strong>Marco Monteiro  Mon Jan 22</strong>  Colocar forma poder ver logs em caso de erro para melhor debugging online
- <strong>Marco Monteiro  Mon Jan 22</strong>  Mesmo que de uma exception com um documento grande demais vamos passar ao seguinte e descartar o que falhou
- <strong>Marco Monteiro  Fri Jan 19</strong>  Faltava um br
- <strong>Marco Monteiro  Fri Jan 19</strong>  Melhorar email enviado e limpar texto que vem do pdf para n trazer o titulo

<h4>0.1.196-ALPHA</h4>
- <strong>Marco  Fri Jan 19</strong>  Enviar os alertas com os criterios de adjudicacao
- <strong>Marco  Fri Jan 19</strong>  Reordenar a busca para fazer os mais recentes primeiro
- <strong>Marco  Fri Jan 19</strong>  Fix merge request anterior
- <strong>Marco  Thu Jan 18</strong>  Ir buscar documentos do diario da republica e ler os documentos
- <strong>Marco  Thu Jan 18</strong>  renomear labels dos campos do prr
- <strong>Marco  Thu Jan 18</strong>  Resolver problema de callbacks adicionados na funcao que borregavam o sincronismo
- <strong>Marco  Thu Jan 18</strong>  Poder ver o PRR com e sem arquivo

<h4>0.1.188-ALPHA</h4>
- <strong>Marco  Tue Jan 16</strong>  Poder selecionar logo todos os cpv + definir data por defeito como ontem
- <strong>Marco  Tue Jan 16</strong>  Enviar alertas criados na app manualmente
- <strong>Marco  Thu Jan 11</strong>  Formatar data, tentar um formato se falhar tentar o seguinte, se falhar colocar vazia
- <strong>Marco  Thu Jan 11</strong>  Fix nos casos em que a data não se consegue ler

<h4>0.1.185-ALPHA</h4>
- <strong> Marco Thu Jan 11</strong>  Gestão de links no PRR
- <strong> Marco Thu Jan 11</strong>  Mostrar avisos do PRR na backoffice
- <strong> Marco Thu Jan 11</strong>  Sincronizar informação do site PRR
- <strong> Marco Thu Jan 11</strong>  Adicionar possibilidade de verificar novos links de PRR
- <strong> Marco Thu Jan 11</strong>  Fix alerts
- <strong> Marco Wed Jan 10</strong>  Mostrar detalhe de avisos europeus
- <strong> Marco Wed Jan 10</strong>  Mostrar avisos europeus e poder filtrar na app


<h4>0.1.178-ALPHA</h4>
- <strong>Marco Wed Jan 10</strong>  Adicionar campo codigo aos avisos europeus
- <strong>Marco Wed Jan 10</strong>  Adicionar nome aos avisos europeus
- <strong>Marco Wed Jan 10</strong>  Ou pesquisa por cidades /distritos ou pelas cims
- <strong>Marco Wed Jan 10</strong>  announcement em vez contract
- <strong>Marco Wed Jan 10</strong>  Fix nome do programa n aparece na listagem de avisos europeus e form de gestao
- <strong>Marco Wed Jan 10</strong>  Não deixar abrir o detalhe se o anuncio ou contrato ainda n sincronizou na totalidade
- <strong>Marco Wed Jan 10</strong>  Fix no menu de anuncios + fix nos breadcrumbs dos anuncios
- <strong>Marco Tue Jan 9</strong>   Apenas status new
- <strong>Marco Tue Jan 9</strong>   Fix get announcements

<h4>0.1.169-ALPHA</h4>
- <strong>Marco  Fri Jan 5</strong>   Corrigir views
- <strong>Marco  Fri Jan 5</strong>   Criar alertas para depois enviar emails automáticos com base nestes alertas
- <strong>Marco  Fri Jan 5</strong>   correcao migracao
- <strong>Marco  Fri Jan 5</strong>   Fix merge
- <strong>Marco  Fri Jan 5</strong>   CRUD cpvs
- <strong>Marco  Fri Jan 5</strong>   Sincronizar sempre a pagina 1 a cada vez que tentou a ultima
- <strong>Marco  Thu Jan 4</strong>   Mostrar detalhe dos anuncios
- <strong>Marco  Thu Jan 4</strong>   Sincronizar restante informação dos anuncios e as entidades envolvidas
- <strong>Marco  Thu Jan 4</strong>   Mostrar listagem de anuncios no backoffice
- <strong>Marco  Thu Jan 4</strong>   Fix typo
- <strong>Marco  Thu Jan 4</strong>   fix
- <strong>Marco  Thu Jan 4</strong>   Add group members para exclusao
- <strong>Marco  Thu Jan 4</strong>   Fix logs online em alguns que dao erro a fazer o sync
- <strong>Marco  Thu Jan 4</strong>   Fix criacao de programas sem organismos

<h4>0.1.154-ALPHA</h4>
- <strong> Marco  Wed Jan 3</strong>   deixar campos como not required
- <strong> Marco  Wed Jan 3</strong>   Bloquear a versao de ci no 4.4.3 por causa do save para ja
- <strong> Marco  Wed Jan 3</strong>   alterar funcao de sincronismo para ir buscar historico
- <strong> Marco  Wed Jan 3</strong>   Base gov sincronizar anuncios
- <strong> Marco  Wed Jan 3</strong>   correcao de insert/update aviso europeu
- <strong> Marco  Wed Jan 3</strong>   correcao de insert/update program

<h4>0.1.150-ALPHA</h4>
- <strong>Marco  Wed Jan 3</strong>   Fix no insert/update programas

<h4>0.1.149-ALPHA</h4>
- <strong>Marco  Wed Jan 3</strong>   Avisos europeus
- <strong>Marco  Wed Jan 3</strong>   Avisos na verdade são programas
- <strong>Marco  Wed Jan 3</strong>   Mudar icones dos avisos
- <strong>Marco  Wed Jan 3</strong>   Corrigir labels nos organismos
- <strong>Marco  Fri Dec 29</strong>  Mudança de estrutura de layout para layout horizontal

<h4>0.1.144-ALPHA</h4>
-<strong>Marco  Fri Dec 29</strong>  Fix merge request
-<strong>Marco  Fri Dec 29</strong>  CRUD municipios
-<strong>Marco  Fri Dec 29</strong>  CRUD juntas de freguesia
-<strong>Marco  Fri Dec 29</strong>  Mostrar detalhe de contrato e links para coisas no base.gov
-<strong>Marco  Thu Dec 28</strong>  Mostrar lista de contratos no backoffice

<h4>0.1.139-ALPHA</h4>
- <strong>Marco  Thu Dec 28</strong>  Sincronizar de base.gov a informação de detalhe

<h4>0.1.138-ALPHA</h4>
- <strong>Marco  Thu Dec 28</strong>  Sincronizar informação base do base.gov de freguesias, Municípios e cims
- <strong>Marco  Wed Dec 27</strong>  Modelos para trabalhar com ambos
- <strong>Marco  Wed Dec 27</strong>  Municípios e freguesias

<h4>0.1.35-ALPHA</h4>
- <strong>Marco  Wed Dec 27</strong>  Mostrar avisos europeus sem pesquisa para fazer release
- <strong>Marco  Tue Dec 26</strong>  Adicionar segmentacao aos avisos europeus criados
- <strong>Marco  Tue Dec 26</strong>  CRUD documentos associados a avisos europeus
- <strong>Marco  Tue Dec 26</strong>  Finalizacao de form de fundos europeus

<h4>0.1.31-ALPHA</h4>
- <strong>Marco   Fri Dec 22</strong>  remover espaçamentos
- <strong>Marco   Fri Dec 22</strong>  Mudar estrutura de edicao de empresa para a segmentacao estar direta no form de edicao
- <strong>nuno    Fri Dec 22</strong>  renomeação da exportação em pdf dos avisos nacionais
- <strong>nuno    Fri Dec 22</strong>  novos icons
- <strong>Marco   Fri Dec 22</strong>  Poder modificar os nomes dos documentos
- <strong>Marco   Fri Dec 22</strong>  Passar de tipologia para Tipo de Entidade
- <strong>Marco   Fri Dec 22</strong>  Colocar as contagens nas colunas das segmentacoes a funcionar
- <strong>Marco   Fri Dec 22</strong>  typo em descricao
- <strong>nuno    Thu Dec 21</strong>  pdf do aviso gerado

<h4>0.1.122-ALPHA</h4>
- <strong>Nuno    Thu Dec 21</strong>  icon hamburger da sidebar encostado à esquerda
- <strong>Nuno    Wed Dec 20</strong>  mudança no card links por indexar na dashboard do admin
- <strong>Nuno    Wed Dec 20</strong>  revisão responsivo app
- <strong>Marco  Wed Dec 20</strong>  Fix para poder limpar e continuar a ver os resultados
- <strong>Marco  Wed Dec 20</strong>  Redirecionar o user caso n exista qualquer tipo de filtragem para um tipo de filtragem por defeito se ele tiver
- <strong>Marco  Wed Dec 20</strong>  limpeza de serviço
- <strong>Marco  Wed Dec 20</strong>  Filtrar por tipologias
- <strong>Marco  Wed Dec 20</strong>  Filtrar por tematica
- <strong>Marco  Wed Dec 20</strong>  Filtrar por setor
- <strong>Marco  Wed Dec 20</strong>  Pesquisa por CIMS
- <strong>Marco  Wed Dec 20</strong>  Filtrar por cidades (dependente do filtro distrito)
- <strong>Marco  Wed Dec 20</strong>  Poder filtrar por distritos
- <strong>Nuno    Tue Dec 19</strong>  links cards
- <strong>Nuno    Tue Dec 19</strong>  cards na dashboard do admin
- <strong>Nuno    Tue Dec 19</strong>  melhorias nas charts
- <strong>Nuno    Mon Dec 18</strong>  menu da dashbord ativo
- <strong>Nuno    Mon Dec 18</strong>  remover numeros das labels dos graficos
- <strong>Nuno    Mon Dec 18</strong>  input date required para filtragem e datepickers
- <strong>Nuno    Mon Dec 18</strong>  alturas das bars dinâmicas e correção na filtragem das queries
- <strong>Nuno    Mon Dec 18</strong>  organizar queries das estatisticas nos respetivos modelos
- <strong>Marco  Mon Dec 18</strong>  Pedido de contactos a consultores definidos em backoffice nos avisos e pre-avisos
- <strong>Marco  Mon Dec 18</strong>  Gravar emails nos settings para poderem ser usados como contactos de consultores
- <strong>Nuno    Mon Dec 18</strong>  pre avisos por area e tag
- <strong>Marco  Mon Dec 18</strong>  Validar conta e enviar email para o user com info que ja pode aceder
- <strong>Nuno    Mon Dec 18</strong>  remoção do serviço Dashboard
- <strong>Nuno    Mon Dec 18</strong>  dashboard sem chart dos pré-avisos
- <strong>Marco  Thu Dec 14</strong>  Recuperar password
- <strong>Marco  Thu Dec 14</strong>  Enviar email de validacao de conta no ato de criação de conta
- <strong>Marco  Thu Dec 14</strong>  Comando para enviar notificacoes sobre informacao que foi sincronizada no dia anterior ou num data em particular
- <strong>Marco  Thu Dec 14</strong>  Service para envio de emails de forma facil e rapida
- <strong>Nuno    Thu Dec 14</strong>  criação de service dashboard
- <strong>Marco  Thu Dec 14</strong>  Poder filtrar pre-avisos na app
- <strong>Marco  Thu Dec 14</strong>  Mostrar pre-avisos na APP
- <strong>Marco  Thu Dec 14</strong>  Refactor avisos europeus para pre-avisos

<h4>0.1.86-ALPHA</h4>
- <strong>Marco  Wed Dec 13</strong>  Mostrar avisos divididos pela data de inicio e data de fim
- <strong>Marco  Wed Dec 13</strong>  Na app apenas mostrar avisos com data de fecho ainda por acontecer
- <strong>Marco  Wed Dec 13</strong>  Fix filtros vazios
- <strong>Nuno    Wed Dec 13</strong>  segmentação no form dos programas indexados
- <strong>Marco  Wed Dec 13</strong>  REmover filtros na pesquisa com um click
- <strong>Marco  Wed Dec 13</strong>  Paginacao nos notices + mostrar total de notices na lista
- <strong>Marco  Wed Dec 13</strong>  Fix merge request
- <strong>Nuno    Wed Dec 13</strong>  radio distritos selecionado por defeito quando não existe tipo de segmentação
- <strong>Marco  Wed Dec 13</strong>  Mostrar pagina de bookmarks na app
- <strong>Nuno    Wed Dec 13</strong>  botão voltar
- <strong>Nuno    Wed Dec 13</strong>  apenas upload de imagem
- <strong>Marco  Wed Dec 13</strong>  Adicionar e remover bookmarks direto na listagem
- <strong>Marco  Wed Dec 13</strong>  Re-indentar
- <strong>Marco  Wed Dec 13</strong>  No detalhe do editar do programa colocar a segmentacao nao geografica
- <strong>Marco  Wed Dec 13</strong>  Segmentar tipos, tematicas e setores direto nos programas
- <strong>Nuno    Wed Dec 13</strong>  imagem do tipo de aviso nos detalhes do program
- <strong>Nuno    Wed Dec 13</strong>  imagem do tipo de aviso nos programas

<h4>0.1.70-ALPHA</h4>
- <strong>Marco   Wed Dec 13</strong>  Fix typo
- <strong>Marco   Wed Dec 13</strong>  Adicionar target blanks aos icones de editar
- <strong>Marco   Wed Dec 13</strong>  Editar textos avisos
- <strong>Nuno    Wed Dec 13</strong>  alertas no layout
- <strong>Nuno    Wed Dec 13</strong>  gerir dados de perfil do lado do user
- <strong>Nuno    Tue Dec 12</strong>  correção no script da info e v up
- <strong>Marco   Tue Dec 12</strong>  Fix criação de contas

<h4>0.1.66-ALPHA</h4>
- <strong>Nuno  Tue Dec 12</strong>  Correção na segmentação quando não é selecionada nenhuma opção
- <strong>Nuno  Tue Dec 12</strong>  Correções após review do marco
- <strong>Nuno  Tue Dec 12</strong>  mudança de nome nas tabelas e correção de bug na segmentação
- <strong>Nuno  Tue Dec 12</strong>  CIMS a funcionar na segmentação

<h4>0.1.62-ALPHA</h4>
- <strong>Marco  Tue Dec 12</strong>  Colocar os filtros ja selecionados dps da filtragem
- <strong>Marco  Tue Dec 12</strong>  Pesquisa por nome do programa
- <strong>Marco  Tue Dec 12</strong>  Pesquisar por data limite de fim de aviso
- <strong>Marco  Tue Dec 12</strong>  Pesquisa aberta por string
- <strong>Marco  Tue Dec 12</strong>  Ordenar resultados por id ou por data de conclusao
- <strong>Marco  Tue Dec 12</strong>  Fix serviço Nuno
- <strong>Nuno   Tue Dec 12</strong>  mudança na forma de segmentar os tipos de segmentação
- <strong>Nuno   Tue Dec 12</strong>  serviço do lado do admin
- <strong>Nuno   Tue Dec 12</strong>  continuar segmentação dos programas seguintes se algum correr mal
- <strong>Nuno   Tue Dec 12</strong>  correções após review do marco
- <strong>Nuno   Tue Dec 12</strong>  comentarios das funções
- <strong>Nuno   Tue Dec 12</strong>  adicionar return se algo correr mal na segmentação
- <strong>Nuno   Tue Dec 12</strong>  radio a funcionar sem CIMS
- <strong>Marco  Mon Dec 11</strong>  Remover tender command
- <strong>Marco  Mon Dec 11</strong>  Ativar e inativar programas
- <strong>Marco  Mon Dec 11</strong>  Editar informação corretamente
- <strong>Nuno   Mon Dec 11</strong>  migracoes necessárias para segmentação de programas
- <strong>Marco  Wed Dec 6</strong>   Associar CIM a uma empresa
- <strong>Marco  Wed Dec 6</strong>   Estrutura CIMS + CRUD CIMS
- <strong>Marco  Wed Dec 6</strong>   Associar distrito, cidade e por consequencia ficam associadas as informacoes de NUTS bastando escolher a cidade no select
- <strong>Marco  Wed Dec 6</strong>   Tabela com concelhos + model + seeder
- <strong>Marco  Wed Dec 6</strong>   tabela de distritos + inserir informação dos mesmos
- <strong>Marco  Tue Dec 5</strong>   Associar setores e tipologias as empresas
- <strong>Nuno   Tue Dec 5</strong>   correções after review
- <strong>Nuno   Mon Dec 4</strong>   mostra botão limpar filtro apenas quando foi feita uma pesquisa
- <strong>Nuno   Mon Dec 4</strong>   filtragem de artigos
- <strong>Nuno   Mon Dec 4</strong>   model chamado no constructor
- <strong>Marco  Mon Dec 4</strong>   Associar tipologias e setores a avisos
- <strong>Marco  Mon Dec 4</strong>   CRUD sectors
- <strong>Nuno   Mon Dec 4</strong>   paginação de noticias
- <strong>Nuno   Mon Dec 4</strong>   remoção de código comentado na view e correção na ordenação ao ir buscar todas as noticias
- <strong>Nuno   Mon Dec 4</strong>   limitar carateres intro das noticias
- <strong>Nuno   Mon Dec 4</strong>   estilos card artigos
- <strong>Marco  Mon Dec 4</strong>   CRUD types
- <strong>Nuno   Mon Dec 4</strong>   show active articles where schedule date < now
- <strong>Nuno   Fri Dec 1</strong>   listagem de noticias na app e detalhes
- <strong>Nuno   Thu Nov 30</strong>  ignorar uploads
- <strong>Nuno   Thu Nov 30</strong>  remoção do service Article, ajustes nas views e alteração do tamanho máx da slug
- <strong>Nuno   Thu Nov 30</strong>  remoção de espaços e comentário
- <strong>Nuno   Thu Nov 30</strong>  correções após review
- <strong>Marco  Thu Nov 30</strong>  Mudar de segments para tematicas
- <strong>Marco  Thu Nov 30</strong>  Mudar de colaborador para utilizador
- <strong>Marco  Thu Nov 30</strong>  Mudar em todo o lado de empresas para entidades e utilizadores
- <strong>Nuno   Thu Nov 30</strong>  duvida ao gerar slug
- <strong>Nuno   Thu Nov 30</strong>  correção a partir do reporte do antónio
- <strong>Nuno   Wed Nov 29</strong>  crud noticias

<h4>0.1.17-ALPHA</h4>
- <strong>Marco Wed Nov 29 2023</strong>  Limpar detalho do aviso de features que não estão feitas, para inclusao incremental
- <strong>Marco Wed Nov 29 2023</strong>  Limpar coisas que ainda não estão feitas para partilhar app
- <strong>Marco Wed Nov 29 2023</strong>  Fix merge request
- <strong>Marco Wed Nov 29 2023</strong>  Associar segmentação a um programa
- <strong>Marco Wed Nov 29 2023</strong>  Associar segmentos a uma empresa/user details
- <strong>Marco Wed Nov 29 2023</strong>  CRUD segmentos e uniformização de botões e ações
- <strong>Marco Tue Nov 28 2023</strong>  modelos para gestao de segmentos
- <strong>Marco Tue Nov 28 2023</strong>  Migracoes para gestao de segmentos
- <strong>Marco Tue Nov 28 2023</strong>  Na listagem na app apenas mostrar programas ativos
- <strong>Marco Tue Nov 28 2023</strong>  Poder editar o estado nos programas
- <strong>Marco Tue Nov 28 2023</strong>  Adicionar campo de estado aos programas
- <strong>Nuno  Tue Nov 28 2023</strong>  correção nas rotas de autenticacao
- <strong>Marco Tue Nov 28 2023</strong>  Fix breadcrumbs na gestao de empresas
- <strong>Marco Tue Nov 28 2023</strong>  Remover duplicação de load de helpers
- <strong>Marco Tue Nov 28 2023</strong>  Erro no caminho de ficheiros de rotas
- <strong>Marco Tue Nov 28 2023</strong>  CRON JOBS documentados

<h4>0.1.5-ALPHA</h4>
- <strong>Nuno    Tue Nov 28 2023</strong>  Correção na criação do admin
- <strong>Marco   Tue Nov 28 2023</strong>  Fix new version of shield

<h4>0.1.4-ALPHA</h4>
- <strong>Nuno    Tue Nov 28 2023</strong>  correção nos returns
- <strong>Nuno    Tue Nov 28 2023</strong>  forgot/reset password admin
- <strong>Nuno    Mon Nov 27 2023</strong>  correção na chamada do model userdetail
- <strong>Nuno    Mon Nov 27 2023</strong>  rotas protegidas, verificação de dias q faltam para subscricao, n de utilizadores pendentes
- <strong>Marco   Mon Nov 27 2023</strong>  Adicionar dominios
- <strong>Marco   Mon Nov 27 2023</strong>  release 1st Alpha
- <strong>Marco   Mon Nov 27 2023</strong>  Trocar o dashboard por pagina de logs por agora
- <strong>Nuno    Mon Nov 27 2023</strong>  correções após revisão Marco
- <strong>Nuno    Mon Nov 27 2023</strong>  listagem de utilizadores pendentes (por aprovar)
- <strong>Nuno    Fri Nov 24 2023</strong>  token gerado para envio de email de confirmação de conta
- <strong>Nuno    Fri Nov 24 2023</strong>  correções após review e layout auth
- <strong>Nuno    Fri Nov 24 2023</strong>  merge com develop
- <strong>Nuno    Fri Nov 24 2023</strong>  recuperção da password sem envio de email
- <strong>Bárbara Thu Nov 23 2023</strong>  Ajuste css filtros, datepicker e multiselect
- <strong>Nuno    Thu Nov 23 2023</strong>  remoção do param 'user_session' no create do user
- <strong>Nuno    Thu Nov 23 2023</strong>  registo e login a funcionar
- <strong>Marco   Wed Nov 22 2023</strong>  Mostrar avisos na listagem ainda sem filtragem etc, apenas mostrar
- <strong>Nuno    Wed Nov 22 2023</strong>  bug formularios empresas/Utilizadores
- <strong>Marco   Wed Nov 22 2023</strong>  Mostrar informação no detalhe de um programa
- <strong>Bárbara Wed Nov 22 2023</strong>  Filtragens
- <strong>Bárbara Wed Nov 22 2023</strong>  Modal
- <strong>Nuno    Wed Nov 22 2023</strong>  urls juntos na app
- <strong>Nuno    Wed Nov 22 2023</strong>  fix app url
- <strong>Nuno    Wed Nov 22 2023</strong>  correções após review
- <strong>Nuno    Tue Nov 21 2023</strong>  correções após review
- <strong>Marco   Tue Nov 21 2023</strong>  Adicionar dotacoes e valores em dinheiro nos formatos corretos em eur
- <strong>Marco   Tue Nov 21 2023</strong>  Fazer bypass a validacao no update em bulk dos estados
- <strong>Nuno    Tue Nov 21 2023</strong>  Merge branch 'feature/notices-resume' into 'develop'
- <strong>Marco   Tue Nov 21 2023</strong>  Poder mudar estados direto na vista global
- <strong>Nuno    Mon Nov 20 2023</strong>  criação de um service user para não haver redundancia de codigo
- <strong>Nuno    Mon Nov 20 2023</strong>  correções nos returns
- <strong>Nuno    Mon Nov 20 2023</strong>  correções após review
- <strong>Marco   Mon Nov 20 2023</strong>  View com toda a informação sobre um aviso
- <strong>Nuno    Mon Nov 20 2023</strong>  app com urls certas
- <strong>Nuno    Mon Nov 20 2023</strong>  merge com develop
- <strong>Nuno    Mon Nov 20 2023</strong>  crud Utilizadores por empresa
- <strong>Marco   Mon Nov 20 2023</strong>  Encurtar colunas para a tabela n desconfigurar
- <strong>Marco   Mon Nov 20 2023</strong>  Fix dashboard e login a queixar-se de n existir breadcrumbs
- <strong>Marco   Mon Nov 20 2023</strong>  Limpeza + estuturar views
- <strong>Marco   Mon Nov 20 2023</strong>  Re estrutura dashboard views e controllers
- <strong>Nuno    Fri Nov 17 2023</strong>   breadcrumbs nas views das companies
- <strong>Nuno    Fri Nov 17 2023</strong>   alterações após review do marco
- <strong>Marco   Fri Nov 17 2023</strong>   Tooltips nos button groups
- <strong>Marco   Fri Nov 17 2023</strong>   Mover o layout do admin para o sitio correto, para dentro da pasta de layouts
- <strong>Marco   Fri Nov 17 2023</strong>   Re-indentar admin layout
- <strong>Marco   Fri Nov 17 2023</strong>   Sistema de breadcrumbs
- <strong>Nuno    Fri Nov 17 2023</strong>   alteração de username para email no login form do admin
- <strong>Nuno    Fri Nov 17 2023</strong>   Rotas de autenticação reformuladas com /auth/ e criação de filtro do lado do admin
- <strong>Nuno    Thu Nov 16 2023</strong>   CRUD Empresas
- <strong>Marco   Thu Nov 16 2023</strong>   Mensagem de confirmação sempre antes de apagar alguma coisa
- <strong>Marco   Thu Nov 16 2023</strong>   Ajustes layout breadcrumbs
- <strong>Marco   Thu Nov 16 2023</strong>   correcoes nos titulos dos varios ecrãs de gestao
- <strong>Marco   Thu Nov 16 2023</strong>   Add icon to logout with font awesome pro
- <strong>Marco   Thu Nov 16 2023</strong>   Adicionar menu de noticias + adicionar template de breadcrumbs
- <strong>Marco   Thu Nov 16 2023</strong>   Ajustar acoes de todas as tabelas para usar font awesome
- <strong>Marco   Thu Nov 16 2023</strong>   Instalar font awesome pro + active menu na navegacao do backend
- <strong>Marco   Thu Nov 16 2023</strong>   Fix merge request
- <strong>Marco   Thu Nov 16 2023</strong>   Re-indentar codigo
- <strong>Marco   Thu Nov 16 2023</strong>   Gerir programas de um aviso
- <strong>Marco   Thu Nov 16 2023</strong>   Gestao integral de documentos e textos associados
- <strong>Marco   Thu Nov 16 2023</strong>   Apagar e editar notices nacionais
- <strong>Marco   Thu Nov 16 2023</strong>   Mostrar avisos portugal2030
- <strong>Bárbara Thu Nov 16 2023</strong>   modal wip
- <strong>Marco   Wed Nov 15 2023</strong>   fix problema de não existencia de elementos no html
- <strong>Nuno    Wed Nov 15 2023</strong>   rotas organizadas
- <strong>Nuno    Wed Nov 15 2023</strong>   correções após review
- <strong>Bárbara Wed Nov 15 2023</strong>   Listagem de detalhe de avisos
- <strong>Nuno    Wed Nov 15 2023</strong>   merge com develop
- <strong>Nuno    Wed Nov 15 2023</strong>   protect dashboard
- <strong>Bárbara Wed Nov 15 2023</strong>   Layout dashboard
- <strong>Marco   Wed Nov 15 2023</strong>   Update european notice
- <strong>Nuno    Wed Nov 15 2023</strong>   commit antes de merge para correção das rotas
- <strong>Marco   Wed Nov 15 2023</strong>   Remover notices europeus
- <strong>Marco   Wed Nov 15 2023</strong>   Faltavam os allowed domains
- <strong>Nuno    Wed Nov 15 2023</strong>   autenticação do admin
- <strong>Marco   Tue Nov 14 2023</strong>   Poder ver a informação original no site de origem
- <strong>Marco   Tue Nov 14 2023</strong>   Show Europe links
- <strong>Marco   Tue Nov 14 2023</strong>   Mostrar lista de links a serem indexados no backoffice
- <strong>Marco   Tue Nov 14 2023</strong>   navegacao lateral com sub-dominio
- <strong>Nuno    Tue Nov 14 2023</strong>   fix layout apos instalacao de dependencias a partir do composer
- <strong>Marco   Tue Nov 14 2023</strong>   Navegacao com sub-links
- <strong>Marco   Tue Nov 14 2023</strong>   adicionar funcionalidade de injetar scripts e styles + colocar versoes no footer
- <strong>Marco   Tue Nov 14 2023</strong>   Merge branch 'feature/install-admin-layout' into 'develop'
- <strong>Marco   Tue Nov 14 2023</strong>   composer json com copy dos ficheiros do adminlte para o projeto sem que passem pelo git
- <strong>Marco   Tue Nov 14 2023</strong>   Remover do git os ficheiros admin
- <strong>Nuno    Tue Nov 14 2023</strong>   primeiro push com a dashboard
- <strong>Nuno    Mon Nov 13 2023</strong>   dashboard criada mas por desenhar
- <strong>Marco   Mon Nov 13 2023</strong>   parse documentos pdf dos avisos e guarda textos adicionais desses documentos. Apenas textos relevantes
- <strong>Marco   Mon Nov 13 2023</strong>   Fix merge request
- <strong>Marco   Mon Nov 13 2023</strong>   Comando para fazer download dos documentos indexados
- <strong>Marco   Fri Nov 10 2023</strong>   Crawler for national info, just needs the documents part
- <strong>Marco   Thu Nov 9 2023</strong>    Gravar informação de notices europeus de links https://portaldosfundoseuropeus.pt/project/
- <strong>Marco   Wed Nov 8 2023</strong>    Crawler para ir indexar os links dos sites de origem
- <strong>Marco   Tue Nov 7 2023</strong>    Merge branch 'feature/layout' into 'develop'
- <strong>Bárbara Tue Nov 7 2023</strong>    Layout base App login
- <strong>Marco   Tue Nov 7 2023</strong>    Lingua pt
- <strong>Marco   Tue Nov 7 2023</strong>    Estrutura base para admin + app
- <strong>Marco   Tue Nov 7 2023</strong>    Estrutura base + instalacao de shield
- <strong>Marco   Tue Nov 7 2023</strong>    Criar estrutura base
- <strong>Marco   Mon Nov 6 2023</strong>    Initial commit
            "?>
            <?php echo nl2br($log) ?>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>
