<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Organismos Nacionais</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/organisms') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($cim->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Organismos Nacionais
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/organisms/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $organism->id ?? null ?>">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="entity" class="form-label">Entidade</label>
                                    <input type="text" name="entity" class="form-control" aria-describedby="entity" value="<?php echo set_value('entity', $organism->entity ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="person" class="form-label">Responsável</label>
                                    <input type="text" name="person" maxlength="255" class="form-control" aria-describedby="person" value="<?php echo set_value('person', $organism->person ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email</label>
                                    <input type="email" name="email" class="form-control" aria-describedby="email" value="<?php echo set_value('email', $organism->email ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="phone" class="form-label">Telefone</label>
                                    <input type="text" name="phone" maxlength="255" class="form-control" aria-describedby="phone" value="<?php echo set_value('phone', $organism->phone ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="cellphone" class="form-label">Telemóvel</label>
                                    <input type="text" name="cellphone" class="form-control" aria-describedby="cellphone" value="<?php echo set_value('cellphone', $organism->cellphone ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="website" class="form-label">Website</label>
                                    <input type="text" name="website" class="form-control" aria-describedby="website" value="<?php echo set_value('website', $organism->website ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>