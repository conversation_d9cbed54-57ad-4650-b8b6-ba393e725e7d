<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Programas Europeus</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('notices/europe-programs/documents/' . $program->id) ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($document->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Documento
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open_multipart('notices/europe-programs/documents/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $document->id ?? null ?>">
                        <input type="hidden" name="notice_id" value="<?php echo $program->id ?? null ?>">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nome</label>
                                    <input type="text" name="name" class="form-control" aria-describedby="name" value="<?php echo set_value('name', $document->name ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="document" class="form-label">Documento</label>
                                    <?php if (!empty($document->local_document)): ?>
                                        <div>
                                            <a class="mr-2" href="<?php echo base_url('uploads/programs/documents/' . ($document->local_document)) ?>" target="_blank"><i class="fa-duotone fa-image mr-1"></i><?php echo $document->local_document ?></a>
                                        </div>
                                    <?php else: ?>
                                        <input type="file" class="form-control" name="document"/>
                                    <?php endif?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>