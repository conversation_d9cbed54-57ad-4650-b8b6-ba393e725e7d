<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Avisos Europeus</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('notices/europe-programs') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">Definir Segmentação</h3>
                </div>
                <div class="card-body">
                    <?php echo form_open_multipart('notices/europe-programs/segments/save') ?>
                        <input type="hidden" name="notice_id" value="<?php echo $program->id ?>">
                        <?php echo csrf_field() ?>
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between">
                                        <h3 class="card-title">
                                            Editar CIMS
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <?php foreach ($cims as $cim): ?>
                                                    <div class="form-check">
                                                        <input <?php if (in_array($cim->id, $program->cims)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="cims[]" value="<?php echo $cim->id ?>" id="cims<?php echo $cim->id ?>">
                                                        <label class="form-check-label" for="cims<?php echo $cim->id ?>"><?php echo $cim->name ?></label>
                                                    </div>
                                                <?php endforeach?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between">
                                        <h3 class="card-title">
                                            Editar Temáticas
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <?php if (empty($thematics)): ?>
                                            <div class="row">
                                                <h5>Deve <a href="<?php echo site_url('settings/thematics/insert') ?>">criar temáticas</a> para os poder atribuir a entidades</h5>
                                            </div>
                                        <?php else: ?>
                                            <div class="row">
                                                <div class="col-12">
                                                    <?php foreach ($thematics as $thematic): ?>
                                                        <div class="form-check">
                                                            <input <?php if (in_array($thematic->id, $program->thematics)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="thematics[]" value="<?php echo $thematic->id ?>" id="thematic<?php echo $thematic->id ?>">
                                                            <label class="form-check-label" for="thematic<?php echo $thematic->id ?>"><?php echo $thematic->name ?></label>
                                                        </div>
                                                    <?php endforeach?>
                                                </div>
                                            </div>
                                        <?php endif?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between">
                                        <h3 class="card-title">
                                            Editar Tipo de Entidade
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <?php if (empty($types)): ?>
                                            <div class="row">
                                                <h5>Deve <a href="<?php echo site_url('settings/types/insert') ?>">criar Tipo de Entidade</a> para os poder atribuir a entidades</h5>
                                            </div>
                                        <?php else: ?>
                                            <div class="row">
                                                <div class="col-12">
                                                    <?php foreach ($types as $type): ?>
                                                        <div class="form-check">
                                                            <input <?php if (in_array($type->id, $program->types)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="types[]" value="<?php echo $type->id ?>" id="types<?php echo $type->id ?>">
                                                            <label class="form-check-label" for="types<?php echo $type->id ?>"><?php echo $type->name ?></label>
                                                        </div>
                                                    <?php endforeach?>
                                                </div>
                                            </div>
                                        <?php endif?>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between">
                                        <h3 class="card-title">
                                            Editar Setores
                                        </h3>
                                    </div>
                                    <div class="card-body">
                                        <?php if (empty($sectors)): ?>
                                            <div class="row">
                                                <h5>Deve <a href="<?php echo site_url('settings/types/insert') ?>">criar Tipo de Entidade</a> para os poder atribuir a entidades</h5>
                                            </div>
                                        <?php else: ?>
                                            <div class="row">
                                                <div class="col-12">
                                                    <?php foreach ($sectors as $sector): ?>
                                                        <div class="form-check">
                                                            <input <?php if (in_array($sector->id, $program->sectors)): ?>checked<?php endif?> class="form-check-input" type="checkbox" name="sectors[]" value="<?php echo $sector->id ?>" id="sectors<?php echo $sector->id ?>">
                                                            <label class="form-check-label" for="sectors<?php echo $sector->id ?>"><?php echo $sector->name ?></label>
                                                        </div>
                                                    <?php endforeach?>
                                                </div>
                                            </div>
                                        <?php endif?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>