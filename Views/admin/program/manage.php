<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Programas Europeus</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('notices/europe-programs') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($program->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Programa europeu
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open_multipart('notices/europe-programs/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $program->id ?? null ?>">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="program" class="form-label">Programa</label>
                                    <input type="text" name="program" class="form-control" aria-describedby="program" value="<?php echo set_value('program', $program->program ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="organism_id" class="form-label">Organismo</label>
                                    <select name="organism_id" class="form-control">
                                        <option value=""></option>
                                        <?php foreach ($organisms as $organism): ?>
                                            <option <?php if (isset($program->organism_id) && $program->organism_id === $organism->id): ?>selected<?php endif?> value="<?php echo $organism->id ?>"><?php echo $organism->entity ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="name" class="form-label">Medida</label>
                                    <input type="text" name="name" class="form-control" aria-describedby="name" value="<?php echo set_value('name', $program->name ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">Descrição</label>
                                    <textarea name="description" class="form-control"rows="10"><?php echo set_value('description', $program->description ?? null) ?></textarea>
                               </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="priority" class="form-label">Prioridades</label>
                                    <textarea name="priority" class="form-control"rows="10"><?php echo set_value('priority', $program->priority ?? null) ?></textarea>
                               </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="objetives" class="form-label">Objetivos</label>
                                    <textarea name="objetives" class="form-control"rows="10"><?php echo set_value('objetives', $program->objetives ?? null) ?></textarea>
                               </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="to_whom" class="form-label">Beneficiários</label>
                                    <textarea name="to_whom" class="form-control"rows="10"><?php echo set_value('to_whom', $program->to_whom ?? null) ?></textarea>
                               </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="expenses" class="form-label">Despesas elegíveis</label>
                                    <textarea name="expenses" class="form-control"rows="10"><?php echo set_value('expenses', $program->expenses ?? null) ?></textarea>
                               </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="budget" class="form-label">Dotação</label>
                                    <input type="text" name="budget" class="form-control" aria-describedby="budget" value="<?php echo set_value('budget', $program->budget ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="tax" class="form-label">Taxa de financiamento</label>
                                    <input type="text" name="tax" class="form-control" aria-describedby="tax" value="<?php echo set_value('tax', $program->tax ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-2">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="starts_at" class="form-label">Data de ínicio</label>
                                    <input type="date" name="starts_at" class="form-control" aria-describedby="starts_at" value="<?php echo set_value('starts_at', $program->starts_at ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="ends_at" class="form-label">Data de Fim</label>
                                    <input type="date" name="ends_at" class="form-control" aria-describedby="ends_at" value="<?php echo set_value('ends_at', $program->ends_at ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="image" class="form-label">Imagem do programa</label>
                                    <?php if (!empty($program->image)): ?>
                                        <div>
                                            <a class="mr-2" href="<?php echo base_url('uploads/programs/images/' . ($program->image)) ?>" target="_blank"><i class="fa-duotone fa-image mr-1"></i><?php echo $program->image ?></a>
                                            <a onclick="return confirmDelete();" href="<?php echo site_url('notices/europe-programs/delete-img/' . $program->id) ?>"><i class="fa-duotone fa-trash"></i></a>
                                        </div>
                                    <?php else: ?>
                                        <input type="file" class="form-control" name="image"/>
                                    <?php endif?>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>