<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Avisos do Plano de Recuperação e Resiliência</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="card-header">
                        <ul class="nav nav-tabs">
                            <li class="nav-item">
                                <a class="nav-link <?php if (!$archive): ?>active<?php endif?>" href="<?php echo site_url('notices/prr') ?>">Dentro do prazo</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php if ($archive): ?>active<?php endif?>" href="<?php echo site_url('notices/prr?archive=1') ?>">Arquivo</a>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th>Investimento</th>
                                <th>Designação do aviso</th>
                                <th>Tags</th>
                                <th>Datas</th>
                                <th>Status</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($notices as $notice): ?>
                            <tr>
                                <td><?php echo word_limiter($notice->name, 3) ?></td>
                                <td><?php echo word_limiter($notice->program, 3) ?></td>
                                <td><?php echo word_limiter($notice->tags, 3) ?></td>
                                <td><?php echo $notice->date_start ?> <br> <?php echo $notice->date_end ?></td>
                                <td><?php echo view_cell('\Admin\Cells\NoticeCell::status', ['status' => $notice->status]) ?></td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a target="_blank" data-toggle="tooltip" data-placement="top" title="Ver em recuperarportugal.gov.pt" href="<?php echo $notice->link ?>" class="btn btn-primary"><i class="fa-duotone fa-file-contract"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Links Adicionais" href="<?php echo site_url('notices/prr/links/' . $notice->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-link"></i></a>
                                        <a href="<?php echo site_url('notices/prr/update/' . $notice->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-pen-to-square"></i></a>
                                        <a onclick="return confirmDelete();" href="<?php echo site_url('notices/prr/delete/' . $notice->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable();
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>