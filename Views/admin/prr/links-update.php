<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-10">
            <h1 class="m-0">Avisos do Plano de Recuperação e Resiliência</h1>
        </div>
        <div class="col-sm-2 text-right">
            <a href="<?php echo site_url('notices/links/' . $notice->id) ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($link->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Links Avisos do Plano de Recuperação e Resiliência
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('notices/prr/links/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $link->id ?? null ?>">
                        <input type="hidden" name="notice_id" value="<?php echo $link->notice_id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="name">Nome</label>
                                <input type="text" required class="form-control" name="name" aria-describedby="name" value="<?php echo set_value('name', $link->name ?? null) ?>" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="link" class="form-label">Link</label>
                                    <input type="url" required name="link" class="form-control" aria-describedby="link" value="<?php echo set_value('link', $link->link ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>