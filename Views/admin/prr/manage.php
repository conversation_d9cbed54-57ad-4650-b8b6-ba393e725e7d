<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-10">
            <h1 class="m-0">Avisos do Plano de Recuperação e Resiliência</h1>
        </div>
        <div class="col-sm-2 text-right">
            <a href="<?php echo site_url('notices/prr') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($notice->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Avisos do Plano de Recuperação e Resiliência
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('notices/prr/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $notice->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="name">Investimento</label>
                                <input type="text" class="form-control" name="name" aria-describedby="name" value="<?php echo set_value('name', $notice->name ?? null) ?>" required>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="program">Designação do aviso</label>
                                <input type="text" class="form-control" name="program" aria-describedby="program" value="<?php echo set_value('program', $notice->program ?? null) ?>" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="date" class="form-label">Data Publicação</label>
                                    <input type="date" name="date" class="form-control" aria-describedby="date" value="<?php echo set_value('date', $notice->date ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="date_start" class="form-label">Início</label>
                                    <input type="date" name="date_start" class="form-control" aria-describedby="date_start" value="<?php echo set_value('date_start', $notice->date_start ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="date_end" class="form-label">Fim</label>
                                    <input type="date" name="date_end" class="form-control" aria-describedby="date_end" value="<?php echo set_value('date_end', $notice->date_end ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="link" class="form-label">Link</label>
                                    <input type="url" required name="link" class="form-control" aria-describedby="link" value="<?php echo set_value('link', $notice->link ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="document" class="form-label">Documento</label>
                                    <input type="url" name="document" class="form-control" aria-describedby="document" value="<?php echo set_value('document', $notice->document ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="tags" class="form-label">Tags</label>
                                    <input type="text" name="tags" class="form-control" aria-describedby="tags" value="<?php echo set_value('tags', $notice->tags ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="texts" class="form-label">Textos</label>
                                    <textarea name="texts" class="form-control"><?php echo set_value('tags', $notice->texts ?? null) ?></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-4">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select name="status" required class="form-control">
                                        <option></option>
                                        <option <?php if ($notice->status === 'active'): ?>selected<?php endif?> value="active">Ativo</option>
                                        <option <?php if ($notice->status === 'inactive'): ?>selected<?php endif?> value="inactive">Inativo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <h4>Segmentação</h4>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'districts')" type="radio" name="segmentation_type" id="districts" value="districts" checked <?php if ($notice->segmentation_type === 'districts'): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="district">Distrito</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'cities')" type="radio" name="segmentation_type" id="cities" value="cities" <?php if ($notice->segmentation_type === 'cities'): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="concelho">Concelho</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'nut_1')" type="radio" name="segmentation_type" id="nut_1" value="nut_1" <?php if ($notice->segmentation_type === 'nut_1'): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="nut_1">NUT 1</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'nut_2')" type="radio" name="segmentation_type" id="nut_2" value="nut_2" <?php if ($notice->segmentation_type === 'nut_2'): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="nut_2">NUT 2</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'nut_3')" type="radio" name="segmentation_type" id="nut_3" value="nut_3" <?php if ($notice->segmentation_type === 'nut_3'): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="nut_3">NUT 3</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" onclick="showSegments(<?php echo $notice->id ?>, 'cims')" type="radio" name="segmentation_type" id="cims" value="cims" <?php if ($notice->cim != 0): ?>checked<?php endif?>>
                                    <label class="form-check-label" for="cims">CIM</label>
                                </div>
                                <p><hr></p>
                                <div class="radio-segmentation" data-programid="<?php echo $notice->id ?>" data-type="<?php echo $notice->segmentation_type ?>" data-cim="<?php echo (int) $notice->cim ?>">
                                    <select name="districts[]" multiple class="sel-districts d-none form-control districts<?php echo $notice->id ?>" style="height: 300px;">
                                        <?php foreach ($districts as $district): ?>
                                            <option <?php if ($notice->segmentation_type === 'districts' && in_array($district->id, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $district->id ?>"><?php echo $district->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                    <select name="cities[]" multiple class="sel-cities d-none form-control cities<?php echo $notice->id ?>" style="height: 300px;">
                                        <?php foreach ($districts as $district): ?>
                                            <optgroup label="<?php echo $district->name ?>">
                                            <?php foreach ($district->cities as $city): ?>
                                                <option <?php if ($notice->segmentation_type === 'cities' && in_array($city->id, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $city->id ?>"><?php echo $city->name ?></option>
                                            <?php endforeach?>
                                        </optgroup>
                                        <?php endforeach?>
                                    </select>
                                    <select name="nut_1[]" multiple class="sel-nut_1 d-none form-control nut_1<?php echo $notice->id ?>" style="height: 300px;">
                                        <?php foreach ($nuts1 as $nut1): ?>
                                            <option <?php if ($notice->segmentation_type === 'nut_1' && in_array($nut1->nut, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut1->nut ?>"><?php echo $nut1->nut ?></option>
                                        <?php endforeach?>
                                    </select>
                                    <select name="nut_2[]" multiple class="sel-nut_2 d-none form-control nut_2<?php echo $notice->id ?>" style="height: 200px;">
                                        <?php foreach ($nuts2 as $nut2): ?>
                                            <option <?php if ($notice->segmentation_type === 'nut_2' && in_array($nut2->nut, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut2->nut ?>"><?php echo $nut2->nut ?></option>
                                        <?php endforeach?>
                                    </select>
                                    <select name="nut_3[]" multiple class="sel-nut_3 d-none form-control nut_3<?php echo $notice->id ?>" style="height: 100px;">
                                        <?php foreach ($nuts3 as $nut3): ?>
                                            <option <?php if ($notice->segmentation_type === 'nut_3' && in_array($nut3->nut, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $nut3->nut ?>"><?php echo $nut3->nut ?></option>
                                        <?php endforeach?>
                                    </select>
                                    <select name="cims[]" multiple class="sel-cims d-none form-control cims<?php echo $notice->id ?>" style="height: 300px;">
                                        <?php foreach ($cims as $cim): ?>
                                            <option <?php if ($notice->cim && in_array($cim->id, $notice->segmentation)): ?>selected="selected"<?php endif?> value="<?php echo $cim->id ?>"><?php echo $cim->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                                <p><hr></p>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="thematics">Temáticas</label>
                                            <select name="segments[thematics][]" class="form-control" multiple>
                                                <?php foreach ($thematics as $thematic): ?>
                                                    <option <?php if (in_array($thematic->id, $notice->thematics)): ?>selected<?php endif?> value="<?php echo $thematic->id ?>"><?php echo $thematic->name ?></option>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="types">Tipo de Entidade</label>
                                            <select name="segments[types][]" class="form-control" multiple>
                                                <?php foreach ($types as $type): ?>
                                                    <option <?php if (in_array($type->id, $notice->types)): ?>selected<?php endif?> value="<?php echo $type->id ?>"><?php echo $type->name ?></option>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="sectors">Setores</label>
                                            <select name="segments[sectors][]" class="form-control" multiple>
                                                <?php foreach ($sectors as $sector): ?>
                                                    <option <?php if (in_array($sector->id, $notice->sectors)): ?>selected<?php endif?> value="<?php echo $sector->id ?>"><?php echo $sector->name ?></option>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
    <script src="<?php echo base_url('dist/js/segmentation.js'); ?>" ></script>
<?php echo $this->endSection() ?>