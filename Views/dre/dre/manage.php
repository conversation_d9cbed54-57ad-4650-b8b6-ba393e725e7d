<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Dre.pt - <?php if (empty($contest->id)): ?>Novo Concurso<?php else: ?><?php echo $contest->title ?><?php endif?></h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('dre') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($contest->id)): ?>Inserir<?php else: ?>Editar<?php endif; ?> Concurso
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('dre/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $contest->id ?? null ?>">
                        <?php if (empty($contest->id)): ?>
                            <div class="row mb-3">
                                <div class="col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label for="title" class="form-label">Título</label>
                                        <input type="text" name="title" class="form-control" required aria-describedby="title" value="<?php echo set_value('title', $contest->title ?? null) ?>">
                                    </div>
                                </div>
                                <div class="col-md-6 col-sm-12">
                                    <div class="form-group">
                                        <label for="link" class="form-label">Link</label>
                                        <input type="text" name="link" class="form-control" aria-describedby="link" value="<?php echo set_value('link', $contest->link ?? null) ?>">
                                    </div>
                                </div>
                            </div>
                        <?php endif?>
                        <div class="row mb-3">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="promoter" class="form-label">Promotor</label>
                                    <input type="text" name="promoter" class="form-control" aria-describedby="promoter" value="<?php echo set_value('promoter', $contest->promoter ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="ref" class="form-label">Referência</label>
                                    <input type="text" name="ref" class="form-control" aria-describedby="ref" value="<?php echo set_value('ref', $contest->ref ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="procedure" class="form-label">Tipo de procedimento</label>
                                    <input type="text" name="procedure" class="form-control" aria-describedby="procedure" value="<?php echo set_value('procedure', $contest->procedure ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-12">
                                <div class="form-group">
                                    <label for="interest" class="form-label">Interesse</label>
                                    <select name="interest" class="form-control" required>
                                        <option value="1" <?php echo set_select('interest', '1', isset($contest->interest) && $contest->interest === '1'); ?>>Sim</option>
                                        <option value="0" <?php echo set_select('interest', '0', isset($contest->interest) && $contest->interest === '0'); ?>>Não</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nome do Projeto</label>
                                    <input type="text" name="name" class="form-control" aria-describedby="name" value="<?php echo set_value('name', $contest->name ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">Descrição</label>
                                    <textarea name="description" rows="3" class="form-control" required><?php echo set_value('description', $contest->description ?? null) ?></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-5 col-sm-12">
                                <div class="form-group">
                                    <label for="platform" class="form-label">Plataforma/E-mail</label>
                                    <input type="text" name="platform" class="form-control" aria-describedby="platform" value="<?php echo set_value('platform', $contest->platform ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="base_price" class="form-label">Preço Base</label>
                                    <input type="text" name="base_price" class="form-control" aria-describedby="base_price" value="<?php echo set_value('base_price', $contest->base_price ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-12">
                                <div class="form-group">
                                    <label for="mono_price" class="form-label">Monofator preço</label>
                                    <select name="mono_price" class="form-control">
                                        <option value=""></option>
                                        <option value="1" <?php echo set_select('mono_price', '1', isset($contest->mono_price) && $contest->mono_price === '1'); ?>>Sim</option>
                                        <option value="0" <?php echo set_select('mono_price', '0', isset($contest->mono_price) && $contest->mono_price === '0'); ?>>Não</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="date_clarify" class="form-label">Data limite solicitar esclarecimentos</label>
                                    <input type="date" name="date_clarify" class="form-control" aria-describedby="date_clarify" value="<?php echo set_value('date_clarify', $contest->date_clarify ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="hour_clarify" class="form-label">Hora limite solicitar esclarecimentos</label>
                                    <input type="time" name="hour_clarify" class="form-control" aria-describedby="hour_clarify" value="<?php echo set_value('hour_clarify', $contest->hour_clarify ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label for="clarifications" class="form-label">Esclarecimentos Solicitados</label>
                                    <input type="text" name="clarifications" class="form-control" aria-describedby="clarifications" value="<?php echo set_value('clarifications', $contest->clarifications ?? null) ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="date_candidacy" class="form-label">Data limite apresentação da candidatura</label>
                                    <input type="date" name="date_candidacy" class="form-control" aria-describedby="date_candidacy" value="<?php echo set_value('date_candidacy', $contest->date_candidacy ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="hour_candidacy" class="form-label">Hora limite apresentação da candidatura</label>
                                    <input type="time" name="hour_candidacy" class="form-control" aria-describedby="hour_candidacy" value="<?php echo set_value('hour_candidacy', $contest->hour_candidacy ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="date_proposal" class="form-label">Data limite apresentação da proposta</label>
                                    <input type="date" name="date_proposal" class="form-control" aria-describedby="date_proposal" value="<?php echo set_value('date_proposal', $contest->date_proposal ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="hour_proposal" class="form-label">Hora limite apresentação da proposta</label>
                                    <input type="time" name="hour_proposal" class="form-control" aria-describedby="hour_proposal" value="<?php echo set_value('hour_proposal', $contest->hour_proposal ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-12">
                                <div class="form-group">
                                    <label for="decision" class="form-label">Decisão de avançar</label>
                                    <select name="decision" class="form-control">
                                        <option value=""></option>
                                        <option value="1" <?php echo set_select('decision', '1', isset($contest->decision) && $contest->decision === '1'); ?>>Sim</option>
                                        <option value="0" <?php echo set_select('decision', '0', isset($contest->decision) && $contest->decision === '0'); ?>>Não</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="candidate" class="form-label">Empresa Concorrente</label>
                                    <input type="text" name="candidate" class="form-control" aria-describedby="candidate" value="<?php echo set_value('candidate', $contest->candidate ?? null) ?>">
                                </div>
                            </div>
                            <div class="col-md-1 col-sm-12">
                                <div class="form-group">
                                    <label for="status" class="form-label">Estado</label>
                                    <select name="status" class="form-control">
                                        <option value="no_status" <?php echo set_select('status', 'no_status', isset($contest->status) && $contest->status === 'no_status'); ?>>Sem estado</option>
                                        <option value="open" <?php echo set_select('status', 'open', isset($contest->status) && $contest->status === 'open'); ?>>Aberto</option>
                                        <option value="closed" <?php echo set_select('status', 'closed', isset($contest->status) && $contest->status === 'closed'); ?>>Fechado</option>
                                        <option value="revoked" <?php echo set_select('status', 'revoked', isset($contest->status) && $contest->status === 'revoked'); ?>>Revogado</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-2 col-sm-12">
                                <div class="form-group">
                                    <label for="companies" class="form-label">Empresa que vai avançar:</label>
                                    <select name="companies[]" class="form-control multi" multiple>
                                        <?php foreach ($companies as $company): ?>
                                            <?php $selected = (in_array($company->id, array_column($contest->companies ?? [], 'company_id'))) ? 'selected' : ''; ?>
                                            <option value="<?php echo $company->id; ?>" <?php echo $selected; ?>><?php echo $company->name; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="grouping" class="form-label">Agrupamento</label>
                                    <select name="grouping" class="form-control">
                                        <option value=""></option>
                                        <option value="1" <?php echo set_select('grouping', '1', isset($contest->grouping) && $contest->grouping === '1'); ?>>Sim</option>
                                        <option value="0" <?php echo set_select('grouping', '0', isset($contest->grouping) && $contest->grouping === '0'); ?>>Não</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-10 col-sm-12">
                                <div class="form-group">
                                    <label for="obs" class="form-label">Observações</label>
                                    <textarea name="obs" rows="7" class="form-control"><?php echo set_value('obs', $contest->obs ?? null) ?></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label for="interest_emails" class="form-label">E-mails Interesse (deixar vírgulas entre e-mails)</label>
                                    <textarea name="interest_emails" rows="3" class="form-control"><?php echo set_value('interest_emails', $contest->interest_emails ?? null) ?></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
    $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
    });
    document.querySelectorAll('select.multi option').forEach(function(option) {
        option.addEventListener('mousedown', function(e) {
            e.preventDefault();
            option.selected = !option.selected;
            option.parentNode.focus();
            return false;
        });
    });
    </script>
<?php $this->endSection(); ?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>