<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Dre.pt - <?php echo $contest->title ?></h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title"><?php echo $contest->title ?></h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4>Informação Detalhada</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <ul class="list-group list-group-flush">
                                <?php if (isset($excludedWords) && !empty($excludedWords)): ?>
                                    <li class="list-group-item"><strong>Palavras excluídas:</strong> <?php echo implode(', ', $excludedWords) ?></li>
                                <?php endif?>
                                <li class="list-group-item"><strong>Data de publicação:</strong> <?php echo $contest->created_at ? date('d-m-Y', strtotime($contest->created_at)) : '' ?></li>
                                <li class="list-group-item"><strong>Promotor:</strong> <?php echo $contest->promoter ?></li>
                                <li class="list-group-item"><strong>Nome do Projeto:</strong> <?php echo $contest->name ?></li>
                                <li class="list-group-item"><strong>Descrição:</strong> <?php echo $contest->description ?></li>
                                <li class="list-group-item"><strong>Referência:</strong> <?php echo $contest->ref ?></li>
                                <?php if (!empty($contest->link)): ?>
                                    <li class="list-group-item"><strong>Concurso:</strong> <a target="_blank" href="<?php echo $contest->link ?>">Ligação para o concurso</a></li>
                                <?php endif?>
                                <li class="list-group-item"><strong>Interesse:</strong> <?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->interest]) ?></li>
                                <li class="list-group-item"><strong>Tipo de procedimento:</strong> <?php echo $contest->procedure ?></li>
                                <li class="list-group-item"><strong>Preço Base:</strong> <?php echo $contest->base_price ?></li>
                                <li class="list-group-item"><strong>Plataforma/E-mail:</strong> <?php echo $contest->platform ?></li>
                                <li class="list-group-item"><strong>Monofator preço:</strong> <?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->mono_price]) ?></li>
                                <li class="list-group-item"><strong>Data limite solicitar esclarecimentos:</strong> <?php echo $contest->date_clarify ? date('d-m-Y', strtotime($contest->date_clarify)) : '' ?></li>
                                <li class="list-group-item"><strong>Hora limite solicitar esclarecimentos:</strong> <?php echo $contest->hour_clarify ? date('H:i', strtotime($contest->hour_clarify)) : '' ?></li>
                                <li class="list-group-item"><strong>Esclarecimentos solicitados:</strong> <?php echo $contest->clarifications ?></li>
                                <li class="list-group-item"><strong>Data limite apresentação da candidatura:</strong> <?php echo $contest->date_candidacy ? date('d-m-Y', strtotime($contest->date_candidacy)) : '' ?></li>
                                <li class="list-group-item"><strong>Hora limite apresentação da candidatura:</strong> <?php echo $contest->hour_candidacy ? date('H:i', strtotime($contest->hour_candidacy)) : '' ?></li>
                                <li class="list-group-item"><strong>Data limite apresentação da proposta:</strong> <?php echo $contest->date_proposal ? date('d-m-Y', strtotime($contest->date_proposal)) : '' ?></li>
                                <li class="list-group-item"><strong>Hora limite apresentação da proposta:</strong> <?php echo $contest->hour_proposal ? date('H:i', strtotime($contest->hour_proposal)) : '' ?></li>
                                <li class="list-group-item"><strong>Decisão de avançar:</strong> <?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->decision]) ?></li>
                                <li class="list-group-item"><strong>Empresa concorrente:</strong> <?php echo $contest->candidate ?></li>
                                <li class="list-group-item"><strong>Empresa que vai avançar:</strong> <?php echo implode(', ', array_column($contest->companies, 'name')) ?></li>
                                <li class="list-group-item"><strong>Agrupamento:</strong> <?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->grouping]) ?></li>
                                <li class="list-group-item"><strong>Estado:</strong> <?php echo view_cell('\DreApp\Cells\ContestCell::status', ['status' => $contest->status]) ?></li>
                                <li class="list-group-item"><strong>Observações:</strong> <?php echo $contest->obs ?></li>
                                <li class="list-group-item"><strong>E-mails Interesse:</strong> <?php echo $contest->interest_emails ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
      });
    </script>
<?php $this->endSection(); ?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>