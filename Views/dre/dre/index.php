<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Dre.pt</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('dre/insert') ?>" class="btn btn-primary"><i class="fa-duotone fa-plus"></i> Inserir Concurso</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link <?php if (!$excluded && !$others): ?>active<?php endif?>" href="<?php echo site_url('dre/index') ?>">Com interesse</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php if ($others): ?>active<?php endif?>" href="<?php echo site_url('dre/index?others=1') ?>">Outros</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php if ($excluded): ?>active<?php endif?>" href="<?php echo site_url('dre/index?excluded=1') ?>">Excluídos</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <form class="card">
                        <?php if (isset($_GET['excluded']) && $_GET['excluded'] === '1'): ?>
                            <input type="hidden" name="excluded" value="1">
                        <?php endif?>
                        <?php if (isset($_GET['others']) && $_GET['others'] === '1'): ?>
                            <input type="hidden" name="others" value="1">
                        <?php endif?>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="from">Data da Publicação <small>(de)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="from" value="<?php echo set_value('from', $_GET['from'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="to">Data da Publicação <small>(até)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="to" value="<?php echo set_value('to', $_GET['to'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="clarify_from">Data limite Esclarecimentos <small>(de)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="clarify_from" value="<?php echo set_value('clarify_from', $_GET['clarify_from'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="clarify_to">Data limite Esclarecimentos <small>(até)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="clarify_to" value="<?php echo set_value('clarify_to', $_GET['clarify_to'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="candidacy_from">Data limite Candidatura <small>(de)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="candidacy_from" value="<?php echo set_value('candidacy_from', $_GET['candidacy_from'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="candidacy_to">Data limite Candidatura <small>(até)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="candidacy_to" value="<?php echo set_value('candidacy_to', $_GET['candidacy_to'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="proposal_from">Data limite Proposta <small>(de)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="proposal_from" value="<?php echo set_value('proposal_from', $_GET['proposal_from'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="proposal_to">Data limite Proposta <small>(até)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="proposal_to" value="<?php echo set_value('proposal_to', $_GET['proposal_to'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="status">Estado</label>
                                        <select onchange="this.form.submit()" name="status" class="form-control">
                                            <option value=""></option>
                                            <option <?php if (isset($_GET['status']) && $_GET['status'] === 'no_status'): ?>selected<?php endif?> value="no_status">Sem Estado</option>
                                            <option <?php if (isset($_GET['status']) && $_GET['status'] === 'open'): ?>selected<?php endif?> value="open">Aberto</option>
                                            <option <?php if (isset($_GET['status']) && $_GET['status'] === 'closed'): ?>selected<?php endif?> value="closed">Fechado</option>
                                            <option <?php if (isset($_GET['status']) && $_GET['status'] === 'revoked'): ?>selected<?php endif?> value="revoked">Revogado</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="search">Pesquisa</label>
                                        <input onchange="this.form.submit()" type="text" name="search" value="<?php echo $_GET['search'] ?? null ?>" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <?php if (isset($_GET['excluded']) && $_GET['excluded'] === '1'): ?>
                                            <a href="<?php echo site_url('dre/index?excluded=1') ?>" class="mt-33 btn btn-default"><i class="fa-duotone fa-broom-wide"></i></a>
                                        <?php else: ?>
                                            <a href="<?php echo site_url('dre/index') ?>" class="mt-33 btn btn-default"><i class="fa-duotone fa-broom-wide"></i></a>
                                        <?php endif?>

                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <button type="submit" class="mt-33 btn btn-primary"><i class="fa-duotone fa-magnifying-glass"></i> Pesquisar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                    <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th width="100">Data da Publicação</th>
                                <th>Promotor</th>
                                <th>Nome do Projeto</th>
                                <th>Descrição</th>
                                <th>Ref</th>
                                <th>Procedimento</th>
                                <th>Data limite esclarecimentos</th>
                                <th>Data limite candidatura</th>
                                <th>Data limite proposta</th>
                                <th>Decisão de avançar</th>
                                <th>Estado</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contests as $contest): ?>
                            <tr>
                                <td><?php echo $contest->created_at ? date('Y-m-d', strtotime($contest->created_at)) : ''; ?></td>
                                <td><?php echo $contest->promoter ?></td>
                                <td><?php echo $contest->name ?></td>
                                <td><?php echo $contest->description ?></td>
                                <td><?php echo $contest->ref ?></td>
                                <td><?php echo $contest->procedure ?></td>
                                <td><?php echo $contest->date_clarify ? date('Y-m-d', strtotime($contest->date_clarify)) : ''; ?></td>
                                <td><?php echo $contest->date_candidacy ? date('Y-m-d', strtotime($contest->date_candidacy)) : ''; ?></td>
                                <td><?php echo $contest->date_proposal ? date('Y-m-d', strtotime($contest->date_proposal)) : ''; ?></td>
                                <td><?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->decision]) ?></td>
                                <td><?php echo view_cell('\DreApp\Cells\ContestCell::status', ['status' => $contest->status]) ?></td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a target="_blank" data-toggle="tooltip" data-placement="top" title="Ver em dre.pt" href="<?php echo $contest->link ?>" class="btn btn-primary <?php if (empty($contest->link)): ?>disabled<?php endif?>"><i class="fa-duotone fa-file-contract"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Editar concurso" href="<?php echo site_url('dre/update/' . $contest->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-pen-to-square"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Ver Detalhes" href="<?php echo site_url('dre/detail/' . $contest->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-memo-circle-info"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Copiar link público"
                                           href="javascript:void(0);" class="btn btn-primary" onclick="copyToClipboard('<?php echo site_url('dre/detail-public/' . $contest->id); ?>')">
                                           <i class="fa-duotone fa-unlock"></i>
                                        </a>
                                        <a data-toggle="tooltip" data-placement="top" title="Anexos" href="<?php echo site_url('dre/attachments/' . $contest->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-files"></i></a>
                                        <?php if ($contest->interest): ?>
                                            <a data-toggle="tooltip" data-placement="top" title="Enviar E-mail: <?php echo str_replace(',', ' ', $contest->interest_emails) ?>" href="<?php echo site_url('dre/send-email/' . $contest->id) ?>" class="btn btn-primary <?php if ($contest->notified || empty($contest->interest_emails) || $contest->status === 'revoked'): ?>disabled<?php endif?>" onclick="return confirmDelete();"><i class="fa-duotone fa-envelope"></i></a>
                                        <?php endif?>
                                        <a data-toggle="tooltip" data-placement="top" href="<?php echo site_url('dre/interest/' . $contest->id) ?>"
                                           class="btn btn-primary" onclick="return confirmDelete();"
                                           title="<?php echo $contest->interest ? 'Remover Interesse' : 'Marcar Interesse'; ?>">
                                            <?php if ($contest->interest): ?>
                                                <i class="fa-duotone fa-xmark"></i>
                                            <?php else: ?>
                                                <i class="fa-duotone fa-check"></i>
                                            <?php endif; ?>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true,
            "order": [[0, 'desc']]
        });
      });
    </script>
    <script>
        function copyToClipboard(link) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(link).then(function() {
                    alert('Link copiado para a área de transferência!');
                }).catch(function(error) {
                    console.error('Erro ao copiar o link: ', error);
                });
            } else {
                let tempInput = document.createElement("input");
                tempInput.value = link;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand("copy");
                document.body.removeChild(tempInput);
                alert('Link copiado para a área de transferência!');
            }
        }
    </script>
<?php $this->endSection(); ?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>