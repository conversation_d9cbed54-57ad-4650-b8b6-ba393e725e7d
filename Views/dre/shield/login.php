<?php echo $this->extend('layouts/auth') ?>

<?php echo $this->section('content') ?>
<?php echo form_open('auth/login') ?>
    <input type="hidden" name="backto" value="<?php echo $_GET['backto'] ?? '' ?>">
    <div class="form-header text-center">
        <img class="mb-4" src="<?php echo base_url('dist/images/rcp-completo.png'); ?>" alt="Radar Fundos Europeus" width="380">
    </div>
    <div class="mb-3">
        <label for="username" class="form-label">E-mail</label>
        <input type="text" name="email" class="form-control" aria-describedby="email" placeholder="Introduza o e-mail" required>
    </div>
    <label for="password" class="form-label">Password</label>
    <div class="input-group mb-3">
        <input type="password" class="form-control" name="password" placeholder="Introduza a password" required>
        <span class="input-group-text">
            <i class="fa-regular fa-eye-slash" id="togglePassword"></i>
        </span>
    </div>
    <div class="mb-4 form-check pl-0">
        <div class="row">
            <div class="col-6 text-start">
                <input type="checkbox" name="chk_remember">
                <label class="form-check-label" for="chk_remember">Lembrar-me</label>
            </div>
            <div class="col-6 text-end">
                <a href="<?php echo site_url('auth/forgot-password') ?>" class="text-end"><u>Esqueceu-se da password</u></a>
            </div>
        </div>
    </div>
    <button type="submit" class="btn btn-primary">Entrar</button>
    <?php if (!empty(session()->has('error'))): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erro!</strong> <?php echo session()->getFlashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('confirm'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo session()->getFlashdata('confirm'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('message'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo session()->getFlashdata('message'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
<?php echo form_close() ?>
<?php echo $this->endSection() ?>