<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Destinatários</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/emails') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (!isset($key)): ?>Inserir<?php else: ?>Editar<?php endif;?> Destinatário
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/emails/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="key" value="<?php echo $key ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-12 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">Destinatário</label>
                                    <input type="email" name="email" maxlength="255" class="form-control" aria-describedby="email" value="<?php echo set_value('email', $email ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>