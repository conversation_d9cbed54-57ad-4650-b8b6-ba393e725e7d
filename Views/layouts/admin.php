<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Radar Fundos Europeus</title>
    <link rel="icon" type="image/png"  href="<?php echo base_url('dist/favicon/android-icon-192x192.png'); ?>">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <script src="https://kit.fontawesome.com/d86bcd5bc3.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/icheck-bootstrap/icheck-bootstrap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/jqvmap/jqvmap.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/dist/css/adminlte.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/overlayScrollbars/css/OverlayScrollbars.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/daterangepicker/daterangepicker.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/summernote/summernote-bs4.min.css'); ?>">
    <link rel="stylesheet" href="<?php echo base_url('/dist/css/admin.css'); ?>">
    <?php echo $this->renderSection('styles') ?>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        <nav class="main-header navbar navbar-expand navbar-white navbar-light navbar-100">
            <?php echo view('layouts/navigation/' . $navigation) ?>
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" href="<?php echo site_url('auth/logout') ?>">
                        <i class="fa-duotone fa-arrow-right-from-bracket"></i>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper">
            <section class="content">
                <div class="container-fluid">
                <?php if (session('error') !== null): ?>
                    <div class="alert alert-danger" role="alert"><?php echo session('error') ?></div>
                <?php elseif (session('errors') !== null): ?>
                    <div class="alert alert-danger" role="alert">
                    <?php if (is_array(session('errors'))): ?>
                        <?php foreach (session('errors') as $error): ?>
                            <?php echo $error ?>
                            <br>
                        <?php endforeach?>
                    <?php else: ?>
                        <?php echo session('errors') ?>
                    <?php endif?>
                    </div>
                <?php endif?>
                <?php if (session('message') !== null): ?>
                    <div class="alert alert-success" role="alert"><?php echo session('message') ?></div>
                <?php endif?>
                <?php if (session('confirm') !== null): ?>
                    <div class="alert alert-success" role="alert"><?php echo session('confirm') ?></div>
                <?php endif?>
                <?php if ($activeMenu !== 'dashboard'): ?>
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="breadcrumb">
                            <?php echo $breadcrumbs->render(); ?>
                        </nav>
                    </div>
                </div>
                <?php endif?>
                <?php echo $this->renderSection('content') ?>
            </div>
        </section>
    </div>
    <footer class="main-footer">
        <strong>Copyright &copy; <?php echo date('Y') ?>
        <?php if (!session()->has('baseGovLogin')): ?>
            <a target="_blank" style="color: #000;" href="<?php echo site_url('/') ?>">RADAR FUNDOS EUROPEUS</a>.
        <?php else: ?>
            <a target="_blank" style="color: #000;" href="<?php echo site_url('/') ?>">RADAR CONCURSOS EUROPEUS</a>.
        <?php endif;?>
        </strong>
        Todos os direitos reservados | CI: <?php echo CodeIgniter\CodeIgniter::CI_VERSION ?> | APP: <a href="<?php echo site_url('release-log') ?>"><?php echo APP_VERSION ?></a> | <a target="_blank" href="<?php echo site_url('logs') ?>">Logs</a>
    </footer>
</div>
<script src="<?php echo base_url('admin/plugins/jquery/jquery.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/jquery-ui/jquery-ui.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/bootstrap/js/bootstrap.bundle.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/chart.js/Chart.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/sparklines/sparkline.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/jqvmap/jquery.vmap.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/jqvmap/maps/jquery.vmap.usa.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/jquery-knob/jquery.knob.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/moment/moment.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/daterangepicker/daterangepicker.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/summernote/summernote-bs4.min.js') ?>"></script>
<script src="<?php echo base_url('admin/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js') ?>"></script>
<script src="<?php echo base_url('admin/dist/js/adminlte.js') ?>"></script>
<script>
    $.widget.bridge('uibutton', $.ui.button)
    function confirmDelete() {
        return confirm("Tem a certeza que pretende continuar?");
    }
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    });
</script>
<?php echo $this->renderSection('scripts') ?>
</body>
</html>
