<?php echo $this->extend('layouts/auth') ?>

<?php echo $this->section('content') ?>
<?php echo form_open('auth/reset-password') ?>
    <input type="number" class="form-control" name="user_id" value="<?php echo $userId; ?>" hidden>
    <div class="form-header text-center">
        <img class="mb-4" src="<?php echo base_url('dist/images/logo.svg'); ?>" alt="Radar Fundos Europeus" width="380">
        <p class="mb-3 fw-normal">Introduza a nova password para aceder à sua conta</p>
        <hr>
    </div>
    <div class="mb-3">
        <label for="password" class="form-label">Password</label>
        <input type="password" class="form-control" name="password" aria-describedby="email" placeholder="Password" minlength="8" required>
    </div>
    <div class="mb-3">
        <label for="password_confirm" class="form-label">Confirmar Password</label>
        <input type="password" class="form-control" name="password_confirm" aria-describedby="password_confirm" placeholder="Confirmar Password" minlength="8" required>
    </div>
    <button type="submit" class="btn btn-secundary">Submeter</button>
    <?php if (!empty(session()->has('error'))): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erro!</strong> <?php echo session()->getFlashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('success'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo session()->getFlashdata('success'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
<?php echo form_close() ?>
<?php echo $this->endSection() ?>