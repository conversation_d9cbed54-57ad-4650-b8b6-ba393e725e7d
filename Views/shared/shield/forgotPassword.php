<?php echo $this->extend('layouts/auth') ?>

<?php echo $this->section('content') ?>
<form action="<?php echo url_to('auth/forgot-password') ?>" method="post">
    <div class="form-header text-center">
        <img class="mb-4" src="<?php echo base_url('dist/images/logo.svg'); ?>" alt="Radar Fundos Europeus" width="380">
        <p class="mb-3 fw-normal">Enviaremos um email para a recuperar a sua password</p>
        <hr>
    </div>
    <div class="mb-3">
        <label for="username" class="form-label">E-mail</label>
        <input type="text" class="form-control" name="email" aria-describedby="email" placeholder="Introduza o e-mail" required>
    </div>
    <button type="submit" class="btn btn-secundary">Recuperar Password</button>
    <?php if (!empty(session()->has('error'))): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erro!</strong> <?php echo session()->getFlashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('success'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo session()->getFlashdata('success'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <i class="fa fa-arrow-left" aria-hidden="true"></i> <a href="<?php echo site_url('auth/login') ?>">Ir para o login</a>
</form>
<?php echo $this->endSection() ?>