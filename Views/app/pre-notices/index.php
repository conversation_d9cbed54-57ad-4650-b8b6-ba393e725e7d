<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col">
            <h1>Avisos</h1>
            <h5>Pesquise os pre-avisos</h5>
        </div>
    </div>
    <div class="row">
        <?php if ($page !== 'bookmarks'): ?>
            <div class="col-12 filters">
                <ul>
                    <li>
                        <a data-bs-toggle="collapse" href="#filters" role="button" aria-expanded="false" aria-controls="filters">
                            <i class="fa-light fa-filter"></i> Filtrar <i class="fa-sharp fa-light fa-chevron-down"></i>
                        </a>
                    </li>
                </ul>
                <!-- FILTERS -->
                <form class="collapse mt-3" id="filters">
                    <div class="card card-body">
                        <div class="row">
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">&nbsp;</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-magnifying-glass"></i>
                                    </span>
                                   <input type="text" value="<?php echo $_GET['search'] ?? null ?>" name="search" class="form-control" placeholder="Procurar...">
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Data limite</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-calendar-days"></i>
                                    </span>
                                   <input value="<?php echo $_GET['dateEnd'] ?? null ?>" name="dateEnd" type="text" class="form-control datePickerClose" placeholder="Selecione"/>
                                </div>
                            </div>
                            <div class="mt-30 col-md-4 col-lg-3 col-xs-12 mobile-filter-btns">
                                <a href="<?php echo site_url('notices') ?>" class="btn btn-primary mobile-w-100"><i class="fa-light fa-broom-wide"></i></a>
                                <button type="submit" class="btn btn-primary mobile-w-100"><i class="fa-light fa-magnifying-glass"></i></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="col-12 mt-35">
                Encontrámos <?php echo $notices['pagination']->getTotal() ?> artigos
            </div>
            <?php echo view_cell('App\Cells\FiltersCell::show') ?>
        <?php endif?>
    </div>
    <div class="row mt-50 mobile-mt-20">
        <?php foreach ($notices['data'] as $notice): ?>
        <div class="col-md-4 col-xs-12">
            <div class="card mb-4">
                <div class="card-body notice-item">
                    <div class="row">
                        <div class="col-6"></div>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <h5 class="mb-3">
                                <a href="<?php echo site_url('pre-notices/detail/' . $notice->id . '/' . slug($notice->title)); ?>">
                                    <?php echo $notice->title ?>
                                </a>
                            </h5>
                            <p><?php echo word_limiter($notice->description, 10) ?></p>
                            <hr>
                        </div>
                        <div class="col-6">
                            <a href="<?php echo site_url('pre-notices/detail/' . $notice->id . '/' . slug($notice->title)); ?>">Saiba mais</a>
                        </div>
                        <div class="col-6 text-end"><?php echo $notice->end_date ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach?>
        <?php if (empty($notices['data'])): ?>
            <div class="text-center">
                Não foram encontrados Pré-Avisos.
            </div>
        <?php endif?>
    </div>
    <?php if ($notices['pagination']->getTotal() > 15): ?>
        <div class="d-flex justify-content-end mt-50 div-pagination">
            <?php echo $notices['pagination']->links() ?>
        </div>
    <?php endif?>
</div>
<?php echo $this->endSection() ?>

<?php echo $this->section('styles') ?>
    <link rel="stylesheet" href="<?php echo base_url('dist/css/datepicker.min.css'); ?>">
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
    <!-- https://www.npmjs.com/ -->
    <script src="<?php echo base_url('dist/js/datepicker.min.js'); ?>"></script>
    <script>
        const pickerClose = datepicker('.datePickerClose', {
            customDays: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
            customMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
            formatter: (input, date, instance) => {
                input.value =  date.toLocaleDateString()
            }
        });
        const chBoxes = document.querySelectorAll('.dropdown-menu input[type="checkbox"]');
        const dpBtn = document.getElementById('multiSelectDropdown');
        let mySelectedListItems = [];
        function handleCB() {
            mySelectedListItems = [];
            let mySelectedListItemsText = '';
            chBoxes.forEach((checkbox) => {
                if (checkbox.checked) {
                    mySelectedListItems.push(checkbox.value);
                    mySelectedListItemsText += checkbox.value + ', ';
                }
            });

            dpBtn.innerText =
                mySelectedListItems.length > 0
                    ? ( mySelectedListItems.length > 2 ? 'Vários' : mySelectedListItemsText.slice(0, -2)) : 'Selecione';
        }
        chBoxes.forEach((checkbox) => {
            checkbox.addEventListener('change', handleCB);
        });
    </script>
<?php echo $this->endSection() ?>