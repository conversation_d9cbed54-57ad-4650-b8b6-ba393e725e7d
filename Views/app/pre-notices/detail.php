<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row notice">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body notice-item">
                    <div class="row">
                        <div class="col pl-0 ">
                            <a href="<?php echo site_url('pre-notices') ?>" class="back">
                                <i class="fa-sharp fa-light fa-circle-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <h2 class="mt-3"><?php echo $notice->title ?></h5>
                            <h3 class="mb-3"><?php echo $notice->tags ?></h5>
                            <hr class="mt-35">
                        </div>
                    </div>
                    <div class="row categories">
                        <div class="col">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-circle-info"></i></div>
                                <div class="col">
                                    <span class="title">Tags</span><br>
                                    <span class="description"><?php echo $notice->tags . ', ' . $areas ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-calendar-days"></i></div>
                                <div class="col">
                                    <span class="title">Data de início</span><br>
                                    <span class="description"><?php echo $notice->start_date ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-calendar-days"></i></div>
                                <div class="col">
                                    <span class="title">Data de encerramento</span><br>
                                    <span class="description"><?php echo $notice->end_date ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-35"><hr></div>
                        <div class="col"></div>
                        <div class="col-3 col-md-4">
                            <button type="button" data-bs-toggle="modal" data-bs-target="#alertConsultant" class="btn btn-primary" >
                                <i class="fa-light fa-comment-dots"></i> Falar com um consultor
                            </button>
                        </div>
                        <div class="col-12 mt-35 mb-35"><hr></div>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <p><?php echo nl2br($notice->description) ?></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-35"><hr></div>
                        <div class="col-2">
                            <a href="<?php echo $notice->origin ?>" target="_blank" class="btn btn-primary">Fonte</a>
                        </div>
                        <?php foreach ($externalLinks as $link): ?>
                            <div class="col-2">
                                <a href="<?php echo $link->url ?>" target="_blank" class="btn btn-primary">Mais info</a>
                            </div>
                        <?php endforeach?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="alertConsultant" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="alertLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="alertLabel">Falar com um consultor</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <?php echo form_open('alerts/ask-for-help') ?>
                <input type="hidden" name="Aviso" value="<?php echo current_url() ?>">
                <input type="hidden" name="Nome" value="<?php echo $notice->title ?>">
                <div class="modal-body">
                    <div class="row">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="basic-addon1"><i class="fa-light fa-envelope"></i></span>
                            <input required value="<?php echo auth()->user()->email ?>" type="email" placeholder="email" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <label for="Contacto alternativo" class="form-label">Contacto alternativo</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="basic-addon1"><i class="fa-light fa-address-book"></i></span>
                            <input name="Contacto alternativo" type="text" placeholder="email / Tlm / Tlf" class="form-control">
                        </div>
                    </div>
                    <div class="row">
                        <label for="Mensagem" class="form-label">Mensagem</label>
                        <div class="input-group mb-3">
                            <textarea rows="6" name="Mensagem" required class="form-control"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Enviar pedido</button>
                </div>
            <?php echo form_close() ?>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>