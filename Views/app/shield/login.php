<?php echo $this->extend('layouts/auth') ?>

<?php echo $this->section('content') ?>
<?php echo form_open('auth/login') ?>
    <div class="form-header text-center">
        <img class="mb-4" src="<?php echo base_url('dist/images/logo.svg'); ?>" alt="Radar Fundos Europeus" width="380">
        <p class="mb-3 fw-normal">Faça o login para usar a sua conta.</p>
        <hr>
        <ul class="nav" id="pills-tab" role="tablist">
            <li class="pr-30">
                <button class="nav-link active" id="pills-company-tab" data-bs-toggle="pill" data-bs-target="#pills-company" type="button" role="tab" aria-controls="pills-company" aria-selected="true"><i class="fa-regular fa-circle"></i> Entidade</button>
            </li>
            <li>
                <button class="nav-link" id="pills-user-tab" data-bs-toggle="pill" data-bs-target="#pills-user" type="button" role="tab" aria-controls="pills-user" aria-selected="false"><i class="fa-regular fa-circle"></i> Utilizador</button>
            </li>
        </ul>
        <hr>
    </div>
    <div class="tab-content" id="pills-tabContent">
        <!-- FORM COMPANY -->
        <div class="tab-pane fade show active" id="pills-company" role="tabpanel" aria-labelledby="pills-company-tab">
            <div class="mb-3">
                <label for="username" class="form-label">NIF</label>
                <input type="text" class="form-control" name="username" minlength="9" maxlength="9" aria-describedby="NIF" placeholder="Introduza o nº de contribuinte">
            </div>
        </div>
        <!-- FORM USER -->
        <div class="tab-pane fade" id="pills-user" role="tabpanel" aria-labelledby="pills-user-tab">
            <div class="mb-3">
                <label for="email" class="form-label">E-mail</label>
                <input type="text" class="form-control" name="email" aria-describedby="email" placeholder="Introduza o e-mail">
            </div>
        </div>
    </div>
    <label for="password" class="form-label">Password</label>
    <div class="input-group mb-3">
        <input type="password" class="form-control" name="password" placeholder="Introduza a password">
        <span class="input-group-text">
            <i class="fa-regular fa-eye-slash" id="togglePassword"></i>
        </span>
    </div>
    <div class="mb-4 form-check pl-0">
        <div class="row">
            <div class="col-6 text-start">
                <input type="checkbox" name="chk_remember">
                <label class="form-check-label" for="exampleCheck1">Lembrar-me</label>
            </div>
            <div class="col-6 text-end">
                <a href="<?php echo site_url('auth/forgot-password') ?>" class="text-end"><u>Esqueceu-se da password</u></a>
            </div>
        </div>
    </div>
    <button type="submit" class="btn btn-primary">Entrar</button>
    <a href="<?php echo site_url('auth/register') ?>" class="btn btn-secundary d-flex align-center justify-content-center align-items-center" role="button">Criar conta</a>
    <?php if (!empty(session()->has('error'))): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erro!</strong> <?php echo session()->getFlashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('confirm'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>Sucesso!</strong> <?php echo session()->getFlashdata('confirm'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
<?php echo form_close() ?>
<?php echo $this->endSection() ?>