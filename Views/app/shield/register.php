<?php echo $this->extend('layouts/auth') ?>

<?php echo $this->section('content') ?>
<?php echo form_open('auth/register') ?>
    <div class="form-header text-center">
        <img class="mb-4" src="<?php echo base_url('dist/images/logo.svg'); ?>" alt="Radar Fundos Europeus" width="380">
        <p class="mb-3 fw-normal">Faça o registo para ter acesso à área reservada.</p>
        <hr>
    </div>
    <div class="mb-3">
        <label for="username" class="form-label">E-mail</label>
        <input type="text" class="form-control" name="email" aria-describedby="email" placeholder="Introduza o e-mail">
    </div>
    <div class="mb-3">
        <label for="username" class="form-label">NIF</label>
        <input type="text" class="form-control" minlength="9" maxlength="9" name="username" aria-describedby="NIF" placeholder="Introduza o nº de contribuinte">
    </div>
    <label for="password" class="form-label">Password</label>
    <div class="input-group mb-3">
        <input type="password" minlength="8" class="form-control" name="password" placeholder="Password">
        <span class="input-group-text">
            <i class="fa-regular fa-eye-slash" id="togglePassword"></i>
        </span>
    </div>
    <label for="password_confirm" class="form-label">Confirmar password</label>
    <div class="input-group mb-3">
        <input type="password" minlength="8" class="form-control" name="password_confirm"  placeholder="Confirmar password">
        <span class="input-group-tssext">
            <i class="fa-regular fa-eye-slash" id="togglePassword"></i>
        </span>
    </div>
    <button type="submit" class="btn btn-secundary">Criar conta</button>
    <?php if (!empty(session()->has('error'))): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Erro!</strong> <?php echo session()->getFlashdata('error'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <?php if (!empty(session()->has('confirm'))): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo session()->getFlashdata('confirm'); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    <?php endif?>
    <i class="fa fa-arrow-left" aria-hidden="true"></i> <a href="<?php echo site_url('auth/login') ?>">Ir para o login</a>
</form>
<?php echo form_close() ?>
<?php echo $this->endSection() ?>