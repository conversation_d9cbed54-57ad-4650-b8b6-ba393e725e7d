<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col">
            <h1>Dados de perfil</h1>
            <h5>Aqui pode atualizar os seus dados</h5>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <?php echo form_open_multipart('auth/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $user->id ?? null ?>">
                        <input type="hidden" name="user_detail_id" value="<?php echo $userDetailId ?? null ?>">
                        <div class="pl-0 fs-25 mb-3">
                            <a href="<?php echo previous_url() ?>" class="back">
                                <i class="fa-sharp fa-light fa-circle-arrow-left"></i>
                            </a>
                        </div>
                        <h5><b>Dados de acesso</b></h5>
                        <div class="row mb-3">
                            <?php if (auth()->user()->inGroup('company')): ?>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="username" class="form-label">NIF</label>
                                    <input type="text" name="username" minlength="9" maxlength="9" class="form-control" aria-describedby="username" value="<?php echo set_value('username', $user->username ?? null) ?>" placeholder="Introduza o número de contribuinte" required>
                                </div>
                            </div>
                            <?php endif?>
                            <div class="<?php if (auth()->user()->inGroup('company')): ?>col-md-3<?php else: ?>col-md-4<?php endif?> col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">E-mail</label>
                                    <input type="email" name="email" class="form-control" aria-describedby="email" value="<?php echo set_value('email', $user->email ?? null) ?>" placeholder="Introduza o e-mail"required>
                                </div>
                            </div>
                            <div class="<?php if (auth()->user()->inGroup('company')): ?>col-md-3<?php else: ?>col-md-4<?php endif?> col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" name="password" class="form-control" minlength="8" placeholder="Introduza a password" <?php if (empty($user->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                            <div class="<?php if (auth()->user()->inGroup('company')): ?>col-md-3<?php else: ?>col-md-4<?php endif?> col-sm-12">
                                <div class="form-group">
                                    <label for="password" class="form-label">Confirmar Password</label>
                                    <input type="password" name="password_confirm" class="form-control" minlength="8" placeholder="Confirmar a password" <?php if (empty($user->id)): ?> required <?php endif;?>>
                                </div>
                            </div>
                        </div>
                        <?php if (auth()->user()->inGroup('company')): ?>
                        <h5><b>Dados de subscrição</b></h5>
                        <div class="row mb-3">
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="start_date" class="form-label">Data de Início</label>
                                    <input type="date" placeholder="dd-mm-yyyy" name="start_date" class="form-control" value="<?php echo set_value('start_date', $user->start_date ?? null) ?>" disabled>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="email" class="form-label">Data de Vencimento</label>
                                    <input type="date" name="due_date" class="form-control" value="<?php echo set_value('due_date', $user->due_date ?? null) ?>" disabled>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <div class="form-group">
                                    <label for="max_employees" class="form-label">Nº Máx. de Utilizadores</label>
                                    <input type="number" name="max_employees" class="form-control" placeholder="Introduza o número máximo de Utilizadores" min="0" value="<?php echo set_value('max_employees', $user->max_employees ?? null) ?>" disabled>
                                </div>
                            </div>
                        </div>
                        <div class="row mb-4">
                            <div class="col-md-6 col-xs-12">
                                <h5><b>Geografia</b></h5>
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="city_id" class="form-label">Geografia</label>
                                            <select required name="city_id" class="form-control">
                                                <option></option>
                                                <?php foreach ($districts as $district): ?>
                                                    <optgroup label="<?php echo $district->name ?>">
                                                        <?php foreach ($district->cities as $city): ?>
                                                            <option <?php if (isset($user->city_id) && $user->city_id === $city->id): ?>selected<?php endif?> value="<?php echo $city->id ?>"><?php echo $city->name ?></option>
                                                        <?php endforeach?>
                                                    </optgroup>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="cim_id" class="form-label">Comunidade Inter Municipal</label>
                                            <select required name="cim_id" class="form-control">
                                                <option></option>
                                                <?php foreach ($cims as $cim): ?>
                                                    <option <?php if (isset($cim->id) && isset($user) && $user->cim_id === $cim->id): ?>selected<?php endif?> value="<?php echo $cim->id ?>"><?php echo $cim->name ?></option>
                                                <?php endforeach?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-xs-12">
                                <h5><b>Entidade</b></h5>
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <div class="form-group">
                                            <label for="image" class="form-label">Logotipo</label>
                                            <?php if (!empty($user->image)): ?>
                                                <div class="img-company">
                                                    <a class="mr-2" href="<?php echo base_url('uploads/users/images/' . ($user->image)) ?>" target="_blank"><img src="<?php echo base_url('uploads/users/images/' . ($user->image)) ?>" class=""/></a>
                                                    <a class="delete-img-company" onclick="return confirmDelete();" href="<?php echo site_url('auth/delete-img/' . $user->userDetailId) ?>"><i class="fa-duotone fa-trash"></i></a>
                                                </div>
                                            <?php else: ?>
                                                <input type="file" class="form-control" name="image"/>
                                            <?php endif?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php elseif (auth()->user()->inGroup('employee')): ?>
                            <?php $image = model('\App\Models\UserDetailModel')->where('user_id', $user->company_id)->first()->image;?>
                            <?php if (!empty($image)): ?>
                                <h5><b>Entidade</b></h5>
                                <div class="row mb-5">
                                    <div class="col-md-4 col-sm-12">
                                        <div class="form-group">
                                            <label for="image" class="form-label">Logotipo</label>
                                                <div class="img-company">
                                                    <a class="mr-2" href="<?php echo base_url('uploads/users/images/' . ($image)) ?>" target="_blank"><img src="<?php echo base_url('uploads/users/images/' . ($image)) ?>" class=""/></a>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif;?>
                        <?php endif;?>
                        <div class="row">
                            <div class="col-xs-12 col-md-2">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>