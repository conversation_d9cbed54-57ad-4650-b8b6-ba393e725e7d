<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col">
            <h1>Notícias</h1>
            <h5>Pesquise <PERSON><PERSON><PERSON>s</h5>
        </div>
    </div>
        <?php echo form_open('articles', ['method' => 'get']) ?>
        <div class="row article-filters">
            <div class="<?php if (isset($_GET['search'])): ?>col-md-9<?php else: ?>col-md-10<?php endif;?> col-xs-12">
                <div class="input-group height-50">
                    <span class="input-group-text" id="basic-addon1">
                        <i class="fa-light fa-magnifying-glass"></i>
                    </span>
                    <input type="text" name="search" class="form-control h-100" placeholder="Procurar" value="<?php echo !empty($_GET['search']) ? $_GET['search'] : '' ?>">
                </div>
            </div>

            <div class="<?php if (isset($_GET['search'])): ?>col-md-3<?php else: ?>col-md-2<?php endif;?> col-xs-12 d-flex justify-content-between mobile-direction-column mobile-mt-20">
                <button class="btn btn-primary border-0" type="submit"><i class="fa-light fa-magnifying-glass"></i></button>
                <?php if (isset($_GET['search'])): ?>
                    <a href="<?php echo site_url('articles'); ?>" class="btn btn-light d-flex align-items-center justify-content-center"><i class="fa-light fa-broom-wide"></i>&nbsp;Limpar filtros</a>
                <?php endif;?>
            </div>
        </div>
        <?php echo form_close() ?>
    <div class="row mt-50 mobile-mt-20">
        <?php foreach ($articles as $article): ?>
        <div class="col-xs-12 col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body article-item d-flex flex-column h-100 justify-content-between">
                    <div>
                        <div class="col-12 icon article-card-img h-150" style="background-image: url(<?php echo base_url('uploads/images/' . ($article->image)) ?>)"></div>
                        <h5 class="mt-3 w700 article-card-title">
                            <a class="color-blue"href="<?php echo site_url('articles/detail/' . $article->slug); ?>"><?php echo substr($article->title, 0, 150) ?></a>
                        </h5>
                        <h5 class="mb-3 article-card-intro">
                            <a href="<?php echo site_url('articles/detail/' . $article->slug); ?>"><?php echo substr($article->intro, 0, 300) ?></a>
                        </h5>
                        <?php echo view_cell('\Admin\Cells\ArticleCell::tags', ['tags' => $article->tags]) ?>
                    </div>
                    <div>
                        <hr>
                        <div class="d-flex justify-content-between">
                            <a class="w700 color-blue" href="<?php echo site_url('articles/detail/' . $article->slug); ?>">Saiba mais</a>
                            <div class="col-6 text-end">
                                <?php echo date('d-m-Y', strtotime($article->schedule)); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach?>
    </div>
    <?php if (count($articles) === 0): ?>
        <div class="text-center">
            Não foram encontradas notícias
            <?php if (isset($_GET['search'])): ?>
                para a sua pesquisa. <br><a class="btn-link" href="<?php echo site_url('articles'); ?>">Ver todas</a>
            <?php endif;?>
        </div>
    <?php endif;?>
    <div class="d-flex justify-content-end mt-50 div-pagination">
        <?php if ($pager): ?>
            <?php echo $pager->links() ?>
        <?php endif?>
    </div>
</div>
<?php echo $this->endSection() ?>