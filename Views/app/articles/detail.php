<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col">
            <h1><?php echo $article->title ?></h1>
            <h5>Veja em detalhe</h5>
        </div>
    </div>
    <div class="row notice">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body notice-item">
                    <div class="row">
                        <div class="col pl-0">
                            <a href="<?php echo previous_url() ?>" class="back">
                                <i class="fa-sharp fa-light fa-circle-arrow-left"></i>
                            </a>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12 article-card-img h-300" style="background-image: url(<?php echo base_url('uploads/images/' . ($article->image)) ?>)">
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-12 pl-0">
                            <h3 class="mt-3 mobile-fs-20"><?php echo $article->intro ?></h3>
                            <h3 class="mb-3 article-detail-description"><?php echo $article->description ?></h3>
                            <hr class="mt-35">
                        </div>
                    </div>
                    <div class="row mt-50 mobile-mt-20">
                        <?php if (!empty($article->source)): ?>
                        <div class="col-md-3 col-xs-12">
                            <a href="<?php echo $article->source ?>" class="btn btn-primary"><i class="fa-light fa-arrow-up-right-from-square"></i>CONSULTAR FONTE</a>
                        </div>
                        <?php endif;?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>