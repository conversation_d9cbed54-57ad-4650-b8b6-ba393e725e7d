<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title><?php echo $program->programaOperacionalDesignacao ?></title>
        <!-- STYLES -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-rbsA2VBKQhggwzxH7pPCaAqO46MgnOM80zW1RWuH61DGLwZJEdK2Kadq2F9CUG65" crossorigin="anonymous">
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.7/css/all.css">
        <link href="<?php echo base_url('dist/css/style.css'); ?>" rel="stylesheet">
        <style type="text/css">
            h2, h3 {
                color: #0433BF;
            }
            .table-1 h2 {
                padding-top: 20px;
            }
            .document-btn {
                padding: 10px 30px;
                border: 1px solid #000;
                color: #000;
                text-decoration: none;
            }
            footer {
                position: fixed;
                bottom: 0px;
                left: 0px;
                right: 0px;
                color: black;
                text-align: center;
                line-height: 35px;
            }
            .table-spacing {
                border-collapse: separate;
                border-spacing: 10px 20px;
            }
            .table-1 .document-btn {
                margin-bottom: 20px !important;
            }
            .img-icon {
                margin-right: 10px;
                margin-bottom: -15px;
            }
            .table-column {
                width: 50%;
                vertical-align: top;
            }
          </style>
    </head>
    <body>
        <div>
            <table cellspacing="0" width="100%" class="table-1">
                <tr>
                    <td align="center"><img src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/logo.png']) ?>"></td>
                </tr>
                <tr>
                    <td><h2 style="margin-bottom: 1px;"><?php echo $program->programaOperacionalDesignacao ?></h2></td>
                </tr>
                <tr>
                    <td><h3><?php echo $notice->designacaoPT ?></h3></td>
                </tr>
                <tr>
                    <td>
                        <span><b>Tipo de financiamento</b></span><br>
                        <span><?php echo $program->tipoFinanciamentoDesignacao ?? '--' ?></span>
                        <br/>
                    </td>
                </tr>
            </table>
            <hr class="solid">
            <table class="table-spacing" width="100%">
                <tr>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" width="20" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/lines-leaning.png']) ?>">
                            <span><b>Prioridade</b></span>
                            <div style="margin-left: 35px;"><?php echo $program->prioridadeDesignacao ?? '--' ?></div>
                        </div>
                    </td>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/arrow-right.png']) ?>">
                            <span><b>Ação</b></span>
                            <div style="margin-left: 40px;"><?php echo $program->tipologiaAcaoDesignacao ?? '--' ?></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/bullseye.png']) ?>">
                            <span><b>Objetivo</b></span>
                            <div style="margin-left: 40px;"><?php echo $program->objetivoEspecificoDesignacao ?? '--' ?></div>
                        </div>
                    </td>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/life-ring.png']) ?>">
                            <span><b>Classificação</b></span>
                            <div style="margin-left: 40px;"><?php echo $notice->classificacaoAvisoDesignacao ?? '--' ?></div>
                        </div>
                    </td>
                </tr>
            </table>
            <hr class="solid">
            <table class="table-spacing" width="100%">
                <tr>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/circle-info.png']) ?>">
                            <span><b>Tipo de intervenção</b></span>
                            <div style="margin-left: 40px;"><?php echo $program->tipologiaIntervencaoDesignacao ?? '--' ?></div>
                        </div>
                    </td>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/sack-dollar.png']) ?>">
                            <span><b>Dotação orçamental</b></span>
                            <div style="margin-left: 40px;"><?php echo $formatter->formatCurrency($program->dotacao, 'EUR'), PHP_EOL; ?></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td class="table-column">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/calendar-days.png']) ?>">
                            <span><b>Data de início</b></span>
                            <div style="margin-left: 35px;"><?php echo $notice->dataInicio ?? '--' ?></div>
                        </div>
                    </td>
                    <td style="width: 50%;">
                        <div>
                            <img class="img-icon" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'dist/images/icons/calendar-days.png']) ?>">
                            <span><b>Data de encerramento</b></span>
                            <div style="margin-left: 35px;"><?php echo $notice->dataFim ?? '--' ?></div>
                        </div>
                    </td>
                </tr>
            </table>
            <?php if (count($documents) > 0): ?>
                <hr class="solid">
                <table class="table-spacing" width="100%">
                    <?php foreach ($documents as $document): ?>
                    <tr>
                        <td>
                           <span><b><?php echo $document->tipoDocumentoDesignacao ?></b></span>
                        </td>
                        <td>
                            <a class="document-btn" href="<?php echo site_url('notices/documents/download/' . $document->id) ?>">Obter documento</a>
                        </td>
                    </tr>
                    <?php endforeach?>
                </table>
            <?php endif;?>
            <?php if (count($texts) > 0): ?>
                <hr class="solid">
                <table class="table-spacing" width="100%">
                    <?php foreach ($texts as $text): ?>
                    <tr>
                        <td>
                           <strong><?php echo $text->title ?></strong><br>
                            <?php echo nl2br($text->content) ?>
                            <br/>
                        </td>
                    </tr>
                    <?php endforeach?>
                </table>
            <?php endif;?>
            <hr class="solid">
            <table class="table-spacing" width="100%">
                <tr>
                    <td align="center">
                        <a target="_blank" class="document-btn" href="https://portugal2030.pt/avisos/">CONSULTAR FONTE</a>
                    </td>
                    <td align="center">
                        <a target="_blank" class="document-btn" href="<?php echo site_url('notices/detail/' . $program->id) ?>">VER AVISO NA PLATAFORMA</a>
                    </td>
                </tr>
            </table>
            <hr class="solid">
            <?php if (!empty($program->image)): ?>
            <table class="table-spacing" width="100%">
                <tr>
                    <td align="center">
                        <img src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'uploads/programs/images/' . $program->image]) ?>" width="400">
                    </td>
                </tr>
            </table>
            <?php endif;?>
            <footer>
                <?php if (!empty($companyLogo)): ?>
                <table class="table-spacing" width="100%">
                    <tr>
                        <td align="center">
                            <strong>Gerado por</strong>
                            <br/>
                            <img style="margin-top: 10px;" src="<?php echo view_cell('\App\Cells\NoticeCell::encodeImage', ['url' => 'uploads/users/images/' . $companyLogo]) ?>" width="400">
                        </td>
                    </tr>
                </table>
                <?php endif;?>
                Esta informação pertence ao Radar dos Fundos Europeus
            </footer>
        </div>
        <!-- JAVASCRIPT -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-kenU1KFdBIe4zVF0s0G1M5b4hcpxyD9F7jL+jjXkk+Q2h455rYXK/7HAuoJl+0I4" crossorigin="anonymous"></script>
        <script src="https://kit.fontawesome.com/759920ea2b.js" crossorigin="anonymous"></script>
        <?php echo $this->renderSection('scripts') ?>
    </body>
</html>