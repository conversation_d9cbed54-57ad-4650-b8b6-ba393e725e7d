<?php echo $this->extend($layout) ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col">
            <h1><?php echo $program->tipoFinanciamentoDesignacao ?></h1>
            <h5>Veja em detalhe</h5>
        </div>
    </div>
    <!--
    <div class="btn-alert">
        <label for=""></label>
        <button  type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#alert">
        <i class="fa-light fa-triangle-exclamation"></i> Criar alerta
        </button>
    </div>
    -->
    <div class="row notice">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body notice-item">
                    <div class="row">
                        <div class="col pl-0">
                            <a href="<?php echo site_url('notices') ?>" class="back">
                                <i class="fa-sharp fa-light fa-circle-arrow-left"></i>
                            </a>
                        </div>
                        <?php if (auth()->user()): ?>
                            <div class="col-md-auto <?php if ($isBookmarked > 0): ?>active<?php endif?> text-end bookmark">
                                <?php if ($isBookmarked > 0): ?>
                                    <a href="<?php echo site_url('bookmarks/remove/' . $program->id) ?>"><i class="fa-light fa-bookmark"></i></a>
                                <?php else: ?>
                                    <a href="<?php echo site_url('bookmarks/save/' . $program->id) ?>"><i class="fa-light fa-bookmark"></i></a>
                                <?php endif;?>
                            </div>
                        <?php endif?>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <h2 class="mt-3"><?php echo $program->programaOperacionalDesignacao ?></h5>
                            <h3 class="mb-3"><?php echo $notice->designacaoPT ?></h5>
                            <hr class="mt-35">
                        </div>
                    </div>
                    <div class="row categories">
                        <div class="col-md-3 col-xs-12 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-thin fa-lines-leaning"></i></div>
                                <div class="col col-xs-12">
                                    <span class="title">Prioridade</span><br>
                                    <span class="description"><?php echo $program->prioridadeDesignacao ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-solid fa-arrow-up-right-dots"></i></div>
                                <div class="col col-xs-12">
                                    <span class="title">Ação</span><br>
                                    <span class="description"><?php echo $program->tipologiaAcaoDesignacao ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-bullseye"></i></div>
                                <div class="col">
                                    <span class="title">Objetivo</span><br>
                                    <span class="description"><?php echo $program->objetivoEspecificoDesignacao ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-life-ring"></i></div>
                                <div class="col">
                                    <span class="title">Classificação</span><br>
                                    <span class="description"><?php echo $notice->classificacaoAvisoDesignacao ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12"><hr></div>
                    </div>
                    <div class="row categories">
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-circle-info"></i></div>
                                <div class="col">
                                    <span class="title">Tipo de intervenção</span><br>
                                    <span class="description"><?php echo $program->tipologiaIntervencaoDesignacao ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center ">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-sack-dollar"></i></div>
                                <div class="col">
                                    <span class="title">Dotação orçamental</span><br>
                                    <span class="description"><?php echo $formatter->formatCurrency($program->dotacao, 'EUR'), PHP_EOL; ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-calendar-days"></i></div>
                                <div class="col">
                                    <span class="title">Data de início</span><br>
                                    <span class="description"><?php echo $notice->dataInicio ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-xs-12 mobile-mt-20 mobile-text-center">
                            <div class="row">
                                <div class="col-md-auto"><i class="fa-light fa-calendar-days"></i></div>
                                <div class="col">
                                    <span class="title">Data de encerramento</span><br>
                                    <span class="description"><?php echo $notice->dataFim ?? '--' ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mb-35"><hr></div>
                        <?php if (auth()->user()): ?>
                            <div class="col-2">
                                <a href="<?php echo site_url('notices/documents/export/national/' . $program->id) ?>" class="btn btn-primary" >
                                    <i class="fa-light fa-file-pdf"></i> Download
                                </a>
                            </div>
                            <div class="col-2">
                                <button data-bs-toggle="modal" data-bs-target="#share" class="btn btn-primary" >
                                    <i class="fa-light fa-share-nodes"></i> Partilhar
                                </button>
                            </div>
                        <?php endif?>
                        <div class="col"></div>
                        <div class="col-md-4 col-xs-12">
                            <?php if (auth()->user()): ?>
                                <button type="button" data-bs-toggle="modal" data-bs-target="#alertConsultant" class="btn btn-primary" >
                                    <i class="fa-light fa-comment-dots"></i> Falar com um consultor
                                </button>
                            <?php else: ?>
                                <a href="<?php echo site_url('auth/login') ?>" class="btn btn-primary" >
                                    <i class="fa-light fa-comment-dots"></i> Falar com um consultor
                                </a>
                            <?php endif?>
                        </div>
                        <div class="col-12 mt-35 mb-35"><hr></div>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <?php foreach ($texts as $text): ?>
                                <strong><?php echo $text->title ?></strong><br>
                                <?php echo nl2br($text->content) ?>
                                <br><br>
                            <?php endforeach?>
                        </div>
                    </div>
                    <?php if (auth()->user()): ?>
                        <div class="row">
                            <div class="col-12 mb-35"><hr></div>
                            <?php foreach ($documents as $document): ?>
                                <?php echo view_cell('\App\Cells\NoticeCell::download', ['document' => $document]) ?>
                            <?php endforeach?>
                        </div>
                        <div class="row mt-50">
                            <div class="col-sm-12 col-md-4">
                                <a href="https://portugal2030.pt/avisos/" class="btn btn-primary"><i class="fa-light fa-arrow-up-right-from-square"></i>CONSULTAR FONTE</a>
                            </div>
                            <div class="col-sm-12 col-md-4">
                                <?php if ($program->image): ?>
                                <img src="<?php echo base_url('uploads/programs/images/' . ($program->image)) ?>" alt="" class="img-fluid ">
                                <?php endif;?>
                            </div>
                        </div>
                    <?php endif?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (auth()->user()): ?>
    <div class="modal fade" id="alertConsultant" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="alertLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="alertLabel">Falar com um consultor</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <?php echo form_open('alerts/ask-for-help') ?>
                    <input type="hidden" name="Aviso" value="<?php echo current_url() ?>">
                    <input type="hidden" name="Programa" value="<?php echo $program->programaOperacionalDesignacao ?>">
                    <input type="hidden" name="Designação" value="<?php echo $notice->designacaoPT ?>">
                    <div class="modal-body">
                        <div class="row">
                            <label for="email" class="form-label">Email</label>
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1"><i class="fa-light fa-envelope"></i></span>
                                <input required value="<?php echo auth()->user()->email ?>" type="email" placeholder="email" class="form-control">
                            </div>
                        </div>
                        <div class="row">
                            <label for="Contacto alternativo" class="form-label">Contacto alternativo</label>
                            <div class="input-group mb-3">
                                <span class="input-group-text" id="basic-addon1"><i class="fa-light fa-address-book"></i></span>
                                <input name="Contacto alternativo" type="text" placeholder="email / Tlm / Tlf" class="form-control">
                            </div>
                        </div>
                        <div class="row">
                            <label for="Mensagem" class="form-label">Mensagem</label>
                            <div class="input-group mb-3">
                                <textarea rows="6" name="Mensagem" required class="form-control"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Enviar pedido</button>
                    </div>
                <?php echo form_close() ?>
            </div>
        </div>
    </div>

    <?php echo view('app/cells/share') ?>
<?php endif?>
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
    <script src="https://cdn.jsdelivr.net/npm/sharer.js@latest/sharer.min.js"></script>
<?php echo $this->endSection() ?>
