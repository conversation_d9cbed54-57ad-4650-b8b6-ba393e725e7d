<?php echo $this->extend('layouts/app') ?>
<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col col-lg-5">
            <h1>Avisos</h1>
            <h5>Pesquise os avisos</h5>
        </div>
        <div class="col col-lg-3">
            <a href="<?php echo site_url((($page === 'bookmarks') ? 'bookmarks' : 'notices')) ?>" class="btn btn-default" >
                <i class="fa-light fa-flag"></i> Portugal 2030
            </a>
        </div>
        <div class="col col-lg-2">
            <a href="<?php echo site_url((($page === 'bookmarks') ? 'bookmarks' : 'notices') . '/prr') ?>" class="btn btn-default" >
                <i class="fa-light fa-flag"></i> PRR
            </a>
        </div>
        <div class="col col-lg-2">
            <a href="<?php echo site_url((($page === 'bookmarks') ? 'bookmarks' : 'notices') . '/europe') ?>" class="btn btn-primary" >
                <i class="fa-light fa-earth-americas"></i> Europeus
            </a>
        </div>
    </div>
    <!--
    <div class="btn-alert">
        <label for=""></label>
        <button  type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#alert">
        <i class="fa-light fa-triangle-exclamation"></i> Criar alerta
        </button>
    </div> -->
    <div class="row">
        <?php if ($page !== 'bookmarks'): ?>
            <div class="col-12 filters">
                <ul>
                    <li>
                        <a data-bs-toggle="collapse" href="#filters" role="button" aria-expanded="false" aria-controls="filters">
                            <i class="fa-light fa-filter"></i> Filtrar <i class="fa-sharp fa-light fa-chevron-down"></i>
                        </a>
                    </li>
                </ul>
                <form class="collapse mt-3" id="filters">
                    <input type="hidden" name="clearUp" value="0">
                    <div class="card card-body">
                        <div class="row">
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Ordenar</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-calendar-days"></i>
                                    </span>
                                    <select name="orderBy" class="form-select">
                                        <option value="" disabled selected class="placeholder">Selecione</option>
                                        <option <?php echo selected($_GET, 'orderBy', 'id') ?> value="id">Mais recente</option>
                                        <option <?php echo selected($_GET, 'orderBy', 'date') ?> value="date">Data de conclusão</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6 col-xs-12">
                                <label for="" class="form-label">&nbsp;</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-magnifying-glass"></i>
                                    </span>
                                   <input type="text" value="<?php echo $_GET['search'] ?? null ?>" name="search" class="form-control" placeholder="Procurar...">
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Data limite</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-calendar-days"></i>
                                    </span>
                                   <input value="<?php echo $_GET['dateEnd'] ?? null ?>" name="dateEnd" type="text" class="form-control datePickerClose" placeholder="Selecione"/>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Programa de Apoio</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <select name="program_id" class="form-select">
                                        <option value="" disabled selected class="placeholder">Selecione</option>
                                        <?php foreach ($programs as $program): ?>
                                            <option <?php echo selected($_GET, 'program_id', $program->id) ?> value="<?php echo $program->id ?>"><?php echo $program->program ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Organismo</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <select name="organism_id" class="form-select">
                                        <option value="" disabled selected class="placeholder">Selecione</option>
                                        <?php foreach ($organisms as $organism): ?>
                                            <option <?php echo selected($_GET, 'organism_id', $organism->id) ?> value="<?php echo $organism->id ?>"><?php echo $organism->entity ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Setores</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <button class="form-control dropdown-toggle text-start" type="button" id="multiSelectDropdown" data-bs-toggle="dropdown" aria-expanded="false">Selecione</button>
                                    <ul class="dropdown-menu" aria-labelledby="multiSelectDropdown">
                                        <?php foreach ($sectors as $sector): ?>
                                            <li>
                                                <label>
                                                    <input <?php echo checked($_GET, 'sectors', $sector->id) ?> name="sectors[]" type="checkbox" value="<?php echo $sector->id ?>"><?php echo $sector->name ?>
                                                </label>
                                            </li>
                                        <?php endforeach?>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Temáticas</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <button class="form-control dropdown-toggle text-start" type="button" id="multiSelectDropdown" data-bs-toggle="dropdown" aria-expanded="false">Selecione</button>
                                    <ul class="dropdown-menu" aria-labelledby="multiSelectDropdown">
                                        <?php foreach ($thematics as $thematic): ?>
                                            <li>
                                                <label>
                                                    <input <?php echo checked($_GET, 'thematics', $thematic->id) ?> name="thematics[]" type="checkbox" value="<?php echo $thematic->id ?>"><?php echo $thematic->name ?>
                                                </label>
                                            </li>
                                        <?php endforeach?>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-4 col-lg-3 col-xs-12">
                                <label for="" class="form-label">Tipo de Entidade</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <button class="form-control dropdown-toggle text-start" type="button" id="multiSelectDropdown" data-bs-toggle="dropdown" aria-expanded="false">Selecione</button>
                                    <ul class="dropdown-menu" aria-labelledby="multiSelectDropdown">
                                        <?php foreach ($types as $type): ?>
                                            <li>
                                                <label>
                                                    <input <?php echo checked($_GET, 'types', $type->id) ?> name="types[]" type="checkbox" value="<?php echo $type->id ?>"><?php echo $type->name ?>
                                                </label>
                                            </li>
                                        <?php endforeach?>
                                    </ul>
                                </div>
                            </div>
                            <div class="col-md-9 col-lg-9 col-xs-12">
                                <label for="" class="form-label">Comunidade Inter-municipal</label>
                                <div class="input-group mb-3">
                                    <span class="input-group-text" id="basic-addon1">
                                        <i class="fa-light fa-list"></i>
                                    </span>
                                    <button class="form-control dropdown-toggle text-start" type="button" id="multiSelectDropdown" data-bs-toggle="dropdown" aria-expanded="false">Selecione</button>
                                    <ul class="dropdown-menu" aria-labelledby="multiSelectDropdown">
                                        <?php foreach ($cims as $cim): ?>
                                            <li>
                                                <label>
                                                    <input <?php echo checked($_GET, 'cims', $cim->id) ?> name="cims[]" type="checkbox" value="<?php echo $cim->id ?>"><?php echo $cim->name ?>
                                                </label>
                                            </li>
                                        <?php endforeach?>
                                    </ul>
                                </div>
                            </div>
                            <div class="mt-30 col-md-4 col-lg-3 mobile-filter-btns">
                                <a href="<?php echo site_url('notices?clearUp=1') ?>" class="btn btn-primary mobile-w-100"><i class="fa-light fa-broom-wide"></i></a>
                                <button type="submit" class="btn btn-primary mobile-w-100"><i class="fa-light fa-magnifying-glass"></i></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-12 mt-35">
                Encontrámos <?php echo $notices['pagination']->getTotal() ?> artigos
            </div>
            <?php echo view_cell('App\Cells\FiltersCell::show') ?>
        <?php endif?>
    </div>
    <div class="row mt-50 mobile-mt-20">
        <?php foreach ($notices['data'] as $notice): ?>
        <div class="col-md-4 col-xs-12">
            <div class="card mb-4">
                <div class="card-body notice-item">
                    <div class="row">
                        <div class="col-4 icon">
                            <?php echo $notice->icon ?? '<i class="fa-light fa-vector-circle"></i>' ?>
                        </div>
                        <div class="col-6"></div>
                        <?php if (isset($notice->bookmarks)): ?>
                            <div class="col-2 text-end <?php if ($notice->bookmarks > 0): ?>active<?php endif?> bookmark">
                                <?php if ($notice->bookmarks > 0): ?>
                                    <a href="<?php echo site_url('bookmarks/remove/' . $notice->id . '?type=europe') ?>"><i class="fa-light fa-bookmark"></i></a>
                                <?php else: ?>
                                    <a href="<?php echo site_url('bookmarks/save/' . $notice->id . '?type=europe') ?>"><i class="fa-light fa-bookmark"></i></a>
                                <?php endif;?>
                            </div>
                        <?php endif?>
                    </div>
                    <div class="row">
                        <div class="col-12 pl-0">
                            <h5 class="mt-3 w700">
                                <a href="<?php echo site_url('notices/europe/detail/' . $notice->id . '/' . slug($notice->code)); ?>">
                                    <?php echo $notice->code ?>
                                </a>
                            </h5>
                            <h5 class="mb-3">
                                <a href="<?php echo site_url('notices/europe/detail/' . $notice->id . '/' . slug($notice->code)); ?>">
                                    <?php echo $notice->name ?>
                                </a>
                            </h5>
                            <?php foreach ($programs as $program): ?>
                                <?php if ($program->id === $notice->program_id): ?>
                                    <p><?php echo $program->program ?></p>
                                <?php endif?>
                            <?php endforeach?>
                            <hr>
                        </div>
                        <div class="col-6">
                            <a href="<?php echo site_url('notices/europe/detail/' . $notice->id); ?>">Saiba mais</a>
                        </div>
                        <div class="col-6 text-end"><?php echo $notice->end_date ?></div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach?>
        <?php if (empty($notices['data'])): ?>
            <div class="text-center">
                Não foram encontrados Avisos.
            </div>
        <?php endif?>
    </div>
    <?php if ($notices['pagination']->getTotal() > 15): ?>
        <div class="d-flex justify-content-end mt-50 div-pagination">
            <?php echo $notices['pagination']->links() ?>
        </div>
    <?php endif?>
</div>
<?php echo $this->endSection() ?>

<?php echo $this->section('styles') ?>
    <link rel="stylesheet" href="<?php echo base_url('dist/css/datepicker.min.css'); ?>">
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
    <!-- https://www.npmjs.com/ -->
    <script src="<?php echo base_url('dist/js/datepicker.min.js'); ?>"></script>
    <script>
        const pickerClose = datepicker('.datePickerClose', {
            customDays: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
            customMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
            formatter: (input, date, instance) => {
                input.value =  date.toLocaleDateString()
            }
        });
        const chBoxes = document.querySelectorAll('.dropdown-menu input[type="checkbox"]');
        const dpBtn = document.getElementById('multiSelectDropdown');
        let mySelectedListItems = [];
        function handleCB() {
            mySelectedListItems = [];
            let mySelectedListItemsText = '';
            chBoxes.forEach((checkbox) => {
                if (checkbox.checked) {
                    mySelectedListItems.push(checkbox.value);
                    mySelectedListItemsText += checkbox.value + ', ';
                }
            });

            dpBtn.innerText =
                mySelectedListItems.length > 0
                    ? ( mySelectedListItems.length > 2 ? 'Vários' : mySelectedListItemsText.slice(0, -2)) : 'Selecione';
        }
        chBoxes.forEach((checkbox) => {
            checkbox.addEventListener('change', handleCB);
        });
    </script>
<?php echo $this->endSection() ?>