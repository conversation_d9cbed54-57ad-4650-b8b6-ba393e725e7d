<?php echo $this->extend('layouts/app') ?>

<?php echo $this->section('yield') ?>
<div class="container">
    <div class="row filters mb-3">
        <div class="col-md-3 col-xs-12">
            <h1>Dashboard</h1>
            <h5>Pesquise todos os avisos</h5>
        </div>

        <div class="col-md-9 col-xs-12">
            <form>
                <div class="row justify-content-end">
                    <div class="col-md-3 col-xs-12">
                        <label for="">Data de início</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="dateStart"><i class="fa-light fa-calendar-days"></i></span>
                            <input value="<?php echo $_GET['dateStart'] ?? null ?>" name="dateStart" type="text" class="form-control datePickerCloseStart" placeholder="Selecione" required/>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-12">
                        <label for="">Data de fim</label>
                        <div class="input-group mb-3">
                            <span class="input-group-text" id="dateEnd"><i class="fa-light fa-calendar-days"></i></span>
                            <input value="<?php echo $_GET['dateEnd'] ?? null ?>" name="dateEnd" type="text" class="form-control datePickerCloseEnd" placeholder="Selecione" required/>
                        </div>
                    </div>
                    <div class="col-md-3 col-xs-12">
                        <label for="" class="mobile-d-none"></label>
                        <button  type="submit" class="btn btn-primary border-0">
                        <i class="fa-light fa-filter"></i> Filtrar
                        </button>
                        <?php if (isset($_GET['dateEnd']) || isset($_GET['dateEnd'])): ?>
                        <a href="<?php echo site_url('/'); ?>" class="clear-filter"><i class="fa-light fa-broom-wide"></i>&nbsp;Limpar filtros</a>
                        <?php endif?>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <!-- -->
    <div class="row">
        <div class="col-md-8 col-xs-12">
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><h4 class="card-title">Novos avisos</h4></div>
                    </div>
                    <div class="row">
                        <?php foreach ($newNotices as $notice): ?>
                            <div class="col-md-4 col-xs-12 col-number-notices"><span class="number"><?php echo $notice['y'] ?></span><br><span class="number-text"><?php echo $notice['name'] ?></span></div>
                        <?php endforeach?>
                    </div>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-body">
                    <h4 class="card-title">Tipo de entidades</h4>
                    <figure class="highcharts-figure">
                        <div id="investmentTypesGraph"></div>
                    </figure>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-body">
                    <h4 class="card-title">Pré-Avisos por Área</h4>
                    <div id="preNoticesByAreaGraph"></div>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-body">
                    <h4 class="card-title">Pré-Avisos por Tag</h4>
                    <div id="preNoticesByTagGraph"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4 col-xs-12">
            <div class="card mb-3">
                <div class="card-body">
                    <h4 class="card-title">Família de avisos</h4>
                    <div id="noticeFamiliesGraph"></div>
                </div>
            </div>
            <div class="card mb-3">
                <div class="card-body">
                    <h4 class="card-title">Programas Regionais</h4>
                    <div id="regionalProgramsGraph"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php echo $this->section('styles') ?>
    <link rel="stylesheet" href="<?php echo base_url('dist/css/datepicker.min.css'); ?>">
<?php echo $this->endSection() ?>

<?php echo $this->section('scripts') ?>
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>
<script src="https://code.highcharts.com/modules/accessibility.js"></script>
<script src="<?php echo base_url('dist/js/highcharts.js'); ?>"></script>
<script src="<?php echo base_url('dist/js/datepicker.min.js'); ?>"></script>
<script>
    Highcharts.chart('noticeFamiliesGraph', {
        colors: ['#FFF079', '#0433BF', '#030075', '#3C3C3C'],
        chart: {
          type: 'pie',
          events: {
            load: function() {
              var chart = this,
                legend = chart.legend,
                newHeight = chart.chartHeight + legend.fullHeight;
              chart.setSize(undefined, newHeight);
            }
          }
        },
        title: false,
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                size: '100%',
                dataLabels: {
                    enabled: true,
                    distance: -30,
                    format: '{y}',
                    style: {
                        fontSize: 20,
                        textOutline: 'none'
                    }
                },
                showInLegend: true
            }
        },
        legend: {
            align: 'center',
            enabled: true,
            layout: 'vertical',
            verticalAlign: 'bottom',
            itemStyle: {
                fontSize: '18px',
                color: '#7A7A7A'
            },
            labelFormatter: function () {
              var symbol = '<div class="d-flex justify-content-between div-legend"><div class="legend-chart">&nbsp;' + this.name + '</div><div class="legend-count"></div></div>';
                return symbol;
            },
        },
        series: [{
            name: 'valor',
            colorByPoint: true,
            innerSize: '50%',
            data: <?php echo json_encode($newNotices) ?>,
            dataLabels: {
                enabled: true,
                style: {
                    textOutline: 'none',
                }
            },
        }],
    });
    Highcharts.chart('regionalProgramsGraph', {
        colors: ['#FFF079', '#0433BF', '#030075', '#3C3C3C'],
        chart: {
          type: 'pie',
        },
        title: false,
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                size: 250,
                dataLabels: {
                    enabled: true,
                    distance: -30,
                    format: '{y}',
                    style: {
                        fontSize: 20,
                        textOutline: 'none'
                    }
                },
                showInLegend: true
            }
        },
        legend: {
            align: 'center',
            enabled: true,
            layout: 'vertical',
            verticalAlign: 'bottom',
            itemStyle: {
                fontSize: '18px',
                color: '#7A7A7A'
            },
            labelFormatter: function () {
              var symbol = '<div class="d-flex justify-content-between div-legend"><div class="legend-chart">&nbsp;' + this.name + '</div><div class="legend-count"></div></div>';
                return symbol;
            },
        },
        series: [{
            name: 'valor',
            colorByPoint: true,
            innerSize: '50%',
            data: <?php echo json_encode($regionalPrograms, JSON_NUMERIC_CHECK) ?>,
            dataLabels: {
                enabled: true,
                style: {
                    textOutline: 'none',
                }
            },
        }],
    });
    Highcharts.chart('investmentTypesGraph', {
        colors: ['#0433BF'],
        chart: {
            type: 'bar',
            height: <?php echo count($investmentTypes) ?> * 30 + 80,
        },
        title: false,
        subtitle: false,
        xAxis: {
            categories: <?php echo json_encode(array_column($investmentTypes, 'name')) ?>,
            title: {
                text: null
            },
            labels: {
                'step': 1,
                style: {
                    fontSize: 16,
                }
            },
            gridLineWidth: 1,
            lineWidth: 0
        },
        yAxis: {
            min: 0,
            title: {
                text: 'Nº Programas',
                align: 'high'
            },
            allowDecimals: false,
            gridLineWidth: 0
        },
        tooltip: {
            valueSuffix: false
        },
        plotOptions: {
            bar: {
                dataLabels: {
                    enabled: true,
                    style: {
                        fontSize: 16,
                        fontWeight: 400
                    }
                },
                groupPadding: 0.1
            }
        },
        legend: false,
        credits: {
            enabled: false
        },
        series: [{
            name: 'Número de Programas',
            data: <?php echo json_encode(array_column($investmentTypes, 'count'), JSON_NUMERIC_CHECK) ?>,
        }]
    });
    Highcharts.chart('preNoticesByAreaGraph', {
        colors: ['#0433BF'],
        chart: {
            type: 'bar',
            height: <?php echo count($preNoticesByArea) ?> * 30 + 80,
        },
        title: false,
        subtitle: false,
        xAxis: {
            categories: <?php echo json_encode(array_column($preNoticesByArea, 'name')) ?>,
            title: {
                text: null
            },
            labels: {
                'step': 1,
                style: {
                    fontSize: 16,
                }
            },
            gridLineWidth: 1,
            lineWidth: 0
        },
        yAxis: {
            min: 0,
            title: {
                text: 'Nº de Pré-Avisos',
                align: 'high'
            },
            allowDecimals: false,
            gridLineWidth: 0
        },
        tooltip: {
            valueSuffix: false
        },
        plotOptions: {
            bar: {
                dataLabels: {
                    enabled: true,
                    style: {
                        fontSize: 16,
                        fontWeight: 400
                    }
                },
                groupPadding: 0.1
            }
        },
        legend: false,
        credits: {
            enabled: false
        },
        series: [{
            name: 'Número de Programas',
            data: <?php echo json_encode(array_column($preNoticesByArea, 'count'), JSON_NUMERIC_CHECK) ?>,
        }]
    });
    Highcharts.chart('preNoticesByTagGraph', {
        colors: ['#0433BF'],
        chart: {
            type: 'bar',
            height: <?php echo count($preNoticesByTag) ?> * 30 + 80,
        },
        title: false,
        subtitle: false,
        xAxis: {
            categories: <?php echo json_encode(array_column($preNoticesByTag, 'name')) ?>,
            title: {
                text: null
            },
            labels: {
                'step': 1,
                style: {
                    fontSize: 16,
                }
            },
            gridLineWidth: 1,
            lineWidth: 0
        },
        yAxis: {
            min: 0,
            title: {
                text: 'Nº de Pré-Avisos',
                align: 'high'
            },
            allowDecimals: false,
            gridLineWidth: 0
        },
        tooltip: {
            valueSuffix: false
        },
        plotOptions: {
            bar: {
                dataLabels: {
                    enabled: true,
                    style: {
                        fontSize: 16,
                        fontWeight: 400
                    }
                },
                groupPadding: 0
            }
        },
        legend: false,
        credits: {
            enabled: false
        },
        series: [{
            name: 'Número de Programas',
            data: <?php echo json_encode(array_column($preNoticesByTag, 'count'), JSON_NUMERIC_CHECK) ?>,
        }]
    });

    // DATEPICKER
    const pickerCloseStart = datepicker('.datePickerCloseStart', {
        customDays: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
        customMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
        formatter: (input, date, instance) => {
            input.value =  date.toLocaleDateString()
        }
    });
    const pickerCloseEnd = datepicker('.datePickerCloseEnd', {
        customDays: ['S', 'T', 'Q', 'Q', 'S', 'S', 'D'],
        customMonths: ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'],
        formatter: (input, date, instance) => {
            input.value =  date.toLocaleDateString()
        }
    });
    const chBoxes = document.querySelectorAll('.dropdown-menu input[type="checkbox"]');
    const dpBtn = document.getElementById('multiSelectDropdown');
    let mySelectedListItems = [];
    function handleCB() {
        mySelectedListItems = [];
        let mySelectedListItemsText = '';
        chBoxes.forEach((checkbox) => {
            if (checkbox.checked) {
                mySelectedListItems.push(checkbox.value);
                mySelectedListItemsText += checkbox.value + ', ';
            }
        });

        dpBtn.innerText =
            mySelectedListItems.length > 0
                ? ( mySelectedListItems.length > 2 ? 'Vários' : mySelectedListItemsText.slice(0, -2)) : 'Selecione';
    }
    chBoxes.forEach((checkbox) => {
        checkbox.addEventListener('change', handleCB);
    });
</script>
<?php echo $this->endSection() ?>