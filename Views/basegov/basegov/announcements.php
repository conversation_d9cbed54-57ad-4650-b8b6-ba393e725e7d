<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Base.gov.pt</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link <?php if (!$archive): ?>active<?php endif?>" href="<?php echo site_url('basegov/announcements') ?>">Dentro do prazo</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php if ($archive): ?>active<?php endif?>" href="<?php echo site_url('basegov/announcements?archive=1') ?>">Arquivo</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <form class="card">
                        <?php if (isset($_GET['archive']) && $_GET['archive'] === '1'): ?>
                            <input type="hidden" name="archive" value="1">
                        <?php endif?>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="drPublicationDateStart">Data publicação <small>(de)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="drPublicationDateStart" value="<?php echo set_value('drPublicationDateStart', $_GET['drPublicationDateStart'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="drPublicationDateEnd">Data publicação <small>(até)</small></label>
                                        <input onchange="this.form.submit()" class="form-control" type="date" name="drPublicationDateEnd" value="<?php echo set_value('drPublicationDateEnd', $_GET['drPublicationDateEnd'] ?? '') ?>">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="entity_type">Tipo de entidade</label>
                                        <select onchange="this.form.submit()" name="entity_type" class="form-control">
                                            <option value=""></option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'CIM'): ?>selected<?php endif?> value="CIM">CIM</option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'city_hall'): ?>selected<?php endif?> value="city_hall">Município</option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'parish'): ?>selected<?php endif?> value="parish">J. de Freguesia</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="entity">Entidade adjudicante</label>
                                        <select onchange="this.form.submit()" <?php if (!isset($_GET['entity_type']) || empty($_GET['entity_type'])): ?>disabled<?php endif?> name="entity"  class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($entities as $entity): ?>
                                                <option <?php if (isset($_GET['entity']) && $_GET['entity'] === $entity->nif): ?>selected<?php endif?> value="<?php echo $entity->nif ?>"><?php echo $entity->description ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="type">Tipo de Ato</label>
                                        <select onchange="this.form.submit()" name="type" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($types as $type): ?>
                                                <option <?php if (isset($_GET['type']) && $type->type === $_GET['type']): ?>selected<?php endif?> value="<?php echo $type->type ?>"><?php echo $type->type ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="contractingProcedureType">Procedimento</label>
                                        <select onchange="this.form.submit()" name="contractingProcedureType" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($contractingProcedureTypes as $contractingProcedureType): ?>
                                                <option <?php if (isset($_GET['contractingProcedureType']) && $contractingProcedureType->contractingProcedureType === $_GET['contractingProcedureType']): ?>selected<?php endif?> value="<?php echo $contractingProcedureType->contractingProcedureType ?>"><?php echo $contractingProcedureType->contractingProcedureType ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="cpvs">CPV</label>
                                        <select onchange="this.form.submit()" name="cpvs" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($cpvs as $cpv): ?>
                                                <option <?php if (isset($_GET['cpvs']) && $cpv->cpvs === $_GET['cpvs']): ?>selected<?php endif?> value="<?php echo $cpv->cpvs ?>"><?php echo word_limiter($cpv->cpvs, 10) ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="alert_id">Alertas</label>
                                        <select onchange="this.form.submit()" name="alert_id" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($alerts as $alert): ?>
                                                <option <?php if (isset($_GET['alert_id']) && $_GET['alert_id'] === $alert->id): ?>selected<?php endif?> value="<?php echo $alert->id ?>"><?php echo $alert->emails ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="search">Pesquisa</label>
                                        <input onchange="this.form.submit()" type="text" name="search" value="<?php echo $_GET['search'] ?? null ?>" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <a href="<?php echo site_url() ?>" class="mt-33 btn btn-default"><i class="fa-duotone fa-broom-wide"></i></a>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <button type="submit" class="mt-33 btn btn-primary"><i class="fa-duotone fa-magnifying-glass"></i> Pesquisar</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <hr>
                                    <p>Encontrou <?php echo $announcements['pagination']->getTotal() ?> resultados</p>
                                </div>
                            </div>
                        </div>
                    </form>
                    <table class="dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th>Objeto</th>
                                <th>Tipo de ato</th>
                                <th>Procedimento</th>
                                <th>Entidade</th>
                                <th>CPV</th>
                                <th>Preço base</th>
                                <th>DR</th>
                                <th>Limite</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($announcements['data'] as $announcement): ?>
                            <tr>
                                <td><?php echo wordwrap($announcement->contractDesignation, 30, "<br />\n"); ?></td>
                                <td><?php echo $announcement->type ?></td>
                                <td><?php echo $announcement->contractingProcedureType ?></td>
                                <td><?php echo $announcement->contractingEntity ?></td>
                                <td><?php echo word_limiter($announcement->cpvs, 30) ?></td>
                                <td><?php echo $announcement->basePrice ?></td>
                                <td><?php echo $announcement->drPublicationDate ?></td>
                                <td><?php echo $announcement->proposalDeadline ?></td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a target="_blank" data-toggle="tooltip" data-placement="top" title="Ver em base.gov.pt" href="https://www.base.gov.pt/Base4/pt/detalhe/?type=anuncios&id=<?php echo $announcement->id ?>" class="btn btn-primary"><i class="fa-duotone fa-file-contract"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Ver Detalhes" href="<?php echo site_url('basegov/announcement-detail/' . $announcement->id) ?>" class="<?php if ($announcement->status === 'new'): ?>disabled<?php endif?> btn btn-primary"><i class="fa-duotone fa-memo-circle-info"></i></a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <?php echo $announcements['pagination']->links() ?>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>