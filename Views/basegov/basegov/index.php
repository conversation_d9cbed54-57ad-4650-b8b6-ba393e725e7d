<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Base.gov.pt</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">Lista de Contratos</h3>
                </div>
                <div class="card-body">
                    <form class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="from">De</label>
                                        <input type="date" onchange="this.form.submit()" name="from" value="<?php echo set_value('from', $_GET['from'] ?? '') ?>" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="to">Até</label>
                                        <input onchange="this.form.submit()" type="date" name="to" value="<?php echo set_value('to', $_GET['to'] ?? '') ?>" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="entity_type">Tipo de entidade</label>
                                        <select onchange="this.form.submit()" name="entity_type" class="form-control">
                                            <option value=""></option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'CIM'): ?>selected<?php endif?> value="CIM">CIM</option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'city_hall'): ?>selected<?php endif?> value="city_hall">Município</option>
                                            <option <?php if (isset($_GET['entity_type']) && $_GET['entity_type'] === 'parish'): ?>selected<?php endif?> value="parish">J. de Freguesia</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label for="entity">Entidade adjudicante</label>
                                        <select onchange="this.form.submit()" <?php if (!isset($_GET['entity_type']) || empty($_GET['entity_type'])): ?>disabled<?php endif?> name="entity" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($entityTypes as $entity): ?>
                                                <option <?php if (isset($_GET['entity']) && $_GET['entity'] === $entity->entity_type_vat): ?>selected<?php endif?> value="<?php echo $entity->entity_type_vat ?>"><?php echo $entity->entity_type_name ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label onchange="this.form.submit()" for="contracted">Entidade adjudicatária</label>
                                        <select name="contracted" <?php if (!isset($_GET['entity']) || empty($_GET['entity'])): ?>disabled<?php endif?> class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($contractedEntities as $contractedEntity): ?>
                                                <option <?php if (isset($_GET['contracted']) && $_GET['contracted'] === $contractedEntity->contracted): ?>selected<?php endif?> value="<?php echo $contractedEntity->contracted ?>"><?php echo $contractedEntity->contracted ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="contractingProcedureType">Tipo de procedimento</label>
                                        <select onchange="this.form.submit()" name="contractingProcedureType" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($contractingProcedureTypes as $contractingType): ?>
                                                <option <?php if (isset($_GET['contractingProcedureType']) && $_GET['contractingProcedureType'] === $contractingType->contractingProcedureType): ?>selected<?php endif?> value="<?php echo $contractingType->contractingProcedureType ?>"><?php echo $contractingType->contractingProcedureType ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label for="cpvsDesignation">CPV</label>
                                        <select onchange="this.form.submit()" name="cpvsDesignation" class="form-control">
                                            <option value=""></option>
                                            <?php foreach ($cpvsDesignations as $cpvsDesignation): ?>
                                                <option <?php if (isset($_GET['cpvsDesignation']) && $_GET['cpvsDesignation'] === $cpvsDesignation->cpvsDesignation): ?>selected<?php endif?> value="<?php echo $cpvsDesignation->cpvsDesignation ?>"><?php echo $cpvsDesignation->cpvsDesignation ?></option>
                                            <?php endforeach?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-5">
                                    <div class="form-group">
                                        <label for="search">Pesquisa...</label>
                                        <input onchange="this.form.submit()" type="text" name="search" value="<?php echo $_GET['search'] ?? null ?>" class="form-control">
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <a href="<?php echo site_url() ?>" class="mt-33 btn btn-default"><i class="fa-duotone fa-broom-wide"></i></a>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <button type="submit" class="mt-33 btn btn-primary"><i class="fa-duotone fa-magnifying-glass"></i> Pesquisar</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-1">
                                    <a class="btn btn-primary" href="<?php echo site_url('basegov/export/?' . $_SERVER['QUERY_STRING']) ?>">Exportar</a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <hr>
                                    <p>Encontrou <?php echo $contracts['pagination']->getTotal() ?> resultados</p>
                                </div>
                            </div>
                        </div>
                    </form>
                    <table class="dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th>Objeto</th>
                                <th>Procedimento</th>
                                <th>Adjudicante</th>
                                <th>Adjudicatário</th>
                                <th>Preço contratual</th>
                                <th>Publicação</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($contracts['data'] as $contract): ?>
                            <tr>
                                <td><?php echo wordwrap($contract->objectBriefDescription, 30, "<br />\n"); ?></td>
                                <td><?php echo $contract->contractingProcedureType ?></td>
                                <td><?php echo wordwrap($contract->contracting, 25, "<br />\n") ?></td>
                                <td><?php echo wordwrap($contract->contracted, 25, "<br />\n") ?></td>
                                <td><?php echo $contract->initialContractualPrice ?></td>
                                <td><?php echo $contract->publicationDate ?></td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a target="_blank" data-toggle="tooltip" data-placement="top" title="Ver em base.gov.pt" href="https://www.base.gov.pt/Base4/pt/detalhe/?type=contratos&id=<?php echo $contract->id ?>" class="btn btn-primary"><i class="fa-duotone fa-file-contract"></i></a>
                                        <a data-toggle="tooltip" data-placement="top" title="Ver Detalhes" href="<?php echo site_url('basegov/detail/' . $contract->id) ?>" class="<?php if ($contract->status === 'new'): ?>disabled<?php endif?> btn btn-primary"><i class="fa-duotone fa-memo-circle-info"></i></a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
                <div class="card-footer">
                    <?php echo $contracts['pagination']->links() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>