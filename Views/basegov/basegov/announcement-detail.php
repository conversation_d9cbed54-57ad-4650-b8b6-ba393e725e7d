<?php echo $this->extend($layout) ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Base.gov.pt - Anúncios</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title"><?php echo $announcement->contractDesignation ?></h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4>Informação Detalhada</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><strong>Nº do anúncio:</strong> <?php echo $announcement->announcementNumber ?></li>
                                <li class="list-group-item"><strong>Data de publicação:</strong> <?php echo $announcement->drPublicationDate ?></li>
                                <li class="list-group-item"><strong>Entidade emissora:</strong>
                                    <?php foreach ($announcement->entities as $entity): ?>
                                        <a href="https://www.base.gov.pt/Base4/pt/detalhe/?type=entidades&id=<?php echo $entity->id ?>" target="_blank"><?php echo $entity->nif ?> - <?php echo $entity->description ?></a>
                                    <?php endforeach?>
                                </li>
                                <li class="list-group-item"><strong>Descrição:</strong> <?php echo $announcement->contractDesignation ?></li>
                                <li class="list-group-item"><strong>Tipo de ato:</strong> <?php echo $announcement->type ?></li>
                                <li class="list-group-item"><strong>Tipo de modelo:</strong> <?php echo $announcement->contractingProcedureType ?></li>
                                <li class="list-group-item"><strong>Tipos de contrato:</strong> <?php echo $announcement->contractType ?></li>
                                <li class="list-group-item"><strong>Preço base:</strong> <?php echo $announcement->basePrice ?></li>
                                <li class="list-group-item"><strong>CPVs:</strong> <?php echo $announcement->cpvs ?></li>
                                <li class="list-group-item"><strong>Prazo para apresentação de propostas:</strong> <?php echo $announcement->proposalDeadline ?></li>
                                <li class="list-group-item"><strong>Anúncio:</strong> <a target="_blank" href="<?php echo $announcement->reference ?>">Ligação para anúncio</a></li>
                                <li class="list-group-item"><strong>Peças do procedimento:</strong> <a href="<?php echo $announcement->contractingProcedureUrl ?>">Ligação para peças do procedimento</a></li>
                                <li class="list-group-item"><strong>Impugnações do anúncio:</strong> <?php echo $announcement->impugnations ?></li>
                                <li class="list-group-item"><strong>Não celebrações de contrato do anúncio:</strong> </li>
                                <li class="list-group-item"><strong>Contratos do anúncio:</strong> </li>
                                <li class="list-group-item"><strong>Critérios ambientais:</strong></li>
                                <li class="list-group-item"><strong>Critérios de Adjudicação:</strong> <br><?php echo nl2br($announcement->award_criteria) ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>