<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-12">
            <h1 class="m-0">Base.gov.pt</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title"><?php echo $contract->objectBriefDescription ?></h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <h4>Informação Detalhada</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><strong>Data da publicação:</strong> <?php echo $contract->publicationDate ?></li>
                                <li class="list-group-item"><strong>Tipos de contrato:</strong> <?php echo $contract->contractTypes ?></li>
                                <li class="list-group-item"><strong>Nº do acordo quadro:</strong> <?php echo $contract->frameworkAgreementProcedureId ?></li>
                                <li class="list-group-item"><strong>Descrição do acordo quadro:</strong> <?php echo $contract->frameworkAgreementProcedureDescription ?></li>
                                <li class="list-group-item"><strong>Tipologia da medida especial:</strong> <?php echo $contract->specialMeasures ?></li>
                                <li class="list-group-item"><strong>Tipo de procedimento:</strong> <?php echo $contract->contractingProcedureType ?></li>
                                <li class="list-group-item"><strong>Descrição:</strong> <?php echo $contract->description ?></li>
                                <li class="list-group-item"><strong>Fundamentação:</strong> <?php echo $contract->contractFundamentationType ?></li>
                                <li class="list-group-item"><strong>Fundamentação para recurso ao ajuste direto:</strong> <?php echo $contract->directAwardFundamentationType ?></li>
                                <li class="list-group-item"><strong>Regime:</strong> <?php echo $contract->regime ?></li>
                                <li class="list-group-item"><strong>Critérios materiais:</strong> <?php echo $contract->materialCriteria ?></li>
                                <li class="list-group-item"><strong>Entidades adjudicantes:</strong> <?php echo $contract->vat ?> - <?php echo $contract->contracting ?></li>
                                <li class="list-group-item"><strong>Entidades adjudicatárias:</strong> <?php echo $contract->contracted ?></li>
                                <li class="list-group-item"><strong>Tipologia CPVS:</strong> <?php echo $contract->cpvsType ?> - <?php echo $contract->cpvsDesignation ?></li>
                                <li class="list-group-item"><strong>Tipo de contrato CS:</strong> <?php echo $contract->contractTypeCS ?></li>
                                <li class="list-group-item"><strong>CCP:</strong> <?php echo $contract->ccp ?></li>
                                <li class="list-group-item"><strong>Objeto do contrato:</strong> <?php echo $contract->objectBriefDescription ?></li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="list-group list-group-flush">

                                <li class="list-group-item"><strong>Procedimento centralizado:</strong> <?php echo $contract->centralizedProcedure ?></li>
                                <li class="list-group-item"><strong>CPVs:</strong> <?php echo $contract->cpvs ?></li>
                                <li class="list-group-item"><strong>Data do contrato:</strong> <?php echo $contract->signingDate ?></li>
                                <li class="list-group-item"><strong>Preço contratual:</strong> <?php echo $contract->initialContractualPrice ?></li>
                                <li class="list-group-item"><strong>Prazo de execução:</strong> <?php echo $contract->executionDeadline ?></li>
                                <li class="list-group-item"><strong>Local de execução:</strong> <?php echo $contract->executionPlace ?></li>
                                <li class="list-group-item"><strong>Anúncio: </strong> <a target="_blank" href="https://www.base.gov.pt/Base4/pt/detalhe/?type=anuncios&id=<?php echo $contract->announcementId ?>">Detalhe em Base.gov.pt</a></li>
                                <li class="list-group-item"><strong>Peças do procedimento:</strong> <a href="<?php echo $contract->contractingProcedureUrl ?>">Download</a></li>
                                <li class="list-group-item"><strong>Modificações contratuais:</strong> </li>
                                <li class="list-group-item"><strong>Observações:</strong> <?php echo $contract->observations ?></li>
                                <li class="list-group-item"><strong>Critérios ambientais:</strong> <?php echo $contract->ambientCriteria ?></li>
                                <li class="list-group-item"><strong>Justificação para não redução a escrito do contrato:</strong> <?php echo $contract->nonWrittenContractJustificationTypes ?></li>
                                <li class="list-group-item"><strong>Aviso:</strong></li>
                                <li class="list-group-item"><strong>Valor CPVS:</strong> <?php echo $contract->cpvsValue ?></li>
                                <li class="list-group-item"><strong>Incrementos:</strong> <?php echo $contract->increments ?></li>
                                <li class="list-group-item"><strong>Membro de aquisição UE:</strong> <?php echo $contract->aquisitionStateMemberUE ?></li>
                                <li class="list-group-item"><strong>Info Membro de aquisição UE:</strong> <?php echo $contract->infoAquisitionStateMemberUE ?></li>
                            </ul>
                        </div>
                    </div>
                    <?php if (!empty($contract->contestants)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h4>Entidades concorrentes</h4>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($contract->contestants as $contestant): ?>
                                        <li class="list-group-item">
                                            <a href="https://www.base.gov.pt/Base4/pt/detalhe/?type=entidades&id=<?php echo $contestant->remote_base_gov_id ?>" target="_blank"><?php echo $contestant->nif ?> - <?php echo $contestant->description ?></a>
                                        </li>
                                    <?php endforeach?>
                                </ul>
                            </div>
                        </div>
                    <?php endif?>
                    <?php if (!empty($contract->documents)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h4>Documentos</h4>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($contract->documents as $document): ?>
                                        <li class="list-group-item">
                                            <a href="https://www.base.gov.pt/Base4/pt/resultados/?type=doc_documentos&id=<?php echo $document->base_gov_id ?>&ext=.pdf"><?php echo $document->description ?></a>
                                        </li>
                                    <?php endforeach?>
                                </ul>
                            </div>
                        </div>
                    <?php endif?>
                    <?php if (!empty($contract->invitees)): ?>
                        <div class="row">
                            <div class="col-12">
                                <h4>Convidados</h4>
                                <ul class="list-group list-group-flush">
                                    <?php foreach ($contract->invitees as $invitee): ?>
                                        <li class="list-group-item">
                                            <a href="https://www.base.gov.pt/Base4/pt/detalhe/?type=entidades&id=<?php echo $invitee->remote_base_gov_id ?>" target="_blank"><?php echo $invitee->nif ?> - <?php echo $invitee->description ?></a>
                                        </li>
                                    <?php endforeach?>
                                </ul>
                            </div>
                        </div>
                    <?php endif?>
                    <div class="row">
                        <div class="col-12">
                            <h4>Execução do contrato</h4>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item"><strong>Causa da extinção do contrato:</strong> </li>
                                <li class="list-group-item"><strong>Data do fecho do contrato:</strong> <?php echo $contract->closeDate ?></li>
                                <li class="list-group-item"><strong>Preço total efetivo:</strong> <?php echo $contract->totalEffectivePrice ?></li>
                                <li class="list-group-item"><strong>Causas das alterações ao prazo:</strong> <?php echo $contract->causesDeadlineChange ?></li>
                                <li class="list-group-item"><strong>Causas das alterações ao preço:</strong> <?php echo $contract->causesPriceChange ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
      $(function () {
        $("#datatable").DataTable({
            "autoWidth": true
        });
      });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>