<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">CPVs</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/cpvs') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($cpv->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> CPV
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/cpvs/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $cpv->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="cpv" class="form-label">CPV</label>
                                    <input type="text" name="cpv" class="form-control" aria-describedby="cpv" value="<?php echo set_value('cpv', $cpv->cpv ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <div class="form-group">
                                    <label for="description" class="form-label">Nome</label>
                                    <input type="text" name="description" maxlength="255" class="form-control" aria-describedby="description" value="<?php echo set_value('description', $cpv->description ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>