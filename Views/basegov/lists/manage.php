<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Lista de Entidades</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/entity-lists') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($list->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Lista de Entidades
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/entity-lists/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $list->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nome</label>
                                    <input type="text" name="name" class="form-control" aria-describedby="name" value="<?php echo set_value('name', $list->name ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="direction" class="form-label">Sincronização</label>
                                    <select required name="direction" class="form-control">
                                        <option></option>
                                        <option <?php if (isset($list->direction) && $list->direction === 'normal'): ?>selected<?php endif?> value="normal">Normal</option>
                                        <option <?php if (isset($list->direction) && $list->direction === 'inverted'): ?>selected<?php endif?> value="inverted">Histórico</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="status" class="form-label">Estado</label>
                                    <select required name="status" class="form-control">
                                        <option></option>
                                        <option <?php if (isset($list->status) && $list->status === 'active'): ?>selected<?php endif?> value="active">Ativo</option>
                                        <option <?php if (isset($list->status) && $list->status === 'inactive'): ?>selected<?php endif?> value="inactive">Inativo</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>