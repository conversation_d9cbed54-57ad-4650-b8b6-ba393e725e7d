<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Importar Entidades</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/entity-lists') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        Importar Entidades
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open_multipart('settings/entity-lists/import') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $list->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="list_id" class="form-label">Lista</label>
                                    <select required name="list_id" class="form-control">
                                        <option></option>
                                        <?php foreach ($lists as $list): ?>
                                            <option <?php if (isset($selectedList->id) && $list->id === $selectedList->id): ?>selected<?php endif?> value="<?php echo $list->id ?>"><?php echo $list->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="file" class="form-label">Ficheiro <small>(csv listagem simples de NIFs)</small></label>
                                    <input type="file" name="file" class="form-control" aria-describedby="file" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>