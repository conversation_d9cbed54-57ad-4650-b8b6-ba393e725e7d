<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Alertas Base.gov <small>(Contratos)</small></h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/alerts') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($alert->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Alerta
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/alerts-contracts/save') ?>
                        <input type="hidden" name="id" value="<?php echo $alert->id ?? null ?>">
                        <input type="hidden" name="type" value="contracts">
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="tags" class="form-label">Tags <small>(Separadas por virgulas)</small></label>
                                    <input type="text" name="tags" class="form-control" aria-describedby="tags" value="<?php echo set_value('tags', $alert->tags ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="emails" class="form-label">Emails <small>(Separados por virgulas)</small></label>
                                    <input type="text" required name="emails" class="form-control" aria-describedby="emails" value="<?php echo set_value('emails', $alert->emails ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="list_id" class="form-label">Lista</label>
                                    <select required name="list_id" class="form-control">
                                        <option></option>
                                        <?php foreach ($lists as $list): ?>
                                            <option <?php if (isset($alert->list_id) && $list->id === $alert->list_id): ?>selected<?php endif?> value="<?php echo $list->id ?>"><?php echo $list->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>
