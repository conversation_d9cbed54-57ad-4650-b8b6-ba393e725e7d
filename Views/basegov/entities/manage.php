<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Entidades</h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/entities') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($entity->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Entidade
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/entities/save') ?>
                        <?php echo csrf_field() ?>
                        <input type="hidden" name="id" value="<?php echo $entity->id ?? null ?>">
                        <div class="row mb-3">
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="vat" class="form-label">NIF</label>
                                    <input type="text" required name="vat" class="form-control" aria-describedby="vat" value="<?php echo set_value('vat', $entity->vat ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="name" class="form-label">Nome</label>
                                    <input type="text" required name="name" class="form-control" aria-describedby="name" value="<?php echo set_value('name', $entity->name ?? null) ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="form-group">
                                    <label for="list_id" class="form-label">Lista</label>
                                    <select required name="list_id" class="form-control">
                                        <option></option>
                                        <?php foreach ($lists as $list): ?>
                                            <option <?php if (isset($entity->list_id) && $list->id === $entity->list_id): ?>selected<?php endif?> value="<?php echo $list->id ?>"><?php echo $list->name ?></option>
                                        <?php endforeach?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>