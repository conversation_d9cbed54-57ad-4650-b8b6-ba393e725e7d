<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Alertas Base.gov <small>(<PERSON><PERSON><PERSON><PERSON>)</small></h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/alerts/insert') ?>" class="btn btn-primary"><i class="fa-duotone fa-plus"></i> Inserir Alerta</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">Lista de Alertas <small>(<PERSON><PERSON><PERSON><PERSON>)</small></h3>
                </div>
                <div class="card-body">
                    <table id="datatable" class="dataTables_wrapper dt-bootstrap4 table table-hover">
                        <thead>
                            <tr>
                                <th>Tags</th>
                                <th>Emails</th>
                                <th>CPVs</th>
                                <th class="text-right"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($alerts as $alert): ?>
                            <tr>
                                <td><?php echo wordwrap($alert->tags, 100, '<br>') ?></td>
                                <td><?php echo wordwrap($alert->emails, 30, '<br>') ?></td>
                                <td><a data-toggle="tooltip" data-placement="top" title="<?php echo implode('; ', $alert->cpvs) ?>">CPVS</a></td>
                                <td class="text-right">
                                    <div class="btn-group" role="group">
                                        <a href="<?php echo site_url('settings/alerts/update/' . $alert->id) ?>" class="btn btn-primary"><i class="fa-duotone fa-pen-to-square"></i></a>
                                        <a onclick="return confirmDelete();" href="<?php echo site_url('settings/alerts/delete/' . $alert->id) ?>" class="<?php if ($alert->main): ?>disabled<?php endif?> btn btn-primary"><i class="fa-duotone fa-trash"></i></a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach;?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php $this->section('scripts')?>
    <script src="<?php echo base_url('admin/plugins/datatables/jquery.dataTables.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/dataTables.responsive.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-responsive/js/responsive.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/dataTables.buttons.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.bootstrap4.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/jszip/jszip.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/pdfmake.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/pdfmake/vfs_fonts.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.html5.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.print.min.js') ?>"></script>
    <script src="<?php echo base_url('admin/plugins/datatables-buttons/js/buttons.colVis.min.js') ?>"></script>
    <script>
        $(function () {
            $("#datatable").DataTable();
        });
    </script>
<?php $this->endSection();?>

<?php $this->section('styles')?>
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-responsive/css/responsive.bootstrap4.min.css') ?>">
    <link rel="stylesheet" href="<?php echo base_url('admin/plugins/datatables-buttons/css/buttons.bootstrap4.min.css') ?>">
<?php $this->endSection()?>