<?php echo $this->extend('layouts/admin') ?>
<?php echo $this->section('content') ?>
<div class="container-fluid">
    <div class="row mb-2 align-items-center">
        <div class="col-sm-6">
            <h1 class="m-0">Alertas Base.gov <small>(<PERSON><PERSON><PERSON><PERSON>)</small></h1>
        </div>
        <div class="col-sm-6 text-right">
            <a href="<?php echo site_url('settings/alerts') ?>" class="btn btn-outline-primary"><i class="fa-duotone fa-backward"></i> Voltar</a>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between">
                    <h3 class="card-title">
                        <?php if (empty($alert->id)): ?>Inserir<?php else: ?>Editar<?php endif;?> Alerta
                    </h3>
                </div>
                <div class="card-body">
                    <?php echo form_open('settings/alerts/save') ?>
                        <input type="hidden" name="id" value="<?php echo $alert->id ?? null ?>">
                        <input type="hidden" name="type" value="announcements">
                        <div class="row mb-3">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="tags" class="form-label">Tags <small>(Separadas por virgulas)</small></label>
                                    <input type="text" name="tags" class="form-control" aria-describedby="tags" value="<?php echo set_value('tags', $alert->tags ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="form-group">
                                    <label for="emails" class="form-label">Emails <small>(Separados por virgulas)</small></label>
                                    <input type="text" required name="emails" class="form-control" aria-describedby="emails" value="<?php echo set_value('emails', $alert->emails ?? null) ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <table class="dt-bootstrap4 table table-hover">
                                    <thead>
                                        <tr>
                                            <th><input onClick="toggleCheckboxes(this)" type="checkbox" class="checkall"></th>
                                            <th>CPV</th>
                                            <th>Nome</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($cpvs as $cpv): ?>
                                        <tr>
                                            <td><input class="checkall" <?php if (in_array($cpv->cpv, $alert->cpvs ?? [])): ?>checked<?php endif?> name="cpvs[]" value="<?php echo $cpv->cpv ?>" type="checkbox"></td>
                                            <td><?php echo $cpv->cpv ?></td>
                                            <td><?php echo $cpv->description ?></td>
                                        </tr>
                                        <?php endforeach;?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary"><i class="fa-duotone fa-floppy-disk"></i> Gravar</button>
                            </div>
                        </div>
                    <?php echo form_close() ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php echo $this->endSection() ?>

<?php echo $this->section('styles') ?>
<style>
input[type=checkbox] {
    /* Double-sized Checkboxes */
    -ms-transform: scale(1.5); /* IE */
    -moz-transform: scale(1.5); /* FF */
    -webkit-transform: scale(1.5); /* Safari and Chrome */
    -o-transform: scale(1.5); /* Opera */
    transform: scale(1.5);
    padding: 10px;
}
</style>
<?php $this->endSection();?>

<?php echo $this->section('scripts') ?>
<script type="text/javascript">
    /**
     * Toogle all checkboxes
     * @param  object source
     */
    function toggleCheckboxes(source) {
        var aInputs = document.getElementsByTagName('input');
        for (var i=0;i<aInputs.length;i++) {
            if (aInputs[i] != source && aInputs[i].className == source.className) {
                aInputs[i].checked = source.checked;
            }
        }
    }
</script>
<?php echo $this->endSection() ?>