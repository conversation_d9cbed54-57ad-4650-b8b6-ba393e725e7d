<!DOCTYPE html>
<html>
<head>
<title>DRE</title>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<style type="text/css">
    /* CLIENT-SPECIFIC STYLES */
    body{font-family: Helvetica, arial, sans-serif;}
    h1 a{
        color: #fff;
        text-decoration: none;
        font-weight: normal;
    }
    h1 a strong{
        font-weight: bold;
    }
    h2{font-size: 18px; font-weight: 800;color:#0192bd;}
    h3{font-size: 14px; font-weight: 400;}
    .sendOk{border: 1px solid #0192bd; width: 150px;padding:10px;margin: 0 auto;
        -webkit-border-top-left-radius: 10px;
        -webkit-border-bottom-right-radius: 10px;
        -moz-border-radius-topleft: 10px;
        -moz-border-radius-bottomright: 10px;
        border-top-left-radius: 10px;
        border-bottom-right-radius: 10px;

    }
    .sendOk a{text-decoration: none; color:#0192bd; }
    body, table, td, a{-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;padding: 5px;} /* Prevent WebKit and Windows mobile changing default text sizes */
    table, td{mso-table-lspace: 0pt; mso-table-rspace: 0pt;} /* Remove spacing between tables in Outlook 2007 and up */
    img{-ms-interpolation-mode: bicubic;} /* Allow smoother rendering of resized image in Internet Explorer */
    /* RESET STYLES */
    img{border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none;}
    table{border-collapse: collapse !important;}
    table th{text-align: right}
    body{height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important;}
    /* iOS BLUE LINKS */
    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: none !important;
        font-size: inherit !important;
        font-family: inherit !important;
        font-weight: inherit !important;
        line-height: inherit !important;
    }
    /* MOBILE STYLES */
    @media screen and (max-width: 525px) {
        /* ALLOWS FOR FLUID TABLES */
        .wrapper {
            width: 100% !important;
            max-width: 100% !important;
        }
        /* ADJUSTS LAYOUT OF LOGO IMAGE */
        .logo img {
            margin: 0 auto !important;
        }
        /* USE THESE CLASSES TO HIDE CONTENT ON MOBILE */
        .mobile-hide {
            display: none !important;
        }
        .img-max {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
        }
        /* FULL-WIDTH TABLES */
        .responsive-table {
            width: 100% !important;
        }
        /* UTILITY CLASSES FOR ADJUSTING PADDING ON MOBILE */
        .padding {
            padding: 10px 5% 15px 5% !important;
        }
        .padding-meta {
            padding: 30px 5% 0px 5% !important;
            text-align: center;
        }
        .no-padding {
            padding: 0 !important;
        }
        .section-padding {
            padding: 50px 15px 50px 15px !important;
        }
        /* ADJUST BUTTONS ON MOBILE */
        .mobile-button-container {
            margin: 0 auto;
            width: 100% !important;
        }
        .mobile-button {
            padding: 15px !important;
            border: 0 !important;
            font-size: 16px !important;
            display: block !important;
        }
    }
    /* ANDROID CENTER FIX */
    div[style*="margin: 16px 0;"] {margin: 0 !important;}
</style>
</head>
<body style="margin: 0 !important; padding: 0 !important;">
<!-- HEADER -->
<table border="0" cellpadding="0" cellspacing="0" width="100%" align="center" style="border:1px solid #CCC;margin-top:30px;">
    <tr>
        <td bgcolor="#FFF" align="center">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="750">
            <tr>
            <td align="center" valign="top" width="750">
            <![endif]-->
            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="wrapper">
                <tr>
                    <td align="center" valign="top" style="padding: 15px 0;" class="logo">
                        <h1>
                            <a href="<?php echo site_url() ?>" target="_blank">
                                <img src="<?php echo base_url('dist/images/rcp-completo.png') ?>" width="350" alt="Radar Concursos Públicos">
                            </a>
                        </h1>
                    </td>
                </tr>
            </table>
            <!--[if (gte mso 9)|(IE)]>
            </td>
            </tr>
            </table>
            <![endif]-->
        </td>
    </tr>
    <tr>
        <td bgcolor="#ffffff" align="center" style="padding: 0px 15px 50px 15px;" class="section-padding">
            <!--[if (gte mso 9)|(IE)]>
            <table align="center" border="0" cellspacing="0" cellpadding="0" width="750">
            <tr>
            <td align="center" valign="top" width="750">
            <![endif]-->
            <table border="0" cellpadding="0" cellspacing="0" width="100%" class="responsive-table">
                <tr>
                    <td>
                        <!-- HERO IMAGE -->
                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                            <tr>
                                <td class="padding" align="center"></td>
                            </tr>
                            <tr>
                                <td align="center">
                                    <h2>Detalhe Concurso Diário da Républica</h2>
                                    <table>
                                        <tbody>
                                            <tr>
                                                <th>
                                                    <center>
                                                        <a style="font-size: 16px;" target="_blank" href="<?php echo $contest->link ?? '#' ?>">
                                                            <?php echo trim($contest->title); ?>
                                                        </a>
                                                    </center>
                                                </th>
                                            </tr>
                                            <br>
                                            <tr>
                                                <td><strong>Data do concurso: </strong><?php echo $contest->created_at ? date('d-m-Y', strtotime($contest->created_at)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Promotor: </strong><?php echo trim($contest->promoter) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Nome do Projeto: </strong><br><?php echo trim($contest->name) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Descrição do Projeto: </strong><br><?php echo trim($contest->description) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Referência: </strong><?php echo trim($contest->ref) ?></td>
                                            </tr>
                                            <?php if (!empty($contest->link)): ?>
                                                <tr>
                                                    <td><strong>Concurso:</strong> <a target="_blank" href="<?php echo $contest->link ?>">Ligação para o concurso</a></td>
                                                </tr>
                                            <?php endif ?>
                                            <tr>
                                                <td><strong>Link para App:</strong>
                                                    <a href="<?php echo config('Radar')->dre . '/dre/detail/' . $contest->id; ?>" target="_blank">
                                                        Ir para a App
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>Preço Base: </strong><?php echo trim($contest->base_price) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Plataforma: </strong><?php echo trim($contest->platform) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Monofator Preço: </strong><?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->mono_price]) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Data limite solicitar esclarecimentos: </strong><?php echo $contest->date_clarify ? date('d-m-Y', strtotime($contest->date_clarify)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Hora limite solicitar esclarecimentos: </strong><?php echo $contest->hour_clarify ? date('H:i', strtotime($contest->hour_clarify)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Esclarecimentos solicitados: </strong><?php echo trim($contest->clarifications) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Data limite apresentação da candidatura: </strong><?php echo $contest->date_candidacy ? date('d-m-Y', strtotime($contest->date_candidacy)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Hora limite apresentação da candidatura: </strong><?php echo $contest->hour_candidacy ? date('H:i', strtotime($contest->hour_candidacy)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Data limite apresentação da proposta: </strong><?php echo $contest->date_proposal ? date('d-m-Y', strtotime($contest->date_proposal)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Hora limite apresentação da proposta: </strong><?php echo $contest->hour_proposal ? date('H:i', strtotime($contest->hour_proposal)) : '' ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Decisão de avançar: </strong><?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->decision]) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Empresa concorrente: </strong><?php echo trim($contest->candidate) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Empresa que vai avançar: </strong><?php echo implode(', ', array_column($contest->companies, 'name')) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Agrupamento: </strong><?php echo view_cell('\DreApp\Cells\ContestCell::decision', ['decision' => $contest->grouping]) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Estado: </strong><?php echo view_cell('\DreApp\Cells\ContestCell::status', ['status' => $contest->status]) ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Observações: </strong><br><?php echo trim($contest->obs) ?></td>
                                            </tr>
                                            <?php if (!empty($attachments)): ?>
                                            <tr>
                                                <td><strong>Anexos: </strong>
                                                    <a href="<?php echo config('Radar')->dre . '/dre/attachments/' . $contest->id ?>" target="_blank">Ver</a><br>
                                                </td>
                                            </tr>
                                            <?php endif?>
                                            <tr>
                                                <td>
                                                    <a style="background-color: #0192bd; color: #fff; text-decoration: none; text-transform: uppercase; padding: 6px;" href="<?php echo config('Radar')->dre . '/dre/interest/' . $contest->id ?>">Remover interesse</a>
                                                </td>
                                            </tr>
                                            <br>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>
<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center" style="max-width: 500px;" class="responsive-table">
    <tr>
        <td align="center" style="font-size: 12px; line-height: 18px; font-family: Helvetica, Arial, sans-serif; color:#666666;">
            <br><br>Copyright © <?php echo date('Y') ?> <a target="_blank" style="color: #0192bd;" href="https://chconsulting.pt">CH Consulting</a>. Todos os direitos reservados <br> Powered By "Afonso Robot"
            <br>
        </td>
    </tr>
</table>
</body>
</html>
