document.addEventListener("DOMContentLoaded", function() {
    // forearch programs and show segmentation selected
    var nodes = document.querySelectorAll('.radio-segmentation');
    for (var i = 0; i < nodes.length; i++) {
        var type = nodes[i].dataset.type;
        var programId = nodes[i].dataset.programid;
        if(nodes[i].dataset.cim == 1) {
            type = 'cims';
        }

        if (!type.length) {
            nodes[i].querySelector('.sel-districts').classList.remove('d-none');
        } else {
            nodes[i].querySelector('.sel-' + type).classList.remove('d-none');
        }
    }
});

// show segment selected when related input is on
function showSegments(programId, type) {
    document.querySelectorAll('[data-programid="' + programId + '"] select').forEach((node) => {
        if (node.classList.contains('sel-' + type)) {
            node.classList.remove('d-none');
        } else {
            node.classList.add('d-none');
        }
    })
}