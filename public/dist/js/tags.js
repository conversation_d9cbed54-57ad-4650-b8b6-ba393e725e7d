const ul = document.querySelector('.tags-ul'),
    input = document.querySelector('#input-tags');
let maxTags = 10, tags = [];

/*
 * Prepare tags when page is loaded
 */
window.addEventListener('DOMContentLoaded', event => {
    let defaultTags = document.querySelector('input[name=tags]').value;
    if (defaultTags.length) {
        tags = defaultTags.split(',');
    }

    createTag();
});

/*
 * Create tag
 */
function createTag(){
    ul.querySelectorAll('li').forEach(li => li.remove());
    tags.slice().reverse().forEach(tag =>{
        let liTag = `<li>${tag} <i class="fa-duotone fa-xmark" onclick="remove(this, '${tag}')"></i></li>`;
        ul.insertAdjacentHTML("afterbegin", liTag);
    });
}

/*
 * Remove tag
 */
function remove(element, tag){
    let index  = tags.indexOf(tag);
    tags = [...tags.slice(0, index), ...tags.slice(index + 1)];
    element.parentElement.remove();
    document.querySelector('input[name=tags]').setAttribute('value', tags.toString());
}

/*
 * Add tag after enter
 */
function addTag(e){
    if (e.key == 'Enter'){
        let tag = e.target.value.replace(/\s+/g, ' ');
        if(tag.length > 1 && !tags.includes(tag) && tags.length < 10){
            tag.split(',').forEach(tag => {
                tags.push(tag);
                createTag();
            });
        }
        e.target.value = "";
        document.querySelector('input[name=tags]').setAttribute('value', tags.toString());
    }
}

input.addEventListener('keyup', addTag);

/*
 * Prevent form submit after enter
 */
document.getElementById('articles-form').addEventListener('keypress', function(e) {
    if (e.keyCode === 13) {
        e.preventDefault();
    }
});