.navbar-100, .sidebar-title {
	height: 100px;
}

.navbar {
	padding: 30px !important;
}

.content-wrapper > .content {
	padding: 30px 10px;
}

.sidebar .nav-link {
	border-radius: 0;
	border-bottom: 1px solid #D9D9D9;
}

.navbar-expand .navbar-nav .nav-link {
	padding: 0;
}

.main-header .nav-link {
    height: auto; 
}

.main-sidebar {
    background-color: #fff;
}

.fa-bars {
	color: #0433BF;
	font-size: 20px;
}

.ml-30 {
	margin-left: 30px;
}

.mt-33{margin-top: 33px;}

.navbar-nav {
	align-items: center;
}

/* ----- ARTICLE TAGS ----- */

.tags-ul {
    display: flex;
    flex-wrap: wrap;
    padding: 7px;
    border-radius: 5px;
    border:1px solid #ced4da;
}
.tags-ul li {
    color: #333;
    margin: 4px 3px;
    list-style: none;
    border-radius: 5px;
    background: #F2F2F2;
    padding: 5px 8px 5px 10px;
    border: 1px solid #e3e1e1;
}
.tags-ul li i{
    color: #808080;
    margin-left: 8px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 50%;
    justify-content: center;
}
.tags-ul input{
    flex: 1;
    padding: 5px;
    border: none;
    outline: none;
    font-size: 16px;
}

/* ----- DASHBOARD ----- */

.cards-counter {
    background: transparent;
    border: 1px solid #212529;
    color: #212529;
}

.cards-bg-goto {
    background: #212529 !important;
}

/* ----- COMPANIES ----- */

.img-company {
    position: relative !important;
}

.img-company, .delete-img-company {
    border: 1px solid #ccc;
    border-radius: 3px;
}

.img-company img {
    width: -webkit-fill-available;
}

.delete-img-company {
    position: absolute;
    padding: 5px;
    background: #fff;
    bottom: 5px;
    right: 5px;
}


body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .content-wrapper, body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-footer, body:not(.sidebar-mini-md):not(.sidebar-mini-xs):not(.layout-top-nav) .main-header{
    margin-left: 0;
}

.main-header.navbar .nav-item{
    padding-left: 10px;
    padding-right: 10px;
}