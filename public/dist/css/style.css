/*
* 1. imported styles
* 2. miscelanious styles
* 3. login styles

*/

/**** IMPORT *******************************************************************/

/*@import url('fontawesome-all.css');*/
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');

/**** Reset *******************************************************************/

* {
    margin: 0px;
    padding: 0px;
    border: none;
    outline: none;
    font-size: 100%;
    line-height: inherit;
    border-radius: 0 !important;
}

/**** miscelanious *******************************************************************/

:root {
  --blue: #0433BF;
  --yellow: #FFF079;
  --gray: #7A7A7A;
  --dark-gray: #3C3C3C;
  --light-gray: #B1B1B1;
}

body {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
}

.container {
    padding-top: 30px;
}

.pl-0 { padding-left: 0px; }
.pr-30 { padding-right: 30px; }
.pt-30 { padding-right: 30px; }
.mt-50 { margin-top: 50px; }
.mt-35 { margin-top: 35px; }
.mt-30 { margin-top: 30px; }
.mb-35 { margin-bottom: 35px; }


h1 {
    color: #3C3C3C;
    font-size: 36px;
    font-weight: 400;
    line-height: 40px;
}

h2 {
    color: var(--blue);
    font-size: 30px;
    font-weight: 400;
    line-height: 34px;
}

h3 {
    color: var(--blue);
    font-size: 24px;
    font-weight: 400;
    line-height: 34px;
}

h5 {
    color: var(--Cinza-Mdio, var(--cinza, #7A7A7A));
    font-size: 16px;
    font-weight: 400;
    line-height: 20px;
}

.w700 {
    font-weight: 700;
}

/*btns*/
.btn {
    width: 100%
}
.btn.btn-primary {
    background-color: var(--blue);
    border: 1px solid var(--blue);
    color: #FFFFFF;
}
.btn.btn-secundary {
    background-color: var(--yellow);
    border: 1px solid var(--yellow);
    color: var(--blue);
}

.btn.btn-default {
    background-color: #FFFFFF;
    border: 1px solid #FFFFFF;
    color: var(--blue);
    border: 1px solid #D9D9D9;
}

.btn:hover {
    background-color: #3C3C3C;
    border: 1px solid #3C3C3C;
    color: #FFFFFF !important;
}
.btn-alert .btn {
    position: fixed;
    transform: rotate(-90deg);
    right:-75px;
    top:250px;
    left: auto;
    width: 200px;
    height: 70px;
}

.btn i {
    margin-right: 11px;
}

.btn i.no-margin {
    margin-right: 0px;
}

.form-control,
.form-select {
    border: 1px solid #D9D9D9;
    color: var(--dark-gray);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
}

.form-control:focus,
.form-select:focus {
    color: #000000;
    background-color: #fff;
    border-color:  var(--blue);
    outline: 0;
    box-shadow: none !important;
}

.form-select {
      color: var(--gray) !important;
    }
.form-select.placeholder, option.placeholder {
      color: var(--gray) !important;
    }

.border-right {
    border-right: 1px solid #D9D9D9;
    padding-right:10px;
    margin-right:10px;
}

a, a.nav-link  i{
    color: var(--dark-gray);
    text-decoration: none;
}
a:hover,  a.nav-link:hover i {
    color: var(--blue);
}

.bg-blue {
    background-color:  var(--blue);
}

.color-blue {
    color:  var(--blue);
}

.translate-middle {
    transform: translate(-130%,15%)!important;
}

.input-group i {
    color: var(--gray) !important;
}

/**** 3. login styles *********************************************************/

#login {
    background-color: var(--blue);
}

.center-screen {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  min-height: 100vh;
  padding: 40px 35px;
}

.form-signin {
    max-width: 465px;
    background-color: #FFFFFF !important;
}

.form-signin p {
    font-size: 18px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: center;
    color: var(--dark-gray);
}

.form-signin label.form-control {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
    color: var(--dark-gray);
}

.form-signin ::-webkit-input-placeholder { /* Chrome/Opera/Safari */
    color: var(--light-gray) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
}
.form-signin ::-moz-placeholder { /* Firefox 19+ */
    color: var(--light-gray) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
}
.form-signin :-ms-input-placeholder { /* IE 10+ */
    color: var(--light-gray) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
}
.form-signin :-moz-placeholder { /* Firefox 18- */
    color: var(--light-gray) !important;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0em;
    text-align: left;
}
.form-signin .input-group-text {
    background-color: #FFFFFF;
    border-left: none !important;
}

#login .input-group input {
    border-right: none !important;
}

#login .btn.btn-primary,
#login .btn.btn-secundary {
    height: 60px;
    width: 100%;
    margin-bottom:15px;
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    box-shadow: 0px 4px 20px 0px #00000040;
}

#login a, #login label {
    color: var(--dark-gray);
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    text-decoration: none;
}

#login .nav { display: inline-flex}
#login .nav-link {
    background-color: #FFFFFF;
    color: var(--dark-gray);
}
#login  i { font-size: 12px; }
#login .nav-link.active i { color: var(--blue); }
#login .nav-link.active i:before { border-radius: 50px; background: var(--blue); }
.mw-500 {
    max-width: 500px;
}

/* APP STYLES*/

    #wrapper {
      overflow-x: hidden;
    }

    .bg-gray, main, footer, footer col {
        background-color: #F9F9F9 !important;
    }

    /* NAV */
    nav {
        background-color: #FFFFFF;
        height: 100px;
        padding: 0 15px 0 50px !important;
        color: #3C3C3C;
    }

    nav i {
          color: #3C3C3C;
    }

    main {
        padding: 50px;
    }

    footer, footer a {
        color: #B1B1B1;
    }

    footer {
        padding: 50px;
    }



    /* SIDEBAR **************************************************************/
    #sidebar-wrapper {
      min-height: 100vh;
      margin-left: -15rem;
      transition: margin 0.25s ease-out;
      box-shadow: 0px 10px 40px 0px rgba(0, 0, 0, 0.05);
      z-index: 100;
    }

    #sidebar-wrapper .sidebar-heading {
      padding: 0.875rem 1.25rem;
      font-size: 1.2rem;
    }

    #page-content-wrapper {
      min-width: 100vw;
    }

    .list-group-item-light.list-group-item-action.active {
        color: var(--blue);
        background-color: transparent;
        border-color: rgba(0, 0, 0, 0.125);
    }

    body.sb-sidenav-toggled #wrapper #sidebar-wrapper {
      margin-left: 0;
    }

    @media (min-width: 768px) {
      #sidebar-wrapper {
        margin-left: 0;
      }
      #page-content-wrapper {
        min-width: 0;
        width: 100%;
      }
      .sidebar-heading {
        text-align: center;
      }

        #wrapper #sidebar-wrapper .list-group {
            padding: 20px
        }
      #wrapper #sidebar-wrapper  .text {
        margin-left: 25px;
        text-align: center;
      }

      body.sb-sidenav-toggled #wrapper #sidebar-wrapper {
        margin-left: 0;
        width: 120px;
        text-align: center;
      }
      body.sb-sidenav-toggled #wrapper #sidebar-wrapper  .text{
        display: none
      }
    }

    .list-group-item-light.list-group-item-action:focus, .list-group-item-light.list-group-item-action:hover {
        color: var(--blue);
        background-color: transparent;
    }

    .card {
        box-shadow: 0px 4px 40px 0px rgba(0, 0, 0, 0.10);
        padding: 25px;
        border:none;
        min-height: 247px;

    }

    .card .card-title {
        color: var(--blue);
        font-family: Open Sans;
        font-size: 30px
        font-weight: 400;
        line-height: 34px; /* 113.333% */
        margin-bottom: 50px;
    }

    input[type="date"]::-webkit-inner-spin-button,
    input[type="date"]::-webkit-calendar-picker-indicator {
        display: none;
        -webkit-appearance: none;
    }

    .input-group-text {
         background-color: #FFFFFF;
    }


    .number {
        color: var(--Cinza-Escuro, var(--CINZA, #3C3C3C));
        font-size: 64px;
        font-weight: 300;
        line-height: 74px; /* 115.625% */
    }

    .number-text {
        color: var(--Cinza-Mdio, var(--cinza, #7A7A7A));
        font-size: 24px;
        font-weight: 400;
        line-height: 34px; /* 141.667% */
    }

    /* NOTICES LIST */
    .filters ul {
        margin: 0;
        padding: 0
    }

    .filters ul i {
        color: var(--Cinza-Mdio, var(--cinza, #7A7A7A));
        font-size: 24px;
        font-weight: 300;
        line-height: 24px; /* 100% */
    }

    .filters ul i.active {
        color: var(--blue);
    }

    .filters ul li {
        display: inline;
        padding: 0 20px;
        border-right: 1px solid #D9D9D9;
    }

     .filters-add {
        margin-top: 30px;
     }
     .filters-add a {
        color:  var(--blue);
        font-size: 12px;
        font-weight: 400;
        padding: 5px 10px;
        border: 1px solid  var(--blue);
        border-radius: 5px !important;
        margin-right: 15px;
    }

    .filters-add a i.fa-xmark-large {
        margin-left: 15px;
    }

    .notice-item h5 {
        color: var(--blue);
        margin: 0;
    }
    .notice-item .icon{
        height: 97.524px;
        background: var(--cinza-linha, #F5F5F5);
        text-align: center;
        padding-top: 25px;
    }

    .notice-item .icon i{
        color: var(--blue);
        text-align: center;
        font-size: 50px;
        font-weight: 100;
        line-height: 50px; /* 100% */
    }

    .notice-item .bookmark a,
    .notice-item .bookmark.active a:hover {
        color: var(--Cinza-Linha-Form, #D9D9D9);
        font-size: 24px;
        font-weight: 300;
        line-height: 24px;
    }
    .notice-item .bookmark.active a,
    .notice-item .bookmark a:hover {
        color: var(--blue);
    }

    .notice-item a {
        color: var(--blue);
        font-size: 14px;
        font-weight: 700;
        line-height: 24px;
    }

    .notice-item .date {
        color: #000;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
    }

    /** DETAIL NOTICE */
    .notice .categories i {
        color: var(--blue);
        text-align: center;
        font-size: 40px;
        font-weight: 100;
        line-height: 40px;
    }

    ..notice categories .title {
        color: var(--Cinza-Escuro, var(--CINZA, #3C3C3C));
    }
    .notice .categories .description {
        color: var(--Cinza-Mdio, var(--cinza, #7A7A7A));
    }

    .notice .more {
        color: var(--cinza-form-texto, var(--cinza-form, #B1B1B1));
        font-size: 20px;
        font-weight: 300;
        line-height: 22px;
    }

    .notice .back {
        color: var(--Cinza-Linha-Form, #D9D9D9);
        font-size: 30px;
        font-weight: 300;
        line-height: 22px;
    }

    .modal label,
    #filters label {
        color: #7A7A7A;
    }

    #filters .dropdown-menu.show {
        display: inline-grid;
        width: 100%;
        left: -37px !important;
        top: -1px !important;
        font-size: 14px;
        padding: 5px 0;
    }

    #filters .dropdown-menu.show li input {
        margin-left: -8px;
        margin-right: 10px;
    }

    #filters .btn {
        max-width: 50px;
    }

    #filters .btn i {
        margin-right: 0px
    }

    #filters .qs-datepicker-container {
        top: 39px !important;
        left: 1px !important;
        width: 100% !important;
        font-size: 14px;
    }

    #filters .qs-controls {
        background-color: rgba(5, 51,191, 0.1);
        color: #000000 !important;
    }

    #filters .qs-day {
        font-weight: 700;
        color: #000000;
    }

    #filters .qs-square:not(.qs-empty):not(.qs-disabled):not(.qs-day):not(.qs-active):hover {
        background: var(--blue);
        color: #FFFFFF;
    }

    /* ARTICLES **************************************************************/
    .article-card-img {
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat !important;
    }
    .h-300 {
        height: 300px;
    }
    .h-150 {
        height: 150px;
    }
    .tags-ul {
        display: flex;
        flex-wrap: wrap;
        padding-left: 0;
        border-radius: 5px;
        border:1px solid #ced4da;
    }
    .tags-ul li {
        color: #333;
        margin: 4px 3px;
        list-style: none;
        border-radius: 5px;
        background: #F2F2F2;
        padding: 5px 8px 5px 10px;
        border: 1px solid #e3e1e1;
    }
    .tags-ul li i{
        color: #808080;
        margin-left: 8px;
        font-size: 14px;
        cursor: pointer;
        border-radius: 50%;
        justify-content: center;
    }
    .tags-ul input{
        flex: 1;
        padding: 5px;
        border: none;
        outline: none;
        font-size: 16px;
    }
    .div-pagination nav {
        background-color: transparent;
    }

    .pagination li a {
        padding: 8px;
        border-radius: 4px !important;
    }
    .pagination li.active a {
        background: var(--blue);
        color: #fff !important;
    }
    .height-50 {
        height: 50px;
    }
    .fs-25 {
        font-size: 25px;
    }
    .article-detail-description p span, .article-detail-description p strong {
        font-size: 18px !important;
    }
    /* CHARTS **************************************************************/
    .highcharts-credits {
        display: none !important;
    }
    .div-legend {
        width: 325px;
        color: var(--gray);
    }

    .legend-count {
        color: var(--dark-gray);float: right;
    }
    .clear-filter {
        display: block;
        text-align: center;
        margin: 0 auto;
        padding-top: 10px;
    }
    #regionalProgramsGraph {
        min-height:450px;
    }
    .navbar-toggler-icon {
        display: none;
    }
    #sidebarToggle i {
        font-size: 30px !important;
        color: var(--blue) !important;
    }
    #sidebarToggle {
        background-color: transparent !important;
    }
    .sidebar-heading {
        height: 100px;
        align-items: center;
        display: flex;
        justify-content: flex-start;
        margin-left: 20px;
    }
    .list-group-item-light i {
        font-size: 20px;
    }
    #wrapper #sidebar-wrapper .text, .navbar-nav i {
        font-size: 16px;
    }

    /* COMPANIES **************************************************************/
    .img-company {
        position: relative !important;
    }

    .img-company, .delete-img-company {
        border: 1px solid #ccc;
        border-radius: 3px;
    }

    .img-company img {
        width: 100% !important;
    }

    .delete-img-company {
        position: absolute;
        padding: 5px;
        background: #fff;
        bottom: 5px;
        right: 5px;
    }

    /* MOBILE **************************************************************/
    @media only screen and (min-width: 600px) {
        .d-none-desktop {
            display: none !important;
        }
    }
    @media only screen and (max-width: 600px) {
        main {
            max-width: 100% !important;
            padding: 20px !important;
        }
        .col-number-notices {
            margin-bottom: 10px;
            text-align: center !important;
        }
        .mobile-d-none {
            display: none !important;
        }
        .mobile-direction-column {
            flex-direction: column !important;
        }
        .mobile-filter-btns {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            gap: 10px;
        }
        .mobile-w-100 {
            max-width: -webkit-fill-available !important;
            width: 100% !important;
        }
        .mobile-mt-20 {
            margin-top: 20px !important;
        }
        .mobile-text-center {
            text-align: center;
        }
        .mobile-border-footer {
            border-top: 1px solid #ccc;
        }
        .mobile-fs-20 {
            font-size: 20px;
        }
        .mobile-mb-0 {
            margin-bottom: 0 !important;
        }
        .notice .categories i {
            font-size: 30px;
        }
        .navbar {
            padding: 20px !important;
        }
        .navbar-nav {
            flex-direction: row !important;
            margin-left: 0 !important;
            gap: 20px;
        }
        .navbar-nav i {
            font-size: 18px;
        }
        #sidebar-wrapper {
            position: absolute !important;
            margin-left: 0 !important;
        }
        #sidebar-wrapper .list-group-flush {
            background: #fff;
        }
        #sidebar-wrapper .sidebar-heading {
            display: none !important;
        }
        #sidebar-wrapper .border-right {
            padding-right: 25px;
        }
        .closeSidebar {
            text-align: right !important;
        }
        .closeSidebar i {
            font-size: 20px;
            font-weight: bold;
        }
        #wrapper #sidebar-wrapper .text, .navbar-nav i {
            font-size: 16px;
        }
        #burgerIcon i, #closeSidebar i {
            font-size: 20px !important;
            color: var(--blue) !important;
        }
        .form-header img {
            width: -webkit-fill-available !important;
        }
    }