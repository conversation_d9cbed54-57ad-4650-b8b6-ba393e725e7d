<?php

namespace Basegov\Cells;

class EntityCell
{
    /**
     * show status badge
     * @param  array $params the current status
     * @return the   badge
     */
    public function status(array $params): string
    {
        if ($params['status'] === 'active') {
            return '<span class="badge badge-success">Ativo</span>';
        }

        return '<span class="badge badge-danger">Inativo</span>';
    }

}
