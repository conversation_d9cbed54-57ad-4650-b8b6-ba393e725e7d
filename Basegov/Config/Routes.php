<?php

// DASHBOARD
$routes->get('/', 'Basegov::index', ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov', 'filter' => 'Basegov\Filters\LoginRedirect::LoginRedirect']);

$routes->get('/release-log', 'Dashboard::releaseLog', ['namespace' => 'Admin\Controllers', 'subdomain' => 'basegov', 'filter' => 'group:admin']);
$routes->get('logs', 'Dashboard::logs', ['namespace' => 'Admin\Controllers', 'subdomain' => 'basegov', 'filter' => 'group:admin']);

$routes->get('announcement-public-detail/(:num)', 'Basegov::announcementDetail/$1',
    ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov']
);

$routes->get('basegov/announcement-public-detail/(:num)', 'Basegov::announcementDetail/$1',
    ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov']
);
$routes->group('basegov', ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov', 'filter' => 'group:admin'], static function ($routes) {
    $routes->get('/', 'Basegov::index');
    $routes->get('detail/(:num)', 'Basegov::detail/$1');
    $routes->get('announcements', 'Basegov::announcements');
    $routes->get('announcement-detail/(:num)', 'Basegov::announcementDetail/$1');
    $routes->get('export', 'Basegov::export');
});

// SETTINGS
$routes->group('settings', ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov', 'filter' => 'group:admin'], static function ($routes) {
    // CRUD LISTS
    $routes->group('entity-lists', static function ($routes) {
        $routes->get('', 'Lists::index');
        $routes->get('insert', 'Lists::insert');
        $routes->get('update/(:num)', 'Lists::update/$1');
        $routes->post('save', 'Lists::save');
        $routes->get('delete/(:num)', 'Lists::delete/$1');
        $routes->get('import/(:num)', 'Lists::import/$1');
        $routes->post('import', 'Lists::importCsv');
    });
    // CRUD ENTITIES
    $routes->group('entities', static function ($routes) {
        $routes->get('', 'Entities::index');
        $routes->get('insert', 'Entities::insert');
        $routes->get('update/(:num)', 'Entities::update/$1');
        $routes->post('save', 'Entities::save');
        $routes->get('delete/(:num)', 'Entities::delete/$1');
    });
    // CRUD CPVs
    $routes->group('cpvs', static function ($routes) {
        $routes->get('', 'Cpvs::index');
        $routes->get('insert', 'Cpvs::insert');
        $routes->get('update/(:num)', 'Cpvs::update/$1');
        $routes->post('save', 'Cpvs::save');
        $routes->get('delete/(:num)', 'Cpvs::delete/$1');
    });
    // CRUD CIMS
    $routes->group('cims', ['namespace' => 'Admin\Controllers'], static function ($routes) {
        $routes->get('', 'Cims::index');
        $routes->get('insert', 'Cims::insert');
        $routes->get('update/(:num)', 'Cims::update/$1');
        $routes->post('save', 'Cims::save');
        $routes->get('delete/(:num)', 'Cims::delete/$1');
    });
    // CRUD CITY HALL (Municípios)
    $routes->group('city-halls', ['namespace' => 'Admin\Controllers'], static function ($routes) {
        $routes->get('', 'CityHalls::index');
        $routes->get('insert', 'CityHalls::insert');
        $routes->get('update/(:num)', 'CityHalls::update/$1');
        $routes->post('save', 'CityHalls::save');
        $routes->get('delete/(:num)', 'CityHalls::delete/$1');
    });
    // CRUD PARISH (Juntas de frequesia)
    $routes->group('parish', ['namespace' => 'Admin\Controllers'], static function ($routes) {
        $routes->get('', 'Parish::index');
        $routes->get('insert', 'Parish::insert');
        $routes->get('update/(:num)', 'Parish::update/$1');
        $routes->post('save', 'Parish::save');
        $routes->get('delete/(:num)', 'Parish::delete/$1');
    });
    // CRUD ALERTS ANNOUNCEMENTS
    $routes->group('alerts', static function ($routes) {
        $routes->get('', 'Alerts::index');
        $routes->get('insert', 'Alerts::insert');
        $routes->get('update/(:num)', 'Alerts::update/$1');
        $routes->post('save', 'Alerts::save');
        $routes->get('delete/(:num)', 'Alerts::delete/$1');
    });
    // CRUD ALERTS CONTRACTS
    $routes->group('alerts-contracts', static function ($routes) {
        $routes->get('', 'ContractsAlerts::index');
        $routes->get('insert', 'ContractsAlerts::insert');
        $routes->get('update/(:num)', 'ContractsAlerts::update/$1');
        $routes->post('save', 'ContractsAlerts::save');
        $routes->get('delete/(:num)', 'ContractsAlerts::delete/$1');
    });
});

// AUTHENTICATION
$routes->group('auth', ['namespace' => 'Basegov\Controllers', 'subdomain' => 'basegov'], static function ($routes) {
    $routes->get('login', 'Auth::loginView');
    $routes->post('login', 'Auth::loginAction');
    $routes->get('forgot-password', 'Auth::forgotPasswordView');
    $routes->post('forgot-password', 'Auth::forgotPasswordAction');
    $routes->get('reset-password', 'Auth::resetPasswordView', ['filter' => \App\Filters\CheckPasswordToken::class]);
    $routes->post('reset-password', 'Auth::resetPasswordAction');
    $routes->get('logout', static function () {
        auth()->logout();
        return redirect()->to('auth/login')->with('confirm', 'Terminou a sessão com sucesso');
    });
});
