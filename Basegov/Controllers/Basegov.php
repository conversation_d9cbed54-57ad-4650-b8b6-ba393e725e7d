<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Basegov extends BaseController
{
    /**
     * Init controller
     * @param \CodeIgniter\HTTP\RequestInterface  $request
     * @param \CodeIgniter\HTTP\ResponseInterface $response
     * @param \Psr\Log\LoggerInterface            $logger
     */
    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'basegov';
        $this->data['activeSubMenu'] = 'contracts';
        $this->data['breadcrumbs']->addCrumb('Base.gov.pt', site_url('basegov'));
    }

    /**
     * Show the list of Contracts
     * @return string
     */
    public function index(): string
    {
        $this->data['entityTypes'] = [];
        $this->data['contracts'] = [
            'data' => model('\Basegov\Models\BaseGovModel')
                ->select('base_gov.*')
                ->filter($this->request->getGet())
                ->orderBy('publicationDate', 'DESC')
                ->paginate(40),
            'pagination' => model('\Basegov\Models\BaseGovModel')->pager,
        ];
        $this->data['contractedEntities'] = model('\Basegov\Models\BaseGovModel')
            ->select('DISTINCT TRIM(contracted) AS contracted')
            ->filter($this->request->getGet())
            ->where('contracted !=', '')->orderBy('contracted', 'ASC')
            ->findAll();
        $this->data['contractingProcedureTypes'] = model('\Basegov\Models\BaseGovModel')->distinct()
            ->select('contractingProcedureType')->orderBy('contractingProcedureType')
            ->filter($this->request->getGet())->where('contractingProcedureType !=', '')
            ->findAll();
        $this->data['cpvsDesignations'] = model('\Basegov\Models\BaseGovModel')->distinct()
            ->select('cpvsDesignation')->where('cpvsDesignation !=', '')
            ->filter($this->request->getGet())->orderBy('cpvsDesignation')
            ->findAll();
        if (in_array($this->request->getGet('entity_type'), ['CIM', 'parish', 'city_hall'])) {
            $selects = [
                'CIM' => 'geo_cims.vat AS entity_type_vat, geo_cims.name AS entity_type_name',
                'city_hall' => 'geo_city_halls.vat AS entity_type_vat, geo_city_halls.name AS entity_type_name',
                'parish' => 'geo_parish_councel.vat AS entity_type_vat, geo_parish_councel.name AS entity_type_name',
            ];
            $this->data['entityTypes'] = model('\Basegov\Models\BaseGovModel')
                ->distinct()->select($selects[$this->request->getGet('entity_type')])
                ->filter($this->request->getGet())
                ->orderBy('entity_type_name', 'ASC')
                ->findAll();
        }
        $this->data['breadcrumbs']->addCrumb('Contratos', site_url('basegov'));

        return view('basegov/basegov/index', $this->data);
    }

    /**
     * Show the detail of a given contract
     * @param  int      $id The selected contract
     * @return string
     */
    public function detail(int $id): string
    {
        $this->data['contract'] = model('\Basegov\Models\BaseGovModel')->find($id);
        if (empty($this->data['contract'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['contract']->documents = model('\Basegov\Models\BaseGovDocumentModel')
            ->where('base_gov_id', $id)->findAll();
        $this->data['contract']->contestants = model('\Basegov\Models\BaseGovContestantModel')
            ->where('base_gov_id', $id)->findAll();
        $this->data['contract']->invitees = model('\Basegov\Models\BaseGovInviteeModel')
            ->where('base_gov_id', $id)->findAll();

        $this->data['breadcrumbs']->addCrumb('Contratos', site_url('basegov'))
            ->addCrumb($this->data['contract']->objectBriefDescription ?? '', site_url('basegov/detail/' . $this->data['contract']->id));

        return view('basegov/basegov/detail', $this->data);
    }

    /**
     * Show announcements
     * @return string view
     */
    public function announcements(): string
    {
        $this->data['archive'] = 0;
        $filters['proposalDeadLine >='] = date('Y-m-d');
        if (isset($_GET['archive']) && $_GET['archive'] === '1') {
            $this->data['archive'] = 1;
        }
        $this->data['cpvs'] = model('\Basegov\Models\BaseGovAnnouncementModel')->distinct()->select('cpvs')
            ->where($filters)->orderBy('cpvs', 'ASC')->findAll();
        $this->data['types'] = model('\Basegov\Models\BaseGovAnnouncementModel')->distinct()->select('type')
            ->where($filters)->orderBy('type', 'ASC')->findAll();
        $this->data['contractingProcedureTypes'] = model('\Basegov\Models\BaseGovAnnouncementModel')->distinct()
            ->select('contractingProcedureType')->where($filters)->orderBy('type', 'ASC')->findAll();
        $this->data['alerts'] = model('\Basegov\Models\BaseGovAlertModel')->orderBy('tags', 'ASC')->findAll();
        $this->data['entities'] = model('\Basegov\Models\BaseGovAnnouncementModel')
            ->entities(service('request')->getGet())
            ->findAll();
        $this->data['announcements'] = [
            'data' => model('\Basegov\Models\BaseGovAnnouncementModel')
                ->select('base_gov_announcements.*')
                ->filter(service('request')->getGet())
                ->paginate(50),
            'pagination' => model('\Basegov\Models\BaseGovAnnouncementModel')->pager,
        ];
        $this->data['breadcrumbs']->addCrumb('Anúncios', site_url('basegov/announcements'));
        $this->data['activeSubMenu'] = 'announcements';

        return view('basegov/basegov/announcements', $this->data);
    }

    /**
     * Show detailed information about the Announcement
     * @param  int      $id The selected announcement
     * @return string
     */
    public function announcementDetail(int $id): string
    {
        $this->data['layout'] = 'layouts/admin';
        $this->data['announcement'] = model('\Basegov\Models\BaseGovAnnouncementModel')->where('id', $id)->first();
        if (empty($this->data['announcement'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['activeSubMenu'] = 'announcements';
        $this->data['announcement']->entities = model('\Basegov\Models\BasegovAnnouncementsContractingEntitiesModel')
            ->where('announcement_id', $id)->findAll();
        $this->data['breadcrumbs']->addCrumb('Anúncios', site_url('basegov/announcements'))
            ->addCrumb(
                $this->data['announcement']->contractDesignation,
                site_url('basegov/announcement-detail/' . $this->data['announcement']->id)
            );
        if (!isset(auth()->user()->id)) {
            $this->data['layout'] = 'layouts/basegov-announcements';
        }
        return view('basegov/basegov/announcement-detail', $this->data);
    }

    /**
     * Export data from a query done
     * @return void
     */
    public function export(): void
    {
        $contracts = model('\Basegov\Models\BaseGovModel')->filter($this->request->getGet())
            ->orderBy('publicationDate', 'DESC')->findAll();
        $spreadsheet = new Spreadsheet();
        $activeWorksheet = $spreadsheet->getActiveSheet();
        $fields = config('BaseGov')->export;

        foreach ($fields as $field) {
            $activeWorksheet->setCellValue($field['local'] . '1', $field['name']); // id
        }
        foreach ($contracts as $key => $contract) {
            foreach ($fields as $field) {
                $activeWorksheet->setCellValue($field['local'] . $key + 2, $contract->{$field['field']}); // id
            }
        }
        $writer = new Xlsx($spreadsheet);
        $fileName = 'exportbasegov' . date('YmdHis');
        $writer->save($fileName . '.xlsx');
        ob_get_clean();
        $this->response->setHeader('Content-type', 'application/vnd.ms-excel')
            ->setHeader('Content-Disposition', 'attachment')
            ->setHeader('filename', $fileName . '.xlsx');
        $writer->save('php://output');
    }

}
