<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;

class <PERSON>erts extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'alerts';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('Alertas', site_url('settings/alerts'))
            ->addCrumb('Anúncios', site_url('settings/alerts'));
    }

    /**
     * Show the list of Alerts
     * @return string
     */
    public function index(): string
    {
        $this->data['alerts'] = model('\Basegov\Models\BaseGovAlertModel')
            ->where('type', 'announcements')
            ->orderBy('created_at', 'DESC')
            ->findAll();
        foreach ($this->data['alerts'] as $key => $alert) {
            $this->data['alerts'][$key]->cpvs = array_column(
                model('\Basegov\Models\BaseGovAlertCpvModel')->asArray()->where('alert_id', $alert->id)
                    ->join('cpvs', 'base_gov_alerts_cpvs.cpv = cpvs.cpv')->findAll(),
                'description'
            );
        }

        return view('basegov/alerts/index', $this->data);
    }

    /**
     * Insert New alert
     * @return string
     */
    public function insert(): string
    {
        $this->data['cpvs'] = model('\App\Models\CpvModel')->orderBy('description', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/alerts/insert'));

        return view('basegov/alerts/manage', $this->data);
    }

    /**
     * Save Alert
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!$id = model('\Basegov\Models\BaseGovAlertModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\Basegov\Models\BaseGovAlertModel')->errors())->withInput();
        }
        $id = (!empty($_POST['id'])) ? $this->request->getPost('id') : model('\Basegov\Models\BaseGovAlertModel')->getInsertID();
        model('\Basegov\Models\BaseGovAlertCpvModel')->where('alert_id', $id)->delete();
        if (isset($_POST['cpvs']) && !empty($_POST['cpvs'])) {
            foreach ($this->request->getPost('cpvs') as $cpv) {
                model('\Basegov\Models\BaseGovAlertCpvModel')->insert([
                    'alert_id' => $id, 'cpv' => $cpv,
                ]);
            }
        }

        return redirect()->to('settings/alerts')->with('message', 'O Alerta foi gravado com sucesso!');
    }

    /**
     * Update Alert view
     * @param  int      $id The selected CPV
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['alert'] = model('\Basegov\Models\BaseGovAlertModel')->where('id', $id)->first();
        if (empty($this->data['alert'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['cpvs'] = model('\App\Models\CpvModel')->orderBy('cpv', 'ASC')->findAll();
        $this->data['alert']->cpvs = array_column(
            model('\Basegov\Models\BaseGovAlertCpvModel')->asArray()->where('alert_id', $id)->findAll(),
            'cpv'
        );
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/alerts/update' . $id))
            ->addCrumb(word_limiter($this->data['alert']->tags, 7), site_url('settings/alerts/update/' . $id));

        return view('basegov/alerts/manage', $this->data);
    }

    /**
     * Delete Alert
     * @param  int    $id        The selected Alert
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovAlertModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Alerta');
        }

        return redirect()->back()->with('confirm', 'O Alerta foi apagado com sucesso.');
    }
}
