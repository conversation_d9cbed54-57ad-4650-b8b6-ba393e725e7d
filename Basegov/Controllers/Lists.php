<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;

class Lists extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'lists';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('Listas de Entidades', site_url('settings/entity-lists'));
    }

    /**
     * Show the list of Entities
     * @return string
     */
    public function index(): string
    {
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')->orderBy('name', 'ASC')->findAll();

        return view('basegov/lists/index', $this->data);
    }

    /**
     * Insert list of Entities
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/entity-lists/insert'));

        return view('basegov/lists/manage', $this->data);
    }

    /**
     * Save list of Entities
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovEntityListModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\BaseGovEntityListModel')->errors())->withInput();
        }

        return redirect()->to('settings/entity-lists')->with('message', 'A listagem de entidades foi gravada com sucesso!');
    }

    /**
     * Update list of Entities view
     * @param  int      $id The selected list of Entities
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['list'] = model('\Basegov\Models\BaseGovEntityListModel')->where('id', $id)->first();
        if (empty($this->data['list'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/entity-lists/update' . $id))
            ->addCrumb($this->data['list']->name, site_url('settings/entity-lists/update/' . $id));

        return view('basegov/lists/manage', $this->data);
    }

    /**
     * Delete list of Entities
     * @param  int    $id        The selected list of Entities
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovEntityListModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a Listagem de entidades');
        }

        return redirect()->back()->with('confirm', 'A listagem de entidades foi apagada com sucesso.');
    }

    /**
     * Show the import form
     * @param  int    $id The selected list
     * @return string The form
     */
    public function import(int $id): string
    {
        $this->data['selectedList'] = model('\Basegov\Models\BaseGovEntityListModel')->where('id', $id)->first();
        if (empty($this->data['selectedList'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')->orderBy('name', 'ASC')->findAll();

        return view('basegov/lists/import', $this->data);
    }

    /**
     * Import List of NIFS into a selected list
     * in case this is a import we just use the nif as name too, since what matters is the nif
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function importCsv(): \CodeIgniter\HTTP\RedirectResponse
    {
        $file = $this->request->getFile('file');
        $filepath = $file->store();
        $handle = fopen(WRITEPATH . 'uploads/' . $filepath, 'r');
        if (!$handle) {
            return redirect()->back()->with('errors', ['Houve um problema ao ler o seu ficheiro']);
        }
        $entities = [];
        while (($data = fgetcsv($handle, 1000, ',')) !== false) {
            $num = count($data);
            for ($c = 0; $c < $num; $c++) {
                $entities[] = $data[$c];
            }
        }
        fclose($handle);
        foreach ($entities as $vat) {
            model('\Basegov\Models\BaseGovEntityModel')->insert([
                'list_id' => $this->request->getPost('list_id'),
                'vat' => $vat,
                'name' => $vat,
            ]);
        }

        return redirect()->to('settings/entity-lists')->with('confirm', 'A sua lista foi gravada com sucesso.');
    }

}
