<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;

class Cpvs extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'cpvs';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('CPVs', site_url('settings/cpvs'));
    }

    /**
     * Show the list of CPVs
     * @return string
     */
    public function index(): string
    {
        $this->data['cpvs'] = model('\App\Models\CpvModel')->orderBy('cpv', 'ASC')->findAll();

        return view('basegov/cpvs/index', $this->data);
    }

    /**
     * Insert cpvs view
     * @return string
     */
    public function insert(): string
    {
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/cpvs/insert'));

        return view('basegov/cpvs/manage', $this->data);
    }

    /**
     * Save CPV
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CpvModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\CpvModel')->errors())->withInput();
        }

        return redirect()->to('settings/cpvs')->with('message', 'O CPV foi gravado com sucesso!');
    }

    /**
     * Update CPV view
     * @param  int      $id The selected CPV
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['cpv'] = model('\App\Models\CpvModel')->where('id', $id)->first();
        if (empty($this->data['cpv'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/cpvs/update' . $id))
            ->addCrumb($this->data['cpv']->cpv, site_url('settings/cpvs/update/' . $id));

        return view('basegov/cpvs/manage', $this->data);
    }

    /**
     * Delete CPV
     * @param  int    $id        The selected CPV
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\App\Models\CpvModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o CPV');
        }

        return redirect()->back()->with('confirm', 'O CPV foi apagado com sucesso.');
    }
}
