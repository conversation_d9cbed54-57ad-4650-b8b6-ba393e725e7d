<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;

class Entities extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'entities';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/entities'))
            ->addCrumb('Entidades', site_url('settings/entities'));
    }

    /**
     * Show the Entities
     * @return string
     */
    public function index(): string
    {
        $this->data['entities'] = model('\Basegov\Models\BaseGovEntityModel')
            ->select('base_gov_entities.*, base_gov_entity_lists.name AS listName')
            ->join('base_gov_entity_lists', 'base_gov_entities.list_id = base_gov_entity_lists.id')
            ->orderBy('name', 'ASC')->findAll();

        return view('basegov/entities/index', $this->data);
    }

    /**
     * Insert Entity
     * @return string
     */
    public function insert(): string
    {
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')
            ->where('status', 'active')
            ->orderBy('name', 'ASC')
            ->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/entities/insert'));

        return view('basegov/entities/manage', $this->data);
    }

    /**
     * Save Entity
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovEntityModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\App\Models\BaseGovEntityModel')->errors())->withInput();
        }

        return redirect()->to('settings/entities')->with('message', 'A Entidade foi gravada com sucesso!');
    }

    /**
     * Update Entity view
     * @param  int      $id The selected Entity
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['entity'] = model('\Basegov\Models\BaseGovEntityModel')->where('id', $id)->first();
        if (empty($this->data['entity'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')
            ->where('status', 'active')
            ->orderBy('name', 'ASC')
            ->findAll();
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/entities/update' . $id))
            ->addCrumb($this->data['entity']->name, site_url('settings/entities/update/' . $id));

        return view('basegov/entities/manage', $this->data);
    }

    /**
     * Delete Entity
     * @param  int    $id        The selected list of Entities
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovEntityModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar a entidade');
        }

        return redirect()->back()->with('confirm', 'A entidade foi apagada com sucesso.');
    }
}
