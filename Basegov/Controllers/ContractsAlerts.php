<?php

namespace Basegov\Controllers;

use Basegov\Controllers\BaseController;

class ContractsAlerts extends BaseController
{

    public function initController(
        \CodeIgniter\HTTP\RequestInterface $request,
        \CodeIgniter\HTTP\ResponseInterface $response,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::initController($request, $response, $logger);
        $this->data['activeMenu'] = 'settings';
        $this->data['activeSubMenu'] = 'alerts-contracts';
        $this->data['breadcrumbs']->addCrumb('Definições', site_url('settings/cims'))
            ->addCrumb('Alertas', site_url('settings/alerts-contracts'))
            ->addCrumb('Contratos', site_url('settings/alerts-contracts'));
    }

    /**
     * Show the list of Alerts
     * @return string
     */
    public function index(): string
    {
        $this->data['alerts'] = model('\Basegov\Models\BaseGovAlertModel')
            ->select('base_gov_alerts.*, base_gov_entity_lists.name AS listName')
            ->join('base_gov_entity_lists', 'base_gov_alerts.list_id = base_gov_entity_lists.id')
            ->where('type', 'contracts')
            ->orderBy('created_at', 'DESC')
            ->findAll();

        return view('basegov/alerts-contracts/index', $this->data);
    }

    /**
     * Insert New alert
     * @return string
     */
    public function insert(): string
    {
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Inserir', site_url('settings/alerts-contracts/insert'));

        return view('basegov/alerts-contracts/manage', $this->data);
    }

    /**
     * Save Alert
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save(): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!$id = model('\Basegov\Models\BaseGovAlertModel')->save($this->request->getPost())) {
            return redirect()->back()->with('errors', model('\Basegov\Models\BaseGovAlertModel')->errors())->withInput();
        }

        return redirect()->to('settings/alerts-contracts')->with('message', 'O Alerta foi gravado com sucesso!');
    }

    /**
     * Update Alert view
     * @param  int      $id The selected CPV
     * @return string
     */
    public function update(int $id): string
    {
        $this->data['alert'] = model('\Basegov\Models\BaseGovAlertModel')->where('id', $id)->first();
        if (empty($this->data['alert'])) {
            throw new \CodeIgniter\Exceptions\PageNotFoundException();
        }
        $this->data['lists'] = model('\Basegov\Models\BaseGovEntityListModel')->orderBy('name', 'ASC')->findAll();
        $this->data['breadcrumbs']->addCrumb('Editar', site_url('settings/alerts/update' . $id))
            ->addCrumb(word_limiter($this->data['alert']->tags, 7), site_url('settings/alerts/update/' . $id));

        return view('basegov/alerts-contracts/manage', $this->data);
    }

    /**
     * Delete Alert
     * @param  int    $id        The selected Alert
     * @return object redirect
     */
    public function delete(int $id): \CodeIgniter\HTTP\RedirectResponse
    {
        if (!model('\Basegov\Models\BaseGovAlertModel')->delete($id)) {
            return redirect()->back()->with('error', 'Houve um problema ao apagar o Alerta');
        }

        return redirect()->back()->with('confirm', 'O Alerta foi apagado com sucesso.');
    }
}
