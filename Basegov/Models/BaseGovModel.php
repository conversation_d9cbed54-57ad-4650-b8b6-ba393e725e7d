<?php

namespace Basegov\Models;

use CodeIgniter\Model;

class BaseGovModel extends Model
{
    protected $table = 'base_gov';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'id', 'vat', 'contractingProcedureType', 'publicationDate', 'ccp', 'contracting', 'contracted',
        'initialContractualPrice', 'objectBriefDescription', 'signingDate', 'contractFundamentationType',
        'increments', 'closeDate', 'causesDeadlineChange', 'causesPriceChange', 'frameworkAgreementProcedureId',
        'frameworkAgreementProcedureDescription', 'directAwardFundamentationType', 'ambientCriteria', 'observations',
        'contractingProcedureUrl', 'endOfContractType', 'totalEffectivePrice', 'announcementId',
        'centralizedProcedure', 'executionDeadline', 'contractTypeCS', 'executionPlace', 'cpvs',
        'nonWrittenContractJustificationTypes', 'contractStatus', 'materialCriteria', 'contractTypes', 'income',
        'aquisitionStateMemberUE', 'infoAquisitionStateMemberUE', 'groupMembers', 'specialMeasures', 'regime',
        'cpvsType', 'cpvsDesignation', 'cocontratantes', 'cpvsValue', 'normal', 'description', 'status',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'id' => 'required',
        'vat' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Set of filters that can be used
     * @param  array          $filters The group of filters
     * @return BaseGovModel
     */
    public function filter(array $filters): BaseGovModel
    {
        $filters = array_filter($filters);
        if (isset($filters['from']) && !empty($filters['from'])) {
            $this->where('publicationDate >=', $filters['from']);
        }
        if (isset($filters['to']) && !empty($filters['to'])) {
            $this->where('publicationDate <=', $filters['to']);
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'CIM') {
            $this->join('geo_cims', 'base_gov.vat = geo_cims.vat');
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'city_hall') {
            $this->join('geo_city_halls', 'base_gov.vat = geo_city_halls.vat');
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'parish') {
            $this->join('geo_parish_councel', 'base_gov.vat = geo_parish_councel.vat');
        }
        if (isset($filters['entity'])) {
            $this->where('base_gov.vat', $filters['entity']);
        }
        if (isset($filters['search']) && !empty($filters['search'])) {
            $terms = explode(' ', $filters['search']);
            foreach ($terms as $term) {
                $this->groupStart();
                $this->orLike('objectBriefDescription', $term);
                $this->orLike('regime', $term);
                $this->orLike('description', $term);
                $this->orLike('causesDeadlineChange', $term);
                $this->orLike('causesPriceChange', $term);
                $this->groupEnd();
            }
        }

        unset($filters['page'], $filters['from'], $filters['to'], $filters['entity_type'], $filters['entity'], $filters['search']);
        foreach ($filters as $field => $value) {
            $this->where($field, $value);
        }

        return $this;
    }
}
