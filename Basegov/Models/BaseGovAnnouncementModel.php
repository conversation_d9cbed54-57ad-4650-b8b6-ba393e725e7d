<?php

namespace Basegov\Models;

use CodeIgniter\Model;

class BaseGovAnnouncementModel extends Model
{
    protected $table = 'base_gov_announcements';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'id', 'type', 'proposalDeadline', 'contractingEntity', 'basePrice', 'contractDesignation', 'drPublicationDate',
        'contractingProcedureType', 'dreSeries', 'modelType', 'cnccs', 'contractingProcedureId', 'contractsCount',
        'announcementNumber', 'ambientCriteria', 'contractingProcedureUrl', 'cpvs', 'contractType',
        'contractingProcedureAliasID', 'impugnations', 'dreNumber', 'maximumEstimatedValueUnderFrameworkAgreement',
        'reference', 'reference', 'status', 'doc', 'award_criteria',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Filters
     * @param  array  $filters Set of base filters
     * @return this
     */
    public function filter(array $filters, bool $entity = true): BaseGovAnnouncementModel
    {
        if (isset($filters['archive']) && $filters['archive'] === '1') {
            $filters['proposalDeadLine <'] = date('Y-m-d');
        } else {
            $filters['proposalDeadLine >='] = date('Y-m-d');
        }
        if (isset($filters['drPublicationDateStart'])) {
            $filters['drPublicationDate >='] = $filters['drPublicationDateStart'];
        }
        if (isset($filters['drPublicationDateEnd'])) {
            $filters['drPublicationDate <='] = $filters['drPublicationDateEnd'];
        }
        if (isset($filters['search']) && !empty($filters['search'])) {
            $terms = explode(' ', $filters['search']);
            foreach ($terms as $term) {
                $this->groupStart();
                $this->orLike('contractDesignation', $term);
                $this->orLike('contractingEntity', $term);
                $this->orLike('award_criteria', $term);
                $this->groupEnd();
            }
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'city_hall') {
            $this->where('base_gov_announcements.id IN(
                SELECT announcement_id
                FROM base_gov_announcements_contracting_entities
                JOIN geo_city_halls ON base_gov_announcements_contracting_entities.nif = geo_city_halls.vat
                )', null, false);
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'CIM') {
            $this->where('base_gov_announcements.id IN(
                SELECT announcement_id
                FROM base_gov_announcements_contracting_entities
                JOIN geo_cims ON base_gov_announcements_contracting_entities.nif = geo_cims.vat
                )', null, false);
        }
        if (isset($filters['entity_type']) && $filters['entity_type'] === 'parish') {
            $this->where('base_gov_announcements.id IN(
                SELECT announcement_id
                FROM base_gov_announcements_contracting_entities
                JOIN geo_parish_councel ON base_gov_announcements_contracting_entities.nif = geo_parish_councel.vat
                )', null, false);
        }
        if (isset($filters['entity']) && !empty($filters['entity']) && $entity) {
            $this->join('base_gov_announcements_contracting_entities', 'base_gov_announcements.id = base_gov_announcements_contracting_entities.announcement_id'
            );
            $this->where('nif', $filters['entity']);
        }
        // Alert filter we need to get the previously made tags and selected cpvs
        if (isset($filters['alert_id'])) {
            $conn = \Config\Database::connect();
            $alertTable = $conn->table('base_gov_alerts');
            $alertCpvTable = $conn->table('base_gov_alerts_cpvs');
            $alert = $alertTable->where('id', $filters['alert_id'])->get()->getRow();
            $tags = array_filter(explode(',', $alert->tags ?? ''));
            if (!empty($tags)) {
                $this->groupStart();
                foreach ($tags as $tag) {
                    if (empty($tag)) {
                        continue;
                    }
                    $this->orLike('contractDesignation', trim($tag));
                }
                $this->groupEnd();
            }
            $cpvs = array_column($alertCpvTable->where('alert_id', $filters['alert_id'])->get()->getResultArray(), 'cpv');
            if (!empty($cpvs)) {
                $this->groupStart();
                foreach ($cpvs as $cpv) {
                    $this->orLike('cpvs', $cpv);
                }
                $this->groupEnd();
            }
        }
        unset(
            $filters['drPublicationDateEnd'], $filters['drPublicationDateStart'], $filters['alert_id'], $filters['archive'],
            $filters['entity_type'], $filters['page'], $filters['entity'], $filters['search']
        );
        $filters = array_filter(array_merge($filters, ['cpvs' => service('request')->getGet('cpvs')]));
        $this->where($filters);

        return $this;
    }

    /**
     * Join entities to get just the ones that apply to the current search
     * @param  array  $filters The current set of filters
     * @return this
     */
    public function entities(array $filters): BaseGovAnnouncementModel
    {
        $this->filter($filters, false);
        $this->distinct()
            ->select('base_gov_announcements_contracting_entities.nif,
                base_gov_announcements_contracting_entities.description'
            )->join('
                base_gov_announcements_contracting_entities',
            'base_gov_announcements.id = base_gov_announcements_contracting_entities.announcement_id'
        );

        return $this;
    }

}
