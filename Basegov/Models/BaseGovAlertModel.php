<?php

namespace Basegov\Models;

use CodeIgniter\Model;

class BaseGovAlertModel extends Model
{
    protected $table = 'base_gov_alerts';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'object';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'list_id', 'tags', 'emails', 'status', 'last_check', 'main', 'type',
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'emails' => 'required',
    ];
    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['setStartDate', 'eraseComma'];
    protected $afterInsert = [];
    protected $beforeUpdate = ['eraseComma'];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Add new tags to the main alert
     */
    public function addToMain(): bool
    {
        $newTags = '';
        $existingTags = array_column($this->select('tags')
                ->where('type', 'announcements')
                ->asArray(true)
                ->findAll(),
            'tags'
        );
        foreach ($existingTags as $tags) {
            $newTags .= $tags . ',';
        }
        $newTagsArray = array_unique(array_filter(array_map('trim', explode(',', $newTags))));
        $mainTags = implode(', ', $newTagsArray);

        return $this->where('main', 1)->skipValidation(true)->set(['tags' => $mainTags])->update();
    }

    /**
     * Set default alert time for yesterday so we make sure it will run on the next call
     * @param array $alert
     */
    protected function setStartDate(array $alert): array
    {
        $yesterday = date('Y-m-d H:i:s', strtotime('yesterday'));
        $alert['data']['last_check'] = $yesterday;

        return $alert;
    }

    /**
     * Erases comma, semicolon, dots, hifen, dash from the end of the last email
     * @param  array   $data
     * @return array
     */
    protected function eraseComma(array $data): array
    {
        if (!isset($data['data']['emails'])) {
            return $data;
        }
        $data['data']['emails'] = rtrim($data['data']['emails'], ",;.:_-?!*+~^");

        return $data;
    }

}
