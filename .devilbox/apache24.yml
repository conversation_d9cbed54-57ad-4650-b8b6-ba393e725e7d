---

# Apache 2.4 vHost Template defintion for vhost-gen.py
#
# The 'feature' section contains optional features that can be enabled via
# conf.yml and will then be replaced into the main vhost ('structure' section)
# into their corresponding position:
#
#    __XDOMAIN_REQ__
#    __PHP_FPM__
#    __ALIASES__
#    __DENIES__
#    __STATUS__
#
# The features itself also contain variables to be adjusted in conf.yml
# and will then be replaced in their corresponding feature section
# before being replaced into the vhost section (if enabled):
#
# PHP-FPM:
#    __PHP_ADDR__
#    __PHP_PORT__
# XDomain:
#    __REGEX__
# Alias:
#    __REGEX__
#    __PATH__
# Deny:
#    __REGEX__
# Status:
#    __REGEX__
#
# Variables to be replaced directly in the vhost configuration can also be set
# in conf.yml and include:
#    __VHOST_NAME__
#    __DOCUMENT_ROOT__
#    __INDEX__
#    __ACCESS_LOG__
#    __ERROR_LOG__
#    __PHP_ADDR__
#    __PHP_PORT__
#


###
### Basic vHost skeleton
###
vhost: |
  <VirtualHost __DEFAULT_VHOST__:__PORT__>
      ServerName __VHOST_NAME__
      ServerAlias  admin.__VHOST_NAME__
      ServerAlias  api.__VHOST_NAME__
      ServerAlias  app.__VHOST_NAME__
      ServerAlias  basegov.__VHOST_NAME__
      ServerAlias  dre.__VHOST_NAME__
      Protocols  __HTTP_PROTO__

      CustomLog  "__ACCESS_LOG__" combined
      ErrorLog   "__ERROR_LOG__"

  __REDIRECT__
  __SSL__
  __VHOST_DOCROOT__
  __VHOST_RPROXY__
  __PHP_FPM__
  __ALIASES__
  __DENIES__
  __SERVER_STATUS__
      # Custom directives
  __CUSTOM__
  </VirtualHost>

###
### vHost Type (normal or reverse proxy)
###
vhost_type:
  # Normal vHost (-p)
  docroot: |
    # Define the vhost to serve files
    DocumentRoot "__DOCUMENT_ROOT__"
    <Directory "__DOCUMENT_ROOT__">
        DirectoryIndex __INDEX__

        AllowOverride All
        Options All

        RewriteEngine on
        RewriteBase /

        Order allow,deny
        Allow from all
        Require all granted
    </Directory>

  # Reverse Proxy (-r)
  rproxy: |
    # Define the vhost to reverse proxy
    ProxyRequests On
    ProxyPreserveHost On
    ProxyPass __LOCATION__ __PROXY_PROTO__://__PROXY_ADDR__:__PROXY_PORT____LOCATION__
    ProxyPassReverse __LOCATION__ __PROXY_PROTO__://__PROXY_ADDR__:__PROXY_PORT____LOCATION__


###
### Optional features to be enabled in vHost
###
features:

  # SSL Configuration
  ssl: |
    SSLEngine on
    SSLCertificateFile    "__SSL_PATH_CRT__"
    SSLCertificateKeyFile "__SSL_PATH_KEY__"
    SSLProtocol           __SSL_PROTOCOLS__
    SSLHonorCipherOrder   __SSL_HONOR_CIPHER_ORDER__
    SSLCipherSuite        __SSL_CIPHERS__

  # Redirect to SSL directive
  redirect: |
    RedirectMatch (.*) https://__VHOST_NAME__:__SSL_PORT__$1

  # PHP-FPM will not be applied to a reverse proxy!
  php_fpm: |
    # In case for PHP-FPM 5.2 compatibility use 'GENERIC' instead of 'FPM'
    # https://httpd.apache.org/docs/2.4/mod/mod_proxy_fcgi.html#proxyfcgibackendtype
    ProxyFCGIBackendType FPM

    # PHP-FPM Definition
    <FilesMatch \.php$>
        Require all granted
        SetHandler proxy:fcgi://__PHP_ADDR__:__PHP_PORT__
    </FilesMatch>

    <Proxy "fcgi://__PHP_ADDR__:__PHP_PORT__/">
        ProxySet timeout=__PHP_TIMEOUT__
        ProxySet connectiontimeout=__PHP_TIMEOUT__
    </Proxy>

    # If the php file doesn't exist, disable the proxy handler.
    # This will allow .htaccess rewrite rules to work and
    # the client will see the default 404 page of Apache
    RewriteCond %{REQUEST_FILENAME} \.php$
    RewriteCond %{DOCUMENT_ROOT}/%{REQUEST_URI} !-f
    RewriteRule (.*) - [H=text/html]

  alias: |
    # Alias Definition
    Alias "__ALIAS__" "__PATH____ALIAS__"
    <Location "__ALIAS__">
    __XDOMAIN_REQ__
    </Location>
    <Directory "__PATH____ALIAS__">
        Order allow,deny
        Allow from all
        Require all granted
    </Directory>

  deny: |
    # Deny Definition
    <FilesMatch "__REGEX__">
        Order allow,deny
        Deny from all
    </FilesMatch>

  server_status: |
    # Status Page
    <Location __REGEX__>
        SetHandler server-status
        Order allow,deny
        Allow from all
        Require all granted
    </Location>

  xdomain_request: |
    # Allow cross domain request from these hosts
    SetEnvIf Origin "__REGEX__" AccessControlAllowOrigin=$0
    Header add Access-Control-Allow-Origin %{AccessControlAllowOrigin}e env=AccessControlAllowOrigin
    Header always set Access-Control-Allow-Methods "POST, GET, OPTIONS, DELETE, PUT"
    Header always set Access-Control-Max-Age "0"
    Header always set Access-Control-Allow-Headers "x-requested-with, Content-Type, origin, authorization, accept, client-security-token"
    # Added a rewrite to respond with a 200 SUCCESS on every OPTIONS request.
    RewriteEngine On
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
